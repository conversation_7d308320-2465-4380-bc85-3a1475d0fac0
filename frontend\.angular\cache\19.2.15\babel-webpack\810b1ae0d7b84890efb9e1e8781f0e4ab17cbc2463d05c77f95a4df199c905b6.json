{"ast": null, "code": "import { FocusMonitor, _Id<PERSON>enerator, FocusKeyManager } from '@angular/cdk/a11y';\nimport { ENTER, SPACE, BACKSPACE, DELETE, TAB, hasModifierKey, UP_ARROW, DOWN_ARROW } from '@angular/cdk/keycodes';\nimport { _CdkPrivateStyleLoader, _VisuallyHiddenLoader } from '@angular/cdk/private';\nimport { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, ElementRef, booleanAttribute, numberAttribute, Directive, Input, ChangeDetectorRef, NgZone, EventEmitter, Injector, ANIMATION_MODULE_TYPE, Component, ViewEncapsulation, ChangeDetectionStrategy, ContentChildren, Output, ContentChild, ViewChild, afterNextRender, QueryList, forwardRef, NgModule } from '@angular/core';\nimport { Subject, merge } from 'rxjs';\nimport { _ as _StructuralStylesLoader } from './structural-styles-BQUT6wsL.mjs';\nimport { a as MAT_RIPPLE_GLOBAL_OPTIONS } from './ripple-BT3tzh6F.mjs';\nimport { M as MatRippleLoader } from './ripple-loader-Ce3DAhPW.mjs';\nimport { takeUntil, startWith, switchMap } from 'rxjs/operators';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { NG_VALUE_ACCESSOR, NgControl, Validators, NgForm, FormGroupDirective } from '@angular/forms';\nimport { E as ErrorStateMatcher } from './error-options-Dm2JJUbF.mjs';\nimport { _ as _ErrorStateTracker } from './error-state-Dtb1IHM-.mjs';\nimport { k as MatFormFieldControl, h as MAT_FORM_FIELD } from './form-field-DqPi4knt.mjs';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport { M as MatRippleModule } from './index-SYVYjXwK.mjs';\nimport '@angular/cdk/platform';\nimport '@angular/cdk/coercion';\nimport '@angular/cdk/observers/private';\n\n/** Injection token to be used to override the default options for the chips module. */\nconst _c0 = [\"*\", [[\"mat-chip-avatar\"], [\"\", \"matChipAvatar\", \"\"]], [[\"mat-chip-trailing-icon\"], [\"\", \"matChipRemove\", \"\"], [\"\", \"matChipTrailingIcon\", \"\"]]];\nconst _c1 = [\"*\", \"mat-chip-avatar, [matChipAvatar]\", \"mat-chip-trailing-icon,[matChipRemove],[matChipTrailingIcon]\"];\nfunction MatChip_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 3);\n    i0.ɵɵprojection(1, 1);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MatChip_Conditional_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 6);\n    i0.ɵɵprojection(1, 2);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MatChipOption_Conditional_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 3);\n    i0.ɵɵprojection(1, 1);\n    i0.ɵɵelementStart(2, \"span\", 8);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(3, \"svg\", 9);\n    i0.ɵɵelement(4, \"path\", 10);\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction MatChipOption_Conditional_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 6);\n    i0.ɵɵprojection(1, 2);\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c2 = \".mdc-evolution-chip,.mdc-evolution-chip__cell,.mdc-evolution-chip__action{display:inline-flex;align-items:center}.mdc-evolution-chip{position:relative;max-width:100%}.mdc-evolution-chip__cell,.mdc-evolution-chip__action{height:100%}.mdc-evolution-chip__cell--primary{flex-basis:100%;overflow-x:hidden}.mdc-evolution-chip__cell--trailing{flex:1 0 auto}.mdc-evolution-chip__action{align-items:center;background:none;border:none;box-sizing:content-box;cursor:pointer;display:inline-flex;justify-content:center;outline:none;padding:0;text-decoration:none;color:inherit}.mdc-evolution-chip__action--presentational{cursor:auto}.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{pointer-events:none}@media(forced-colors: active){.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{forced-color-adjust:none}}.mdc-evolution-chip__action--primary{font:inherit;letter-spacing:inherit;white-space:inherit;overflow-x:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary::before{border-width:var(--mdc-chip-outline-width, 1px);border-radius:var(--mdc-chip-container-shape-radius, 8px);box-sizing:border-box;content:\\\"\\\";height:100%;left:0;position:absolute;pointer-events:none;top:0;width:100%;z-index:1;border-style:solid}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--primary::before{border-color:var(--mdc-chip-outline-color, var(--mat-sys-outline))}.mdc-evolution-chip__action--primary:not(.mdc-evolution-chip__action--presentational):not(.mdc-ripple-upgraded):focus::before{border-color:var(--mdc-chip-focus-outline-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--primary::before{border-color:var(--mdc-chip-disabled-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__action--primary::before{border-width:var(--mdc-chip-flat-selected-outline-width, 0)}.mat-mdc-basic-chip .mdc-evolution-chip__action--primary{font:inherit}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip__action--trailing{position:relative;overflow:visible}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--trailing{color:var(--mdc-chip-with-trailing-icon-trailing-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--trailing{color:var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip__text-label{-webkit-user-select:none;user-select:none;white-space:nowrap;text-overflow:ellipsis;overflow:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__text-label{font-family:var(--mdc-chip-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mdc-chip-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mdc-chip-label-text-size, var(--mat-sys-label-large-size));font-weight:var(--mdc-chip-label-text-weight, var(--mat-sys-label-large-weight));letter-spacing:var(--mdc-chip-label-text-tracking, var(--mat-sys-label-large-tracking))}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mdc-chip-label-text-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mdc-chip-selected-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label,.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label{color:var(--mdc-chip-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-evolution-chip__graphic{align-items:center;display:inline-flex;justify-content:center;overflow:hidden;pointer-events:none;position:relative;flex:1 0 auto}.mat-mdc-standard-chip .mdc-evolution-chip__graphic{width:var(--mdc-chip-with-avatar-avatar-size, 24px);height:var(--mdc-chip-with-avatar-avatar-size, 24px);font-size:var(--mdc-chip-with-avatar-avatar-size, 24px)}.mdc-evolution-chip--selecting .mdc-evolution-chip__graphic{transition:width 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selectable:not(.mdc-evolution-chip--selected):not(.mdc-evolution-chip--with-primary-icon) .mdc-evolution-chip__graphic{width:0}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mdc-evolution-chip__checkmark{position:absolute;opacity:0;top:50%;left:50%;height:20px;width:20px}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__checkmark{color:var(--mdc-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__checkmark{color:var(--mdc-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark{transition:transform 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1);transform:translate(-75%, -50%)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{transform:translate(-50%, -50%);opacity:1}.mdc-evolution-chip__checkmark-svg{display:block}.mdc-evolution-chip__checkmark-path{stroke-width:2px;stroke-dasharray:29.7833385;stroke-dashoffset:29.7833385;stroke:currentColor}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark-path{transition:stroke-dashoffset 150ms 45ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark-path{stroke-dashoffset:0}@media(forced-colors: active){.mdc-evolution-chip__checkmark-path{stroke:CanvasText !important}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--trailing{height:18px;width:18px;font-size:18px}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove{opacity:calc(var(--mat-chip-trailing-action-opacity, 1)*var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove:focus{opacity:calc(var(--mat-chip-trailing-action-focus-opacity, 1)*var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mat-mdc-standard-chip{border-radius:var(--mdc-chip-container-shape-radius, 8px);height:var(--mdc-chip-container-height, 32px)}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled){background-color:var(--mdc-chip-elevated-container-color, transparent)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{background-color:var(--mdc-chip-elevated-disabled-container-color)}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled){background-color:var(--mdc-chip-elevated-selected-container-color, var(--mat-sys-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled{background-color:var(--mdc-chip-flat-disabled-selected-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}@media(forced-colors: active){.mat-mdc-standard-chip{outline:solid 1px}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--primary{border-radius:var(--mdc-chip-with-avatar-avatar-shape-radius, 24px);width:var(--mdc-chip-with-icon-icon-size, 18px);height:var(--mdc-chip-with-icon-icon-size, 18px);font-size:var(--mdc-chip-with-icon-icon-size, 18px)}.mdc-evolution-chip--selected .mdc-evolution-chip__icon--primary{opacity:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__icon--primary{color:var(--mdc-chip-with-icon-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--primary{color:var(--mdc-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-highlighted{--mdc-chip-with-icon-icon-color:var(--mdc-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container));--mdc-chip-elevated-container-color:var(--mdc-chip-elevated-selected-container-color, var(--mat-sys-secondary-container));--mdc-chip-label-text-color:var(--mdc-chip-selected-label-text-color, var(--mat-sys-on-secondary-container));--mdc-chip-outline-width:var(--mdc-chip-flat-selected-outline-width, 0)}.mat-mdc-chip-focus-overlay{background:var(--mdc-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-selected .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-chip:hover .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-hover-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mdc-chip-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-focus-overlay .mat-mdc-chip-selected:hover,.mat-mdc-chip-highlighted:hover .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-hover-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mdc-chip-selected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mdc-chip-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected.cdk-focused .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mdc-chip-selected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-evolution-chip--disabled:not(.mdc-evolution-chip--selected) .mat-mdc-chip-avatar{opacity:var(--mdc-chip-with-avatar-disabled-avatar-opacity, 0.38)}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{opacity:var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38)}.mdc-evolution-chip--disabled.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{opacity:var(--mdc-chip-with-icon-disabled-icon-opacity, 0.38)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{opacity:var(--mat-chip-disabled-container-opacity, 1)}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-trailing-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-remove{opacity:var(--mat-chip-trailing-action-opacity, 1)}.mat-mdc-chip-remove:focus{opacity:var(--mat-chip-trailing-action-focus-opacity, 1)}.mat-mdc-chip-remove::after{background-color:var(--mat-chip-trailing-action-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-remove:hover::after{opacity:var(--mat-chip-trailing-action-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-remove:focus::after{opacity:var(--mat-chip-trailing-action-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected .mat-mdc-chip-remove::after,.mat-mdc-chip-highlighted .mat-mdc-chip-remove::after{background-color:var(--mat-chip-selected-trailing-action-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-standard-chip .mdc-evolution-chip__cell--primary,.mat-mdc-standard-chip .mdc-evolution-chip__action--primary,.mat-mdc-standard-chip .mat-mdc-chip-action-label{overflow:visible}.mat-mdc-standard-chip .mat-mdc-chip-graphic,.mat-mdc-standard-chip .mat-mdc-chip-trailing-icon{box-sizing:content-box}.mat-mdc-standard-chip._mat-animation-noopable,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__graphic,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark-path{transition-duration:1ms;animation-duration:1ms}.mat-mdc-chip-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;opacity:0;border-radius:inherit;transition:opacity 150ms linear}._mat-animation-noopable .mat-mdc-chip-focus-overlay{transition:none}.mat-mdc-basic-chip .mat-mdc-chip-focus-overlay{display:none}.mat-mdc-chip .mat-ripple.mat-mdc-chip-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-chip-avatar{text-align:center;line-height:1;color:var(--mdc-chip-with-icon-icon-color, currentColor)}.mat-mdc-chip{position:relative;z-index:0}.mat-mdc-chip-action-label{text-align:left;z-index:1}[dir=rtl] .mat-mdc-chip-action-label{text-align:right}.mat-mdc-chip.mdc-evolution-chip--with-trailing-action .mat-mdc-chip-action-label{position:relative}.mat-mdc-chip-action-label .mat-mdc-chip-primary-focus-indicator{position:absolute;top:0;right:0;bottom:0;left:0;pointer-events:none}.mat-mdc-chip-action-label .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-mdc-chip-remove::before{margin:calc(var(--mat-focus-indicator-border-width, 3px)*-1);left:8px;right:8px}.mat-mdc-chip-remove::after{content:\\\"\\\";display:block;opacity:0;position:absolute;top:-3px;bottom:-3px;left:5px;right:5px;border-radius:50%;box-sizing:border-box;padding:12px;margin:-12px;background-clip:content-box}.mat-mdc-chip-remove .mat-icon{width:18px;height:18px;font-size:18px;box-sizing:content-box}.mat-chip-edit-input{cursor:text;display:inline-block;color:inherit;outline:0}@media(forced-colors: active){.mat-mdc-chip-selected:not(.mat-mdc-chip-multiple){outline-width:3px}}.mat-mdc-chip-action:focus .mat-focus-indicator::before{content:\\\"\\\"}.mdc-evolution-chip__icon,.mat-mdc-chip-remove .mat-icon{min-height:fit-content}\\n\";\nconst _c3 = [[[\"mat-chip-avatar\"], [\"\", \"matChipAvatar\", \"\"]], [[\"\", \"matChipEditInput\", \"\"]], \"*\", [[\"mat-chip-trailing-icon\"], [\"\", \"matChipRemove\", \"\"], [\"\", \"matChipTrailingIcon\", \"\"]]];\nconst _c4 = [\"mat-chip-avatar, [matChipAvatar]\", \"[matChipEditInput]\", \"*\", \"mat-chip-trailing-icon,[matChipRemove],[matChipTrailingIcon]\"];\nfunction MatChipRow_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 0);\n  }\n}\nfunction MatChipRow_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 2);\n    i0.ɵɵprojection(1);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MatChipRow_Conditional_4_Conditional_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 1);\n  }\n}\nfunction MatChipRow_Conditional_4_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 7);\n  }\n}\nfunction MatChipRow_Conditional_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MatChipRow_Conditional_4_Conditional_0_Template, 1, 0)(1, MatChipRow_Conditional_4_Conditional_1_Template, 1, 0, \"span\", 7);\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵconditional(ctx_r0.contentEditInput ? 0 : 1);\n  }\n}\nfunction MatChipRow_Conditional_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵprojection(0, 2);\n  }\n}\nfunction MatChipRow_Conditional_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 5);\n    i0.ɵɵprojection(1, 3);\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c5 = [\"*\"];\nconst _c6 = \".mat-mdc-chip-set{display:flex}.mat-mdc-chip-set:focus{outline:none}.mat-mdc-chip-set .mdc-evolution-chip-set__chips{min-width:100%;margin-left:-8px;margin-right:0}.mat-mdc-chip-set .mdc-evolution-chip{margin:4px 0 4px 8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip-set__chips{margin-left:0;margin-right:-8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip{margin-left:0;margin-right:8px}.mdc-evolution-chip-set__chips{display:flex;flex-flow:wrap;min-width:0}.mat-mdc-chip-set-stacked{flex-direction:column;align-items:flex-start}.mat-mdc-chip-set-stacked .mat-mdc-chip{width:100%}.mat-mdc-chip-set-stacked .mdc-evolution-chip__graphic{flex-grow:0}.mat-mdc-chip-set-stacked .mdc-evolution-chip__action--primary{flex-basis:100%;justify-content:start}input.mat-mdc-chip-input{flex:1 0 150px;margin-left:8px}[dir=rtl] input.mat-mdc-chip-input{margin-left:0;margin-right:8px}\\n\";\nconst MAT_CHIPS_DEFAULT_OPTIONS = new InjectionToken('mat-chips-default-options', {\n  providedIn: 'root',\n  factory: () => ({\n    separatorKeyCodes: [ENTER]\n  })\n});\n/**\n * Injection token that can be used to reference instances of `MatChipAvatar`. It serves as\n * alternative token to the actual `MatChipAvatar` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_CHIP_AVATAR = new InjectionToken('MatChipAvatar');\n/**\n * Injection token that can be used to reference instances of `MatChipTrailingIcon`. It serves as\n * alternative token to the actual `MatChipTrailingIcon` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_CHIP_TRAILING_ICON = new InjectionToken('MatChipTrailingIcon');\n/**\n * Injection token that can be used to reference instances of `MatChipRemove`. It serves as\n * alternative token to the actual `MatChipRemove` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_CHIP_REMOVE = new InjectionToken('MatChipRemove');\n/**\n * Injection token used to avoid a circular dependency between the `MatChip` and `MatChipAction`.\n */\nconst MAT_CHIP = new InjectionToken('MatChip');\n\n/**\n * Section within a chip.\n * @docs-private\n */\nclass MatChipAction {\n  _elementRef = inject(ElementRef);\n  _parentChip = inject(MAT_CHIP);\n  /** Whether the action is interactive. */\n  isInteractive = true;\n  /** Whether this is the primary action in the chip. */\n  _isPrimary = true;\n  /** Whether the action is disabled. */\n  get disabled() {\n    return this._disabled || this._parentChip?.disabled || false;\n  }\n  set disabled(value) {\n    this._disabled = value;\n  }\n  _disabled = false;\n  /** Tab index of the action. */\n  tabIndex = -1;\n  /**\n   * Private API to allow focusing this chip when it is disabled.\n   */\n  _allowFocusWhenDisabled = false;\n  /**\n   * Determine the value of the disabled attribute for this chip action.\n   */\n  _getDisabledAttribute() {\n    // When this chip action is disabled and focusing disabled chips is not permitted, return empty\n    // string to indicate that disabled attribute should be included.\n    return this.disabled && !this._allowFocusWhenDisabled ? '' : null;\n  }\n  /**\n   * Determine the value of the tabindex attribute for this chip action.\n   */\n  _getTabindex() {\n    return this.disabled && !this._allowFocusWhenDisabled || !this.isInteractive ? null : this.tabIndex.toString();\n  }\n  constructor() {\n    inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n    if (this._elementRef.nativeElement.nodeName === 'BUTTON') {\n      this._elementRef.nativeElement.setAttribute('type', 'button');\n    }\n  }\n  focus() {\n    this._elementRef.nativeElement.focus();\n  }\n  _handleClick(event) {\n    if (!this.disabled && this.isInteractive && this._isPrimary) {\n      event.preventDefault();\n      this._parentChip._handlePrimaryActionInteraction();\n    }\n  }\n  _handleKeydown(event) {\n    if ((event.keyCode === ENTER || event.keyCode === SPACE) && !this.disabled && this.isInteractive && this._isPrimary && !this._parentChip._isEditing) {\n      event.preventDefault();\n      this._parentChip._handlePrimaryActionInteraction();\n    }\n  }\n  static ɵfac = function MatChipAction_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatChipAction)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatChipAction,\n    selectors: [[\"\", \"matChipAction\", \"\"]],\n    hostAttrs: [1, \"mdc-evolution-chip__action\", \"mat-mdc-chip-action\"],\n    hostVars: 9,\n    hostBindings: function MatChipAction_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function MatChipAction_click_HostBindingHandler($event) {\n          return ctx._handleClick($event);\n        })(\"keydown\", function MatChipAction_keydown_HostBindingHandler($event) {\n          return ctx._handleKeydown($event);\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵattribute(\"tabindex\", ctx._getTabindex())(\"disabled\", ctx._getDisabledAttribute())(\"aria-disabled\", ctx.disabled);\n        i0.ɵɵclassProp(\"mdc-evolution-chip__action--primary\", ctx._isPrimary)(\"mdc-evolution-chip__action--presentational\", !ctx.isInteractive)(\"mdc-evolution-chip__action--trailing\", !ctx._isPrimary);\n      }\n    },\n    inputs: {\n      isInteractive: \"isInteractive\",\n      disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n      tabIndex: [2, \"tabIndex\", \"tabIndex\", value => value == null ? -1 : numberAttribute(value)],\n      _allowFocusWhenDisabled: \"_allowFocusWhenDisabled\"\n    }\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatChipAction, [{\n    type: Directive,\n    args: [{\n      selector: '[matChipAction]',\n      host: {\n        'class': 'mdc-evolution-chip__action mat-mdc-chip-action',\n        '[class.mdc-evolution-chip__action--primary]': '_isPrimary',\n        '[class.mdc-evolution-chip__action--presentational]': '!isInteractive',\n        '[class.mdc-evolution-chip__action--trailing]': '!_isPrimary',\n        '[attr.tabindex]': '_getTabindex()',\n        '[attr.disabled]': '_getDisabledAttribute()',\n        '[attr.aria-disabled]': 'disabled',\n        '(click)': '_handleClick($event)',\n        '(keydown)': '_handleKeydown($event)'\n      }\n    }]\n  }], () => [], {\n    isInteractive: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    tabIndex: [{\n      type: Input,\n      args: [{\n        transform: value => value == null ? -1 : numberAttribute(value)\n      }]\n    }],\n    _allowFocusWhenDisabled: [{\n      type: Input\n    }]\n  });\n})();\n\n/** Avatar image within a chip. */\nclass MatChipAvatar {\n  static ɵfac = function MatChipAvatar_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatChipAvatar)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatChipAvatar,\n    selectors: [[\"mat-chip-avatar\"], [\"\", \"matChipAvatar\", \"\"]],\n    hostAttrs: [\"role\", \"img\", 1, \"mat-mdc-chip-avatar\", \"mdc-evolution-chip__icon\", \"mdc-evolution-chip__icon--primary\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MAT_CHIP_AVATAR,\n      useExisting: MatChipAvatar\n    }])]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatChipAvatar, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-chip-avatar, [matChipAvatar]',\n      host: {\n        'class': 'mat-mdc-chip-avatar mdc-evolution-chip__icon mdc-evolution-chip__icon--primary',\n        'role': 'img'\n      },\n      providers: [{\n        provide: MAT_CHIP_AVATAR,\n        useExisting: MatChipAvatar\n      }]\n    }]\n  }], null, null);\n})();\n/** Non-interactive trailing icon in a chip. */\nclass MatChipTrailingIcon extends MatChipAction {\n  /**\n   * MDC considers all trailing actions as a remove icon,\n   * but we support non-interactive trailing icons.\n   */\n  isInteractive = false;\n  _isPrimary = false;\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatChipTrailingIcon_BaseFactory;\n    return function MatChipTrailingIcon_Factory(__ngFactoryType__) {\n      return (ɵMatChipTrailingIcon_BaseFactory || (ɵMatChipTrailingIcon_BaseFactory = i0.ɵɵgetInheritedFactory(MatChipTrailingIcon)))(__ngFactoryType__ || MatChipTrailingIcon);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatChipTrailingIcon,\n    selectors: [[\"mat-chip-trailing-icon\"], [\"\", \"matChipTrailingIcon\", \"\"]],\n    hostAttrs: [\"aria-hidden\", \"true\", 1, \"mat-mdc-chip-trailing-icon\", \"mdc-evolution-chip__icon\", \"mdc-evolution-chip__icon--trailing\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MAT_CHIP_TRAILING_ICON,\n      useExisting: MatChipTrailingIcon\n    }]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatChipTrailingIcon, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-chip-trailing-icon, [matChipTrailingIcon]',\n      host: {\n        'class': 'mat-mdc-chip-trailing-icon mdc-evolution-chip__icon mdc-evolution-chip__icon--trailing',\n        'aria-hidden': 'true'\n      },\n      providers: [{\n        provide: MAT_CHIP_TRAILING_ICON,\n        useExisting: MatChipTrailingIcon\n      }]\n    }]\n  }], null, null);\n})();\n/**\n * Directive to remove the parent chip when the trailing icon is clicked or\n * when the ENTER key is pressed on it.\n *\n * Recommended for use with the Material Design \"cancel\" icon\n * available at https://material.io/icons/#ic_cancel.\n *\n * Example:\n *\n * ```\n * <mat-chip>\n *   <mat-icon matChipRemove>cancel</mat-icon>\n * </mat-chip>\n * ```\n */\nclass MatChipRemove extends MatChipAction {\n  _isPrimary = false;\n  _handleClick(event) {\n    if (!this.disabled) {\n      event.stopPropagation();\n      event.preventDefault();\n      this._parentChip.remove();\n    }\n  }\n  _handleKeydown(event) {\n    if ((event.keyCode === ENTER || event.keyCode === SPACE) && !this.disabled) {\n      event.stopPropagation();\n      event.preventDefault();\n      this._parentChip.remove();\n    }\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatChipRemove_BaseFactory;\n    return function MatChipRemove_Factory(__ngFactoryType__) {\n      return (ɵMatChipRemove_BaseFactory || (ɵMatChipRemove_BaseFactory = i0.ɵɵgetInheritedFactory(MatChipRemove)))(__ngFactoryType__ || MatChipRemove);\n    };\n  })();\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatChipRemove,\n    selectors: [[\"\", \"matChipRemove\", \"\"]],\n    hostAttrs: [\"role\", \"button\", 1, \"mat-mdc-chip-remove\", \"mat-mdc-chip-trailing-icon\", \"mat-focus-indicator\", \"mdc-evolution-chip__icon\", \"mdc-evolution-chip__icon--trailing\"],\n    hostVars: 1,\n    hostBindings: function MatChipRemove_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"aria-hidden\", null);\n      }\n    },\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MAT_CHIP_REMOVE,\n      useExisting: MatChipRemove\n    }]), i0.ɵɵInheritDefinitionFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatChipRemove, [{\n    type: Directive,\n    args: [{\n      selector: '[matChipRemove]',\n      host: {\n        'class': 'mat-mdc-chip-remove mat-mdc-chip-trailing-icon mat-focus-indicator ' + 'mdc-evolution-chip__icon mdc-evolution-chip__icon--trailing',\n        'role': 'button',\n        '[attr.aria-hidden]': 'null'\n      },\n      providers: [{\n        provide: MAT_CHIP_REMOVE,\n        useExisting: MatChipRemove\n      }]\n    }]\n  }], null, null);\n})();\n\n/**\n * Material design styled Chip base component. Used inside the MatChipSet component.\n *\n * Extended by MatChipOption and MatChipRow for different interaction patterns.\n */\nclass MatChip {\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  _elementRef = inject(ElementRef);\n  _ngZone = inject(NgZone);\n  _focusMonitor = inject(FocusMonitor);\n  _globalRippleOptions = inject(MAT_RIPPLE_GLOBAL_OPTIONS, {\n    optional: true\n  });\n  _document = inject(DOCUMENT);\n  /** Emits when the chip is focused. */\n  _onFocus = new Subject();\n  /** Emits when the chip is blurred. */\n  _onBlur = new Subject();\n  /** Whether this chip is a basic (unstyled) chip. */\n  _isBasicChip;\n  /** Role for the root of the chip. */\n  role = null;\n  /** Whether the chip has focus. */\n  _hasFocusInternal = false;\n  /** Whether moving focus into the chip is pending. */\n  _pendingFocus;\n  /** Subscription to changes in the chip's actions. */\n  _actionChanges;\n  /** Whether animations for the chip are enabled. */\n  _animationsDisabled;\n  /** All avatars present in the chip. */\n  _allLeadingIcons;\n  /** All trailing icons present in the chip. */\n  _allTrailingIcons;\n  /** All remove icons present in the chip. */\n  _allRemoveIcons;\n  _hasFocus() {\n    return this._hasFocusInternal;\n  }\n  /** A unique id for the chip. If none is supplied, it will be auto-generated. */\n  id = inject(_IdGenerator).getId('mat-mdc-chip-');\n  // TODO(#26104): Consider deprecating and using `_computeAriaAccessibleName` instead.\n  // `ariaLabel` may be unnecessary, and `_computeAriaAccessibleName` only supports\n  // datepicker's use case.\n  /** ARIA label for the content of the chip. */\n  ariaLabel = null;\n  // TODO(#26104): Consider deprecating and using `_computeAriaAccessibleName` instead.\n  // `ariaDescription` may be unnecessary, and `_computeAriaAccessibleName` only supports\n  // datepicker's use case.\n  /** ARIA description for the content of the chip. */\n  ariaDescription = null;\n  /** Id of a span that contains this chip's aria description. */\n  _ariaDescriptionId = `${this.id}-aria-description`;\n  /** Whether the chip list is disabled. */\n  _chipListDisabled = false;\n  _textElement;\n  /**\n   * The value of the chip. Defaults to the content inside\n   * the `mat-mdc-chip-action-label` element.\n   */\n  get value() {\n    return this._value !== undefined ? this._value : this._textElement.textContent.trim();\n  }\n  set value(value) {\n    this._value = value;\n  }\n  _value;\n  // TODO: should be typed as `ThemePalette` but internal apps pass in arbitrary strings.\n  /**\n   * Theme color of the chip. This API is supported in M2 themes only, it has no\n   * effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/chips/styling.\n   *\n   * For information on applying color variants in M3, see\n   * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n   */\n  color;\n  /**\n   * Determines whether or not the chip displays the remove styling and emits (removed) events.\n   */\n  removable = true;\n  /**\n   * Colors the chip for emphasis as if it were selected.\n   */\n  highlighted = false;\n  /** Whether the ripple effect is disabled or not. */\n  disableRipple = false;\n  /** Whether the chip is disabled. */\n  get disabled() {\n    return this._disabled || this._chipListDisabled;\n  }\n  set disabled(value) {\n    this._disabled = value;\n  }\n  _disabled = false;\n  /** Emitted when a chip is to be removed. */\n  removed = new EventEmitter();\n  /** Emitted when the chip is destroyed. */\n  destroyed = new EventEmitter();\n  /** The unstyled chip selector for this component. */\n  basicChipAttrName = 'mat-basic-chip';\n  /** The chip's leading icon. */\n  leadingIcon;\n  /** The chip's trailing icon. */\n  trailingIcon;\n  /** The chip's trailing remove icon. */\n  removeIcon;\n  /** Action receiving the primary set of user interactions. */\n  primaryAction;\n  /**\n   * Handles the lazy creation of the MatChip ripple.\n   * Used to improve initial load time of large applications.\n   */\n  _rippleLoader = inject(MatRippleLoader);\n  _injector = inject(Injector);\n  constructor() {\n    const styleLoader = inject(_CdkPrivateStyleLoader);\n    styleLoader.load(_StructuralStylesLoader);\n    styleLoader.load(_VisuallyHiddenLoader);\n    const animationMode = inject(ANIMATION_MODULE_TYPE, {\n      optional: true\n    });\n    this._animationsDisabled = animationMode === 'NoopAnimations';\n    this._monitorFocus();\n    this._rippleLoader?.configureRipple(this._elementRef.nativeElement, {\n      className: 'mat-mdc-chip-ripple',\n      disabled: this._isRippleDisabled()\n    });\n  }\n  ngOnInit() {\n    // This check needs to happen in `ngOnInit` so the overridden value of\n    // `basicChipAttrName` coming from base classes can be picked up.\n    const element = this._elementRef.nativeElement;\n    this._isBasicChip = element.hasAttribute(this.basicChipAttrName) || element.tagName.toLowerCase() === this.basicChipAttrName;\n  }\n  ngAfterViewInit() {\n    this._textElement = this._elementRef.nativeElement.querySelector('.mat-mdc-chip-action-label');\n    if (this._pendingFocus) {\n      this._pendingFocus = false;\n      this.focus();\n    }\n  }\n  ngAfterContentInit() {\n    // Since the styling depends on the presence of some\n    // actions, we have to mark for check on changes.\n    this._actionChanges = merge(this._allLeadingIcons.changes, this._allTrailingIcons.changes, this._allRemoveIcons.changes).subscribe(() => this._changeDetectorRef.markForCheck());\n  }\n  ngDoCheck() {\n    this._rippleLoader.setDisabled(this._elementRef.nativeElement, this._isRippleDisabled());\n  }\n  ngOnDestroy() {\n    this._focusMonitor.stopMonitoring(this._elementRef);\n    this._rippleLoader?.destroyRipple(this._elementRef.nativeElement);\n    this._actionChanges?.unsubscribe();\n    this.destroyed.emit({\n      chip: this\n    });\n    this.destroyed.complete();\n  }\n  /**\n   * Allows for programmatic removal of the chip.\n   *\n   * Informs any listeners of the removal request. Does not remove the chip from the DOM.\n   */\n  remove() {\n    if (this.removable) {\n      this.removed.emit({\n        chip: this\n      });\n    }\n  }\n  /** Whether or not the ripple should be disabled. */\n  _isRippleDisabled() {\n    return this.disabled || this.disableRipple || this._animationsDisabled || this._isBasicChip || !!this._globalRippleOptions?.disabled;\n  }\n  /** Returns whether the chip has a trailing icon. */\n  _hasTrailingIcon() {\n    return !!(this.trailingIcon || this.removeIcon);\n  }\n  /** Handles keyboard events on the chip. */\n  _handleKeydown(event) {\n    // Ignore backspace events where the user is holding down the key\n    // so that we don't accidentally remove too many chips.\n    if (event.keyCode === BACKSPACE && !event.repeat || event.keyCode === DELETE) {\n      event.preventDefault();\n      this.remove();\n    }\n  }\n  /** Allows for programmatic focusing of the chip. */\n  focus() {\n    if (!this.disabled) {\n      // If `focus` is called before `ngAfterViewInit`, we won't have access to the primary action.\n      // This can happen if the consumer tries to focus a chip immediately after it is added.\n      // Queue the method to be called again on init.\n      if (this.primaryAction) {\n        this.primaryAction.focus();\n      } else {\n        this._pendingFocus = true;\n      }\n    }\n  }\n  /** Gets the action that contains a specific target node. */\n  _getSourceAction(target) {\n    return this._getActions().find(action => {\n      const element = action._elementRef.nativeElement;\n      return element === target || element.contains(target);\n    });\n  }\n  /** Gets all of the actions within the chip. */\n  _getActions() {\n    const result = [];\n    if (this.primaryAction) {\n      result.push(this.primaryAction);\n    }\n    if (this.removeIcon) {\n      result.push(this.removeIcon);\n    }\n    if (this.trailingIcon) {\n      result.push(this.trailingIcon);\n    }\n    return result;\n  }\n  /** Handles interactions with the primary action of the chip. */\n  _handlePrimaryActionInteraction() {\n    // Empty here, but is overwritten in child classes.\n  }\n  /** Starts the focus monitoring process on the chip. */\n  _monitorFocus() {\n    this._focusMonitor.monitor(this._elementRef, true).subscribe(origin => {\n      const hasFocus = origin !== null;\n      if (hasFocus !== this._hasFocusInternal) {\n        this._hasFocusInternal = hasFocus;\n        if (hasFocus) {\n          this._onFocus.next({\n            chip: this\n          });\n        } else {\n          // When animations are enabled, Angular may end up removing the chip from the DOM a little\n          // earlier than usual, causing it to be blurred and throwing off the logic in the chip list\n          // that moves focus to the next item. To work around the issue, we defer marking the chip\n          // as not focused until after the next render.\n          this._changeDetectorRef.markForCheck();\n          setTimeout(() => this._ngZone.run(() => this._onBlur.next({\n            chip: this\n          })));\n        }\n      }\n    });\n  }\n  static ɵfac = function MatChip_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatChip)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatChip,\n    selectors: [[\"mat-basic-chip\"], [\"\", \"mat-basic-chip\", \"\"], [\"mat-chip\"], [\"\", \"mat-chip\", \"\"]],\n    contentQueries: function MatChip_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MAT_CHIP_AVATAR, 5);\n        i0.ɵɵcontentQuery(dirIndex, MAT_CHIP_TRAILING_ICON, 5);\n        i0.ɵɵcontentQuery(dirIndex, MAT_CHIP_REMOVE, 5);\n        i0.ɵɵcontentQuery(dirIndex, MAT_CHIP_AVATAR, 5);\n        i0.ɵɵcontentQuery(dirIndex, MAT_CHIP_TRAILING_ICON, 5);\n        i0.ɵɵcontentQuery(dirIndex, MAT_CHIP_REMOVE, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.leadingIcon = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.trailingIcon = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.removeIcon = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._allLeadingIcons = _t);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._allTrailingIcons = _t);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._allRemoveIcons = _t);\n      }\n    },\n    viewQuery: function MatChip_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(MatChipAction, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.primaryAction = _t.first);\n      }\n    },\n    hostAttrs: [1, \"mat-mdc-chip\"],\n    hostVars: 31,\n    hostBindings: function MatChip_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"keydown\", function MatChip_keydown_HostBindingHandler($event) {\n          return ctx._handleKeydown($event);\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵhostProperty(\"id\", ctx.id);\n        i0.ɵɵattribute(\"role\", ctx.role)(\"aria-label\", ctx.ariaLabel);\n        i0.ɵɵclassMap(\"mat-\" + (ctx.color || \"primary\"));\n        i0.ɵɵclassProp(\"mdc-evolution-chip\", !ctx._isBasicChip)(\"mdc-evolution-chip--disabled\", ctx.disabled)(\"mdc-evolution-chip--with-trailing-action\", ctx._hasTrailingIcon())(\"mdc-evolution-chip--with-primary-graphic\", ctx.leadingIcon)(\"mdc-evolution-chip--with-primary-icon\", ctx.leadingIcon)(\"mdc-evolution-chip--with-avatar\", ctx.leadingIcon)(\"mat-mdc-chip-with-avatar\", ctx.leadingIcon)(\"mat-mdc-chip-highlighted\", ctx.highlighted)(\"mat-mdc-chip-disabled\", ctx.disabled)(\"mat-mdc-basic-chip\", ctx._isBasicChip)(\"mat-mdc-standard-chip\", !ctx._isBasicChip)(\"mat-mdc-chip-with-trailing-icon\", ctx._hasTrailingIcon())(\"_mat-animation-noopable\", ctx._animationsDisabled);\n      }\n    },\n    inputs: {\n      role: \"role\",\n      id: \"id\",\n      ariaLabel: [0, \"aria-label\", \"ariaLabel\"],\n      ariaDescription: [0, \"aria-description\", \"ariaDescription\"],\n      value: \"value\",\n      color: \"color\",\n      removable: [2, \"removable\", \"removable\", booleanAttribute],\n      highlighted: [2, \"highlighted\", \"highlighted\", booleanAttribute],\n      disableRipple: [2, \"disableRipple\", \"disableRipple\", booleanAttribute],\n      disabled: [2, \"disabled\", \"disabled\", booleanAttribute]\n    },\n    outputs: {\n      removed: \"removed\",\n      destroyed: \"destroyed\"\n    },\n    exportAs: [\"matChip\"],\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MAT_CHIP,\n      useExisting: MatChip\n    }])],\n    ngContentSelectors: _c1,\n    decls: 8,\n    vars: 3,\n    consts: [[1, \"mat-mdc-chip-focus-overlay\"], [1, \"mdc-evolution-chip__cell\", \"mdc-evolution-chip__cell--primary\"], [\"matChipAction\", \"\", 3, \"isInteractive\"], [1, \"mdc-evolution-chip__graphic\", \"mat-mdc-chip-graphic\"], [1, \"mdc-evolution-chip__text-label\", \"mat-mdc-chip-action-label\"], [1, \"mat-mdc-chip-primary-focus-indicator\", \"mat-focus-indicator\"], [1, \"mdc-evolution-chip__cell\", \"mdc-evolution-chip__cell--trailing\"]],\n    template: function MatChip_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c0);\n        i0.ɵɵelement(0, \"span\", 0);\n        i0.ɵɵelementStart(1, \"span\", 1)(2, \"span\", 2);\n        i0.ɵɵtemplate(3, MatChip_Conditional_3_Template, 2, 0, \"span\", 3);\n        i0.ɵɵelementStart(4, \"span\", 4);\n        i0.ɵɵprojection(5);\n        i0.ɵɵelement(6, \"span\", 5);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵtemplate(7, MatChip_Conditional_7_Template, 2, 0, \"span\", 6);\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"isInteractive\", false);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(ctx.leadingIcon ? 3 : -1);\n        i0.ɵɵadvance(4);\n        i0.ɵɵconditional(ctx._hasTrailingIcon() ? 7 : -1);\n      }\n    },\n    dependencies: [MatChipAction],\n    styles: [\".mdc-evolution-chip,.mdc-evolution-chip__cell,.mdc-evolution-chip__action{display:inline-flex;align-items:center}.mdc-evolution-chip{position:relative;max-width:100%}.mdc-evolution-chip__cell,.mdc-evolution-chip__action{height:100%}.mdc-evolution-chip__cell--primary{flex-basis:100%;overflow-x:hidden}.mdc-evolution-chip__cell--trailing{flex:1 0 auto}.mdc-evolution-chip__action{align-items:center;background:none;border:none;box-sizing:content-box;cursor:pointer;display:inline-flex;justify-content:center;outline:none;padding:0;text-decoration:none;color:inherit}.mdc-evolution-chip__action--presentational{cursor:auto}.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{pointer-events:none}@media(forced-colors: active){.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{forced-color-adjust:none}}.mdc-evolution-chip__action--primary{font:inherit;letter-spacing:inherit;white-space:inherit;overflow-x:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary::before{border-width:var(--mdc-chip-outline-width, 1px);border-radius:var(--mdc-chip-container-shape-radius, 8px);box-sizing:border-box;content:\\\"\\\";height:100%;left:0;position:absolute;pointer-events:none;top:0;width:100%;z-index:1;border-style:solid}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--primary::before{border-color:var(--mdc-chip-outline-color, var(--mat-sys-outline))}.mdc-evolution-chip__action--primary:not(.mdc-evolution-chip__action--presentational):not(.mdc-ripple-upgraded):focus::before{border-color:var(--mdc-chip-focus-outline-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--primary::before{border-color:var(--mdc-chip-disabled-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__action--primary::before{border-width:var(--mdc-chip-flat-selected-outline-width, 0)}.mat-mdc-basic-chip .mdc-evolution-chip__action--primary{font:inherit}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip__action--trailing{position:relative;overflow:visible}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--trailing{color:var(--mdc-chip-with-trailing-icon-trailing-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--trailing{color:var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip__text-label{-webkit-user-select:none;user-select:none;white-space:nowrap;text-overflow:ellipsis;overflow:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__text-label{font-family:var(--mdc-chip-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mdc-chip-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mdc-chip-label-text-size, var(--mat-sys-label-large-size));font-weight:var(--mdc-chip-label-text-weight, var(--mat-sys-label-large-weight));letter-spacing:var(--mdc-chip-label-text-tracking, var(--mat-sys-label-large-tracking))}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mdc-chip-label-text-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mdc-chip-selected-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label,.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label{color:var(--mdc-chip-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-evolution-chip__graphic{align-items:center;display:inline-flex;justify-content:center;overflow:hidden;pointer-events:none;position:relative;flex:1 0 auto}.mat-mdc-standard-chip .mdc-evolution-chip__graphic{width:var(--mdc-chip-with-avatar-avatar-size, 24px);height:var(--mdc-chip-with-avatar-avatar-size, 24px);font-size:var(--mdc-chip-with-avatar-avatar-size, 24px)}.mdc-evolution-chip--selecting .mdc-evolution-chip__graphic{transition:width 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selectable:not(.mdc-evolution-chip--selected):not(.mdc-evolution-chip--with-primary-icon) .mdc-evolution-chip__graphic{width:0}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mdc-evolution-chip__checkmark{position:absolute;opacity:0;top:50%;left:50%;height:20px;width:20px}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__checkmark{color:var(--mdc-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__checkmark{color:var(--mdc-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark{transition:transform 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1);transform:translate(-75%, -50%)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{transform:translate(-50%, -50%);opacity:1}.mdc-evolution-chip__checkmark-svg{display:block}.mdc-evolution-chip__checkmark-path{stroke-width:2px;stroke-dasharray:29.7833385;stroke-dashoffset:29.7833385;stroke:currentColor}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark-path{transition:stroke-dashoffset 150ms 45ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark-path{stroke-dashoffset:0}@media(forced-colors: active){.mdc-evolution-chip__checkmark-path{stroke:CanvasText !important}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--trailing{height:18px;width:18px;font-size:18px}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove{opacity:calc(var(--mat-chip-trailing-action-opacity, 1)*var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove:focus{opacity:calc(var(--mat-chip-trailing-action-focus-opacity, 1)*var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mat-mdc-standard-chip{border-radius:var(--mdc-chip-container-shape-radius, 8px);height:var(--mdc-chip-container-height, 32px)}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled){background-color:var(--mdc-chip-elevated-container-color, transparent)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{background-color:var(--mdc-chip-elevated-disabled-container-color)}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled){background-color:var(--mdc-chip-elevated-selected-container-color, var(--mat-sys-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled{background-color:var(--mdc-chip-flat-disabled-selected-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}@media(forced-colors: active){.mat-mdc-standard-chip{outline:solid 1px}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--primary{border-radius:var(--mdc-chip-with-avatar-avatar-shape-radius, 24px);width:var(--mdc-chip-with-icon-icon-size, 18px);height:var(--mdc-chip-with-icon-icon-size, 18px);font-size:var(--mdc-chip-with-icon-icon-size, 18px)}.mdc-evolution-chip--selected .mdc-evolution-chip__icon--primary{opacity:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__icon--primary{color:var(--mdc-chip-with-icon-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--primary{color:var(--mdc-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-highlighted{--mdc-chip-with-icon-icon-color:var(--mdc-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container));--mdc-chip-elevated-container-color:var(--mdc-chip-elevated-selected-container-color, var(--mat-sys-secondary-container));--mdc-chip-label-text-color:var(--mdc-chip-selected-label-text-color, var(--mat-sys-on-secondary-container));--mdc-chip-outline-width:var(--mdc-chip-flat-selected-outline-width, 0)}.mat-mdc-chip-focus-overlay{background:var(--mdc-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-selected .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-chip:hover .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-hover-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mdc-chip-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-focus-overlay .mat-mdc-chip-selected:hover,.mat-mdc-chip-highlighted:hover .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-hover-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mdc-chip-selected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mdc-chip-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected.cdk-focused .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mdc-chip-selected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-evolution-chip--disabled:not(.mdc-evolution-chip--selected) .mat-mdc-chip-avatar{opacity:var(--mdc-chip-with-avatar-disabled-avatar-opacity, 0.38)}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{opacity:var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38)}.mdc-evolution-chip--disabled.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{opacity:var(--mdc-chip-with-icon-disabled-icon-opacity, 0.38)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{opacity:var(--mat-chip-disabled-container-opacity, 1)}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-trailing-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-remove{opacity:var(--mat-chip-trailing-action-opacity, 1)}.mat-mdc-chip-remove:focus{opacity:var(--mat-chip-trailing-action-focus-opacity, 1)}.mat-mdc-chip-remove::after{background-color:var(--mat-chip-trailing-action-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-remove:hover::after{opacity:var(--mat-chip-trailing-action-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-remove:focus::after{opacity:var(--mat-chip-trailing-action-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected .mat-mdc-chip-remove::after,.mat-mdc-chip-highlighted .mat-mdc-chip-remove::after{background-color:var(--mat-chip-selected-trailing-action-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-standard-chip .mdc-evolution-chip__cell--primary,.mat-mdc-standard-chip .mdc-evolution-chip__action--primary,.mat-mdc-standard-chip .mat-mdc-chip-action-label{overflow:visible}.mat-mdc-standard-chip .mat-mdc-chip-graphic,.mat-mdc-standard-chip .mat-mdc-chip-trailing-icon{box-sizing:content-box}.mat-mdc-standard-chip._mat-animation-noopable,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__graphic,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark-path{transition-duration:1ms;animation-duration:1ms}.mat-mdc-chip-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;opacity:0;border-radius:inherit;transition:opacity 150ms linear}._mat-animation-noopable .mat-mdc-chip-focus-overlay{transition:none}.mat-mdc-basic-chip .mat-mdc-chip-focus-overlay{display:none}.mat-mdc-chip .mat-ripple.mat-mdc-chip-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-chip-avatar{text-align:center;line-height:1;color:var(--mdc-chip-with-icon-icon-color, currentColor)}.mat-mdc-chip{position:relative;z-index:0}.mat-mdc-chip-action-label{text-align:left;z-index:1}[dir=rtl] .mat-mdc-chip-action-label{text-align:right}.mat-mdc-chip.mdc-evolution-chip--with-trailing-action .mat-mdc-chip-action-label{position:relative}.mat-mdc-chip-action-label .mat-mdc-chip-primary-focus-indicator{position:absolute;top:0;right:0;bottom:0;left:0;pointer-events:none}.mat-mdc-chip-action-label .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-mdc-chip-remove::before{margin:calc(var(--mat-focus-indicator-border-width, 3px)*-1);left:8px;right:8px}.mat-mdc-chip-remove::after{content:\\\"\\\";display:block;opacity:0;position:absolute;top:-3px;bottom:-3px;left:5px;right:5px;border-radius:50%;box-sizing:border-box;padding:12px;margin:-12px;background-clip:content-box}.mat-mdc-chip-remove .mat-icon{width:18px;height:18px;font-size:18px;box-sizing:content-box}.mat-chip-edit-input{cursor:text;display:inline-block;color:inherit;outline:0}@media(forced-colors: active){.mat-mdc-chip-selected:not(.mat-mdc-chip-multiple){outline-width:3px}}.mat-mdc-chip-action:focus .mat-focus-indicator::before{content:\\\"\\\"}.mdc-evolution-chip__icon,.mat-mdc-chip-remove .mat-icon{min-height:fit-content}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatChip, [{\n    type: Component,\n    args: [{\n      selector: 'mat-basic-chip, [mat-basic-chip], mat-chip, [mat-chip]',\n      exportAs: 'matChip',\n      host: {\n        'class': 'mat-mdc-chip',\n        '[class]': '\"mat-\" + (color || \"primary\")',\n        '[class.mdc-evolution-chip]': '!_isBasicChip',\n        '[class.mdc-evolution-chip--disabled]': 'disabled',\n        '[class.mdc-evolution-chip--with-trailing-action]': '_hasTrailingIcon()',\n        '[class.mdc-evolution-chip--with-primary-graphic]': 'leadingIcon',\n        '[class.mdc-evolution-chip--with-primary-icon]': 'leadingIcon',\n        '[class.mdc-evolution-chip--with-avatar]': 'leadingIcon',\n        '[class.mat-mdc-chip-with-avatar]': 'leadingIcon',\n        '[class.mat-mdc-chip-highlighted]': 'highlighted',\n        '[class.mat-mdc-chip-disabled]': 'disabled',\n        '[class.mat-mdc-basic-chip]': '_isBasicChip',\n        '[class.mat-mdc-standard-chip]': '!_isBasicChip',\n        '[class.mat-mdc-chip-with-trailing-icon]': '_hasTrailingIcon()',\n        '[class._mat-animation-noopable]': '_animationsDisabled',\n        '[id]': 'id',\n        '[attr.role]': 'role',\n        '[attr.aria-label]': 'ariaLabel',\n        '(keydown)': '_handleKeydown($event)'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [{\n        provide: MAT_CHIP,\n        useExisting: MatChip\n      }],\n      imports: [MatChipAction],\n      template: \"<span class=\\\"mat-mdc-chip-focus-overlay\\\"></span>\\n\\n<span class=\\\"mdc-evolution-chip__cell mdc-evolution-chip__cell--primary\\\">\\n  <span matChipAction [isInteractive]=\\\"false\\\">\\n    @if (leadingIcon) {\\n      <span class=\\\"mdc-evolution-chip__graphic mat-mdc-chip-graphic\\\">\\n        <ng-content select=\\\"mat-chip-avatar, [matChipAvatar]\\\"></ng-content>\\n      </span>\\n    }\\n    <span class=\\\"mdc-evolution-chip__text-label mat-mdc-chip-action-label\\\">\\n      <ng-content></ng-content>\\n      <span class=\\\"mat-mdc-chip-primary-focus-indicator mat-focus-indicator\\\"></span>\\n    </span>\\n  </span>\\n</span>\\n\\n@if (_hasTrailingIcon()) {\\n  <span class=\\\"mdc-evolution-chip__cell mdc-evolution-chip__cell--trailing\\\">\\n    <ng-content select=\\\"mat-chip-trailing-icon,[matChipRemove],[matChipTrailingIcon]\\\"></ng-content>\\n  </span>\\n}\\n\",\n      styles: [\".mdc-evolution-chip,.mdc-evolution-chip__cell,.mdc-evolution-chip__action{display:inline-flex;align-items:center}.mdc-evolution-chip{position:relative;max-width:100%}.mdc-evolution-chip__cell,.mdc-evolution-chip__action{height:100%}.mdc-evolution-chip__cell--primary{flex-basis:100%;overflow-x:hidden}.mdc-evolution-chip__cell--trailing{flex:1 0 auto}.mdc-evolution-chip__action{align-items:center;background:none;border:none;box-sizing:content-box;cursor:pointer;display:inline-flex;justify-content:center;outline:none;padding:0;text-decoration:none;color:inherit}.mdc-evolution-chip__action--presentational{cursor:auto}.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{pointer-events:none}@media(forced-colors: active){.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{forced-color-adjust:none}}.mdc-evolution-chip__action--primary{font:inherit;letter-spacing:inherit;white-space:inherit;overflow-x:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary::before{border-width:var(--mdc-chip-outline-width, 1px);border-radius:var(--mdc-chip-container-shape-radius, 8px);box-sizing:border-box;content:\\\"\\\";height:100%;left:0;position:absolute;pointer-events:none;top:0;width:100%;z-index:1;border-style:solid}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--primary::before{border-color:var(--mdc-chip-outline-color, var(--mat-sys-outline))}.mdc-evolution-chip__action--primary:not(.mdc-evolution-chip__action--presentational):not(.mdc-ripple-upgraded):focus::before{border-color:var(--mdc-chip-focus-outline-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--primary::before{border-color:var(--mdc-chip-disabled-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__action--primary::before{border-width:var(--mdc-chip-flat-selected-outline-width, 0)}.mat-mdc-basic-chip .mdc-evolution-chip__action--primary{font:inherit}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip__action--trailing{position:relative;overflow:visible}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--trailing{color:var(--mdc-chip-with-trailing-icon-trailing-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--trailing{color:var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip__text-label{-webkit-user-select:none;user-select:none;white-space:nowrap;text-overflow:ellipsis;overflow:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__text-label{font-family:var(--mdc-chip-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mdc-chip-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mdc-chip-label-text-size, var(--mat-sys-label-large-size));font-weight:var(--mdc-chip-label-text-weight, var(--mat-sys-label-large-weight));letter-spacing:var(--mdc-chip-label-text-tracking, var(--mat-sys-label-large-tracking))}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mdc-chip-label-text-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mdc-chip-selected-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label,.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label{color:var(--mdc-chip-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-evolution-chip__graphic{align-items:center;display:inline-flex;justify-content:center;overflow:hidden;pointer-events:none;position:relative;flex:1 0 auto}.mat-mdc-standard-chip .mdc-evolution-chip__graphic{width:var(--mdc-chip-with-avatar-avatar-size, 24px);height:var(--mdc-chip-with-avatar-avatar-size, 24px);font-size:var(--mdc-chip-with-avatar-avatar-size, 24px)}.mdc-evolution-chip--selecting .mdc-evolution-chip__graphic{transition:width 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selectable:not(.mdc-evolution-chip--selected):not(.mdc-evolution-chip--with-primary-icon) .mdc-evolution-chip__graphic{width:0}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mdc-evolution-chip__checkmark{position:absolute;opacity:0;top:50%;left:50%;height:20px;width:20px}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__checkmark{color:var(--mdc-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__checkmark{color:var(--mdc-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark{transition:transform 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1);transform:translate(-75%, -50%)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{transform:translate(-50%, -50%);opacity:1}.mdc-evolution-chip__checkmark-svg{display:block}.mdc-evolution-chip__checkmark-path{stroke-width:2px;stroke-dasharray:29.7833385;stroke-dashoffset:29.7833385;stroke:currentColor}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark-path{transition:stroke-dashoffset 150ms 45ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark-path{stroke-dashoffset:0}@media(forced-colors: active){.mdc-evolution-chip__checkmark-path{stroke:CanvasText !important}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--trailing{height:18px;width:18px;font-size:18px}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove{opacity:calc(var(--mat-chip-trailing-action-opacity, 1)*var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove:focus{opacity:calc(var(--mat-chip-trailing-action-focus-opacity, 1)*var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mat-mdc-standard-chip{border-radius:var(--mdc-chip-container-shape-radius, 8px);height:var(--mdc-chip-container-height, 32px)}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled){background-color:var(--mdc-chip-elevated-container-color, transparent)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{background-color:var(--mdc-chip-elevated-disabled-container-color)}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled){background-color:var(--mdc-chip-elevated-selected-container-color, var(--mat-sys-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled{background-color:var(--mdc-chip-flat-disabled-selected-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}@media(forced-colors: active){.mat-mdc-standard-chip{outline:solid 1px}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--primary{border-radius:var(--mdc-chip-with-avatar-avatar-shape-radius, 24px);width:var(--mdc-chip-with-icon-icon-size, 18px);height:var(--mdc-chip-with-icon-icon-size, 18px);font-size:var(--mdc-chip-with-icon-icon-size, 18px)}.mdc-evolution-chip--selected .mdc-evolution-chip__icon--primary{opacity:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__icon--primary{color:var(--mdc-chip-with-icon-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--primary{color:var(--mdc-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-highlighted{--mdc-chip-with-icon-icon-color:var(--mdc-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container));--mdc-chip-elevated-container-color:var(--mdc-chip-elevated-selected-container-color, var(--mat-sys-secondary-container));--mdc-chip-label-text-color:var(--mdc-chip-selected-label-text-color, var(--mat-sys-on-secondary-container));--mdc-chip-outline-width:var(--mdc-chip-flat-selected-outline-width, 0)}.mat-mdc-chip-focus-overlay{background:var(--mdc-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-selected .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-chip:hover .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-hover-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mdc-chip-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-focus-overlay .mat-mdc-chip-selected:hover,.mat-mdc-chip-highlighted:hover .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-hover-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mdc-chip-selected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mdc-chip-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected.cdk-focused .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mdc-chip-selected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-evolution-chip--disabled:not(.mdc-evolution-chip--selected) .mat-mdc-chip-avatar{opacity:var(--mdc-chip-with-avatar-disabled-avatar-opacity, 0.38)}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{opacity:var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38)}.mdc-evolution-chip--disabled.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{opacity:var(--mdc-chip-with-icon-disabled-icon-opacity, 0.38)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{opacity:var(--mat-chip-disabled-container-opacity, 1)}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-trailing-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-remove{opacity:var(--mat-chip-trailing-action-opacity, 1)}.mat-mdc-chip-remove:focus{opacity:var(--mat-chip-trailing-action-focus-opacity, 1)}.mat-mdc-chip-remove::after{background-color:var(--mat-chip-trailing-action-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-remove:hover::after{opacity:var(--mat-chip-trailing-action-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-remove:focus::after{opacity:var(--mat-chip-trailing-action-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected .mat-mdc-chip-remove::after,.mat-mdc-chip-highlighted .mat-mdc-chip-remove::after{background-color:var(--mat-chip-selected-trailing-action-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-standard-chip .mdc-evolution-chip__cell--primary,.mat-mdc-standard-chip .mdc-evolution-chip__action--primary,.mat-mdc-standard-chip .mat-mdc-chip-action-label{overflow:visible}.mat-mdc-standard-chip .mat-mdc-chip-graphic,.mat-mdc-standard-chip .mat-mdc-chip-trailing-icon{box-sizing:content-box}.mat-mdc-standard-chip._mat-animation-noopable,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__graphic,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark-path{transition-duration:1ms;animation-duration:1ms}.mat-mdc-chip-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;opacity:0;border-radius:inherit;transition:opacity 150ms linear}._mat-animation-noopable .mat-mdc-chip-focus-overlay{transition:none}.mat-mdc-basic-chip .mat-mdc-chip-focus-overlay{display:none}.mat-mdc-chip .mat-ripple.mat-mdc-chip-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-chip-avatar{text-align:center;line-height:1;color:var(--mdc-chip-with-icon-icon-color, currentColor)}.mat-mdc-chip{position:relative;z-index:0}.mat-mdc-chip-action-label{text-align:left;z-index:1}[dir=rtl] .mat-mdc-chip-action-label{text-align:right}.mat-mdc-chip.mdc-evolution-chip--with-trailing-action .mat-mdc-chip-action-label{position:relative}.mat-mdc-chip-action-label .mat-mdc-chip-primary-focus-indicator{position:absolute;top:0;right:0;bottom:0;left:0;pointer-events:none}.mat-mdc-chip-action-label .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-mdc-chip-remove::before{margin:calc(var(--mat-focus-indicator-border-width, 3px)*-1);left:8px;right:8px}.mat-mdc-chip-remove::after{content:\\\"\\\";display:block;opacity:0;position:absolute;top:-3px;bottom:-3px;left:5px;right:5px;border-radius:50%;box-sizing:border-box;padding:12px;margin:-12px;background-clip:content-box}.mat-mdc-chip-remove .mat-icon{width:18px;height:18px;font-size:18px;box-sizing:content-box}.mat-chip-edit-input{cursor:text;display:inline-block;color:inherit;outline:0}@media(forced-colors: active){.mat-mdc-chip-selected:not(.mat-mdc-chip-multiple){outline-width:3px}}.mat-mdc-chip-action:focus .mat-focus-indicator::before{content:\\\"\\\"}.mdc-evolution-chip__icon,.mat-mdc-chip-remove .mat-icon{min-height:fit-content}\\n\"]\n    }]\n  }], () => [], {\n    role: [{\n      type: Input\n    }],\n    _allLeadingIcons: [{\n      type: ContentChildren,\n      args: [MAT_CHIP_AVATAR, {\n        descendants: true\n      }]\n    }],\n    _allTrailingIcons: [{\n      type: ContentChildren,\n      args: [MAT_CHIP_TRAILING_ICON, {\n        descendants: true\n      }]\n    }],\n    _allRemoveIcons: [{\n      type: ContentChildren,\n      args: [MAT_CHIP_REMOVE, {\n        descendants: true\n      }]\n    }],\n    id: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaDescription: [{\n      type: Input,\n      args: ['aria-description']\n    }],\n    value: [{\n      type: Input\n    }],\n    color: [{\n      type: Input\n    }],\n    removable: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    highlighted: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disableRipple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    removed: [{\n      type: Output\n    }],\n    destroyed: [{\n      type: Output\n    }],\n    leadingIcon: [{\n      type: ContentChild,\n      args: [MAT_CHIP_AVATAR]\n    }],\n    trailingIcon: [{\n      type: ContentChild,\n      args: [MAT_CHIP_TRAILING_ICON]\n    }],\n    removeIcon: [{\n      type: ContentChild,\n      args: [MAT_CHIP_REMOVE]\n    }],\n    primaryAction: [{\n      type: ViewChild,\n      args: [MatChipAction]\n    }]\n  });\n})();\n\n/** Event object emitted by MatChipOption when selected or deselected. */\nclass MatChipSelectionChange {\n  source;\n  selected;\n  isUserInput;\n  constructor(/** Reference to the chip that emitted the event. */\n  source, /** Whether the chip that emitted the event is selected. */\n  selected, /** Whether the selection change was a result of a user interaction. */\n  isUserInput = false) {\n    this.source = source;\n    this.selected = selected;\n    this.isUserInput = isUserInput;\n  }\n}\n/**\n * An extension of the MatChip component that supports chip selection. Used with MatChipListbox.\n *\n * Unlike other chips, the user can focus on disabled chip options inside a MatChipListbox. The\n * user cannot click disabled chips.\n */\nclass MatChipOption extends MatChip {\n  /** Default chip options. */\n  _defaultOptions = inject(MAT_CHIPS_DEFAULT_OPTIONS, {\n    optional: true\n  });\n  /** Whether the chip list is selectable. */\n  chipListSelectable = true;\n  /** Whether the chip list is in multi-selection mode. */\n  _chipListMultiple = false;\n  /** Whether the chip list hides single-selection indicator. */\n  _chipListHideSingleSelectionIndicator = this._defaultOptions?.hideSingleSelectionIndicator ?? false;\n  /**\n   * Whether or not the chip is selectable.\n   *\n   * When a chip is not selectable, changes to its selected state are always\n   * ignored. By default an option chip is selectable, and it becomes\n   * non-selectable if its parent chip list is not selectable.\n   */\n  get selectable() {\n    return this._selectable && this.chipListSelectable;\n  }\n  set selectable(value) {\n    this._selectable = value;\n    this._changeDetectorRef.markForCheck();\n  }\n  _selectable = true;\n  /** Whether the chip is selected. */\n  get selected() {\n    return this._selected;\n  }\n  set selected(value) {\n    this._setSelectedState(value, false, true);\n  }\n  _selected = false;\n  /**\n   * The ARIA selected applied to the chip. Conforms to WAI ARIA best practices for listbox\n   * interaction patterns.\n   *\n   * From [WAI ARIA Listbox authoring practices guide](\n   * https://www.w3.org/WAI/ARIA/apg/patterns/listbox/):\n   *  \"If any options are selected, each selected option has either aria-selected or aria-checked\n   *  set to true. All options that are selectable but not selected have either aria-selected or\n   *  aria-checked set to false.\"\n   *\n   * Set `aria-selected=\"false\"` on not-selected listbox options that are selectable to fix\n   * VoiceOver reading every option as \"selected\" (#25736).\n   */\n  get ariaSelected() {\n    return this.selectable ? this.selected.toString() : null;\n  }\n  /** The unstyled chip selector for this component. */\n  basicChipAttrName = 'mat-basic-chip-option';\n  /** Emitted when the chip is selected or deselected. */\n  selectionChange = new EventEmitter();\n  ngOnInit() {\n    super.ngOnInit();\n    this.role = 'presentation';\n  }\n  /** Selects the chip. */\n  select() {\n    this._setSelectedState(true, false, true);\n  }\n  /** Deselects the chip. */\n  deselect() {\n    this._setSelectedState(false, false, true);\n  }\n  /** Selects this chip and emits userInputSelection event */\n  selectViaInteraction() {\n    this._setSelectedState(true, true, true);\n  }\n  /** Toggles the current selected state of this chip. */\n  toggleSelected(isUserInput = false) {\n    this._setSelectedState(!this.selected, isUserInput, true);\n    return this.selected;\n  }\n  _handlePrimaryActionInteraction() {\n    if (!this.disabled) {\n      // Interacting with the primary action implies that the chip already has focus, however\n      // there's a bug in Safari where focus ends up lingering on the previous chip (see #27544).\n      // We work around it by explicitly focusing the primary action of the current chip.\n      this.focus();\n      if (this.selectable) {\n        this.toggleSelected(true);\n      }\n    }\n  }\n  _hasLeadingGraphic() {\n    if (this.leadingIcon) {\n      return true;\n    }\n    // The checkmark graphic communicates selected state for both single-select and multi-select.\n    // Include checkmark in single-select to fix a11y issue where selected state is communicated\n    // visually only using color (#25886).\n    return !this._chipListHideSingleSelectionIndicator || this._chipListMultiple;\n  }\n  _setSelectedState(isSelected, isUserInput, emitEvent) {\n    if (isSelected !== this.selected) {\n      this._selected = isSelected;\n      if (emitEvent) {\n        this.selectionChange.emit({\n          source: this,\n          isUserInput,\n          selected: this.selected\n        });\n      }\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatChipOption_BaseFactory;\n    return function MatChipOption_Factory(__ngFactoryType__) {\n      return (ɵMatChipOption_BaseFactory || (ɵMatChipOption_BaseFactory = i0.ɵɵgetInheritedFactory(MatChipOption)))(__ngFactoryType__ || MatChipOption);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatChipOption,\n    selectors: [[\"mat-basic-chip-option\"], [\"\", \"mat-basic-chip-option\", \"\"], [\"mat-chip-option\"], [\"\", \"mat-chip-option\", \"\"]],\n    hostAttrs: [1, \"mat-mdc-chip\", \"mat-mdc-chip-option\"],\n    hostVars: 37,\n    hostBindings: function MatChipOption_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵhostProperty(\"id\", ctx.id);\n        i0.ɵɵattribute(\"tabindex\", null)(\"aria-label\", null)(\"aria-description\", null)(\"role\", ctx.role);\n        i0.ɵɵclassProp(\"mdc-evolution-chip\", !ctx._isBasicChip)(\"mdc-evolution-chip--filter\", !ctx._isBasicChip)(\"mdc-evolution-chip--selectable\", !ctx._isBasicChip)(\"mat-mdc-chip-selected\", ctx.selected)(\"mat-mdc-chip-multiple\", ctx._chipListMultiple)(\"mat-mdc-chip-disabled\", ctx.disabled)(\"mat-mdc-chip-with-avatar\", ctx.leadingIcon)(\"mdc-evolution-chip--disabled\", ctx.disabled)(\"mdc-evolution-chip--selected\", ctx.selected)(\"mdc-evolution-chip--selecting\", !ctx._animationsDisabled)(\"mdc-evolution-chip--with-trailing-action\", ctx._hasTrailingIcon())(\"mdc-evolution-chip--with-primary-icon\", ctx.leadingIcon)(\"mdc-evolution-chip--with-primary-graphic\", ctx._hasLeadingGraphic())(\"mdc-evolution-chip--with-avatar\", ctx.leadingIcon)(\"mat-mdc-chip-highlighted\", ctx.highlighted)(\"mat-mdc-chip-with-trailing-icon\", ctx._hasTrailingIcon());\n      }\n    },\n    inputs: {\n      selectable: [2, \"selectable\", \"selectable\", booleanAttribute],\n      selected: [2, \"selected\", \"selected\", booleanAttribute]\n    },\n    outputs: {\n      selectionChange: \"selectionChange\"\n    },\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MatChip,\n      useExisting: MatChipOption\n    }, {\n      provide: MAT_CHIP,\n      useExisting: MatChipOption\n    }]), i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c1,\n    decls: 10,\n    vars: 8,\n    consts: [[1, \"mat-mdc-chip-focus-overlay\"], [1, \"mdc-evolution-chip__cell\", \"mdc-evolution-chip__cell--primary\"], [\"matChipAction\", \"\", \"role\", \"option\", 3, \"_allowFocusWhenDisabled\"], [1, \"mdc-evolution-chip__graphic\", \"mat-mdc-chip-graphic\"], [1, \"mdc-evolution-chip__text-label\", \"mat-mdc-chip-action-label\"], [1, \"mat-mdc-chip-primary-focus-indicator\", \"mat-focus-indicator\"], [1, \"mdc-evolution-chip__cell\", \"mdc-evolution-chip__cell--trailing\"], [1, \"cdk-visually-hidden\", 3, \"id\"], [1, \"mdc-evolution-chip__checkmark\"], [\"viewBox\", \"-2 -3 30 30\", \"focusable\", \"false\", \"aria-hidden\", \"true\", 1, \"mdc-evolution-chip__checkmark-svg\"], [\"fill\", \"none\", \"stroke\", \"currentColor\", \"d\", \"M1.73,12.91 8.1,19.28 22.79,4.59\", 1, \"mdc-evolution-chip__checkmark-path\"]],\n    template: function MatChipOption_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c0);\n        i0.ɵɵelement(0, \"span\", 0);\n        i0.ɵɵelementStart(1, \"span\", 1)(2, \"button\", 2);\n        i0.ɵɵtemplate(3, MatChipOption_Conditional_3_Template, 5, 0, \"span\", 3);\n        i0.ɵɵelementStart(4, \"span\", 4);\n        i0.ɵɵprojection(5);\n        i0.ɵɵelement(6, \"span\", 5);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵtemplate(7, MatChipOption_Conditional_7_Template, 2, 0, \"span\", 6);\n        i0.ɵɵelementStart(8, \"span\", 7);\n        i0.ɵɵtext(9);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"_allowFocusWhenDisabled\", true);\n        i0.ɵɵattribute(\"aria-selected\", ctx.ariaSelected)(\"aria-label\", ctx.ariaLabel)(\"aria-describedby\", ctx._ariaDescriptionId);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(ctx._hasLeadingGraphic() ? 3 : -1);\n        i0.ɵɵadvance(4);\n        i0.ɵɵconditional(ctx._hasTrailingIcon() ? 7 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"id\", ctx._ariaDescriptionId);\n        i0.ɵɵadvance();\n        i0.ɵɵtextInterpolate(ctx.ariaDescription);\n      }\n    },\n    dependencies: [MatChipAction],\n    styles: [_c2],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatChipOption, [{\n    type: Component,\n    args: [{\n      selector: 'mat-basic-chip-option, [mat-basic-chip-option], mat-chip-option, [mat-chip-option]',\n      host: {\n        'class': 'mat-mdc-chip mat-mdc-chip-option',\n        '[class.mdc-evolution-chip]': '!_isBasicChip',\n        '[class.mdc-evolution-chip--filter]': '!_isBasicChip',\n        '[class.mdc-evolution-chip--selectable]': '!_isBasicChip',\n        '[class.mat-mdc-chip-selected]': 'selected',\n        '[class.mat-mdc-chip-multiple]': '_chipListMultiple',\n        '[class.mat-mdc-chip-disabled]': 'disabled',\n        '[class.mat-mdc-chip-with-avatar]': 'leadingIcon',\n        '[class.mdc-evolution-chip--disabled]': 'disabled',\n        '[class.mdc-evolution-chip--selected]': 'selected',\n        // This class enables the transition on the checkmark. Usually MDC adds it when selection\n        // starts and removes it once the animation is finished. We don't need to go through all\n        // the trouble, because we only care about the selection animation. MDC needs to do it,\n        // because they also have an exit animation that we don't care about.\n        '[class.mdc-evolution-chip--selecting]': '!_animationsDisabled',\n        '[class.mdc-evolution-chip--with-trailing-action]': '_hasTrailingIcon()',\n        '[class.mdc-evolution-chip--with-primary-icon]': 'leadingIcon',\n        '[class.mdc-evolution-chip--with-primary-graphic]': '_hasLeadingGraphic()',\n        '[class.mdc-evolution-chip--with-avatar]': 'leadingIcon',\n        '[class.mat-mdc-chip-highlighted]': 'highlighted',\n        '[class.mat-mdc-chip-with-trailing-icon]': '_hasTrailingIcon()',\n        '[attr.tabindex]': 'null',\n        '[attr.aria-label]': 'null',\n        '[attr.aria-description]': 'null',\n        '[attr.role]': 'role',\n        '[id]': 'id'\n      },\n      providers: [{\n        provide: MatChip,\n        useExisting: MatChipOption\n      }, {\n        provide: MAT_CHIP,\n        useExisting: MatChipOption\n      }],\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      imports: [MatChipAction],\n      template: \"<span class=\\\"mat-mdc-chip-focus-overlay\\\"></span>\\n\\n<span class=\\\"mdc-evolution-chip__cell mdc-evolution-chip__cell--primary\\\">\\n  <button\\n    matChipAction\\n    [_allowFocusWhenDisabled]=\\\"true\\\"\\n    [attr.aria-selected]=\\\"ariaSelected\\\"\\n    [attr.aria-label]=\\\"ariaLabel\\\"\\n    [attr.aria-describedby]=\\\"_ariaDescriptionId\\\"\\n    role=\\\"option\\\">\\n    @if (_hasLeadingGraphic()) {\\n      <span class=\\\"mdc-evolution-chip__graphic mat-mdc-chip-graphic\\\">\\n        <ng-content select=\\\"mat-chip-avatar, [matChipAvatar]\\\"></ng-content>\\n        <span class=\\\"mdc-evolution-chip__checkmark\\\">\\n          <svg\\n            class=\\\"mdc-evolution-chip__checkmark-svg\\\"\\n            viewBox=\\\"-2 -3 30 30\\\"\\n            focusable=\\\"false\\\"\\n            aria-hidden=\\\"true\\\">\\n            <path class=\\\"mdc-evolution-chip__checkmark-path\\\"\\n                  fill=\\\"none\\\" stroke=\\\"currentColor\\\" d=\\\"M1.73,12.91 8.1,19.28 22.79,4.59\\\" />\\n          </svg>\\n        </span>\\n      </span>\\n    }\\n    <span class=\\\"mdc-evolution-chip__text-label mat-mdc-chip-action-label\\\">\\n      <ng-content></ng-content>\\n      <span class=\\\"mat-mdc-chip-primary-focus-indicator mat-focus-indicator\\\"></span>\\n    </span>\\n  </button>\\n</span>\\n\\n@if (_hasTrailingIcon()) {\\n  <span class=\\\"mdc-evolution-chip__cell mdc-evolution-chip__cell--trailing\\\">\\n    <ng-content select=\\\"mat-chip-trailing-icon,[matChipRemove],[matChipTrailingIcon]\\\"></ng-content>\\n  </span>\\n}\\n\\n<span class=\\\"cdk-visually-hidden\\\" [id]=\\\"_ariaDescriptionId\\\">{{ariaDescription}}</span>\\n\",\n      styles: [\".mdc-evolution-chip,.mdc-evolution-chip__cell,.mdc-evolution-chip__action{display:inline-flex;align-items:center}.mdc-evolution-chip{position:relative;max-width:100%}.mdc-evolution-chip__cell,.mdc-evolution-chip__action{height:100%}.mdc-evolution-chip__cell--primary{flex-basis:100%;overflow-x:hidden}.mdc-evolution-chip__cell--trailing{flex:1 0 auto}.mdc-evolution-chip__action{align-items:center;background:none;border:none;box-sizing:content-box;cursor:pointer;display:inline-flex;justify-content:center;outline:none;padding:0;text-decoration:none;color:inherit}.mdc-evolution-chip__action--presentational{cursor:auto}.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{pointer-events:none}@media(forced-colors: active){.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{forced-color-adjust:none}}.mdc-evolution-chip__action--primary{font:inherit;letter-spacing:inherit;white-space:inherit;overflow-x:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary::before{border-width:var(--mdc-chip-outline-width, 1px);border-radius:var(--mdc-chip-container-shape-radius, 8px);box-sizing:border-box;content:\\\"\\\";height:100%;left:0;position:absolute;pointer-events:none;top:0;width:100%;z-index:1;border-style:solid}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--primary::before{border-color:var(--mdc-chip-outline-color, var(--mat-sys-outline))}.mdc-evolution-chip__action--primary:not(.mdc-evolution-chip__action--presentational):not(.mdc-ripple-upgraded):focus::before{border-color:var(--mdc-chip-focus-outline-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--primary::before{border-color:var(--mdc-chip-disabled-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__action--primary::before{border-width:var(--mdc-chip-flat-selected-outline-width, 0)}.mat-mdc-basic-chip .mdc-evolution-chip__action--primary{font:inherit}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip__action--trailing{position:relative;overflow:visible}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--trailing{color:var(--mdc-chip-with-trailing-icon-trailing-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--trailing{color:var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip__text-label{-webkit-user-select:none;user-select:none;white-space:nowrap;text-overflow:ellipsis;overflow:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__text-label{font-family:var(--mdc-chip-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mdc-chip-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mdc-chip-label-text-size, var(--mat-sys-label-large-size));font-weight:var(--mdc-chip-label-text-weight, var(--mat-sys-label-large-weight));letter-spacing:var(--mdc-chip-label-text-tracking, var(--mat-sys-label-large-tracking))}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mdc-chip-label-text-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mdc-chip-selected-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label,.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label{color:var(--mdc-chip-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-evolution-chip__graphic{align-items:center;display:inline-flex;justify-content:center;overflow:hidden;pointer-events:none;position:relative;flex:1 0 auto}.mat-mdc-standard-chip .mdc-evolution-chip__graphic{width:var(--mdc-chip-with-avatar-avatar-size, 24px);height:var(--mdc-chip-with-avatar-avatar-size, 24px);font-size:var(--mdc-chip-with-avatar-avatar-size, 24px)}.mdc-evolution-chip--selecting .mdc-evolution-chip__graphic{transition:width 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selectable:not(.mdc-evolution-chip--selected):not(.mdc-evolution-chip--with-primary-icon) .mdc-evolution-chip__graphic{width:0}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mdc-evolution-chip__checkmark{position:absolute;opacity:0;top:50%;left:50%;height:20px;width:20px}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__checkmark{color:var(--mdc-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__checkmark{color:var(--mdc-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark{transition:transform 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1);transform:translate(-75%, -50%)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{transform:translate(-50%, -50%);opacity:1}.mdc-evolution-chip__checkmark-svg{display:block}.mdc-evolution-chip__checkmark-path{stroke-width:2px;stroke-dasharray:29.7833385;stroke-dashoffset:29.7833385;stroke:currentColor}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark-path{transition:stroke-dashoffset 150ms 45ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark-path{stroke-dashoffset:0}@media(forced-colors: active){.mdc-evolution-chip__checkmark-path{stroke:CanvasText !important}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--trailing{height:18px;width:18px;font-size:18px}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove{opacity:calc(var(--mat-chip-trailing-action-opacity, 1)*var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove:focus{opacity:calc(var(--mat-chip-trailing-action-focus-opacity, 1)*var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mat-mdc-standard-chip{border-radius:var(--mdc-chip-container-shape-radius, 8px);height:var(--mdc-chip-container-height, 32px)}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled){background-color:var(--mdc-chip-elevated-container-color, transparent)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{background-color:var(--mdc-chip-elevated-disabled-container-color)}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled){background-color:var(--mdc-chip-elevated-selected-container-color, var(--mat-sys-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled{background-color:var(--mdc-chip-flat-disabled-selected-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}@media(forced-colors: active){.mat-mdc-standard-chip{outline:solid 1px}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--primary{border-radius:var(--mdc-chip-with-avatar-avatar-shape-radius, 24px);width:var(--mdc-chip-with-icon-icon-size, 18px);height:var(--mdc-chip-with-icon-icon-size, 18px);font-size:var(--mdc-chip-with-icon-icon-size, 18px)}.mdc-evolution-chip--selected .mdc-evolution-chip__icon--primary{opacity:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__icon--primary{color:var(--mdc-chip-with-icon-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--primary{color:var(--mdc-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-highlighted{--mdc-chip-with-icon-icon-color:var(--mdc-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container));--mdc-chip-elevated-container-color:var(--mdc-chip-elevated-selected-container-color, var(--mat-sys-secondary-container));--mdc-chip-label-text-color:var(--mdc-chip-selected-label-text-color, var(--mat-sys-on-secondary-container));--mdc-chip-outline-width:var(--mdc-chip-flat-selected-outline-width, 0)}.mat-mdc-chip-focus-overlay{background:var(--mdc-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-selected .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-chip:hover .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-hover-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mdc-chip-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-focus-overlay .mat-mdc-chip-selected:hover,.mat-mdc-chip-highlighted:hover .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-hover-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mdc-chip-selected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mdc-chip-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected.cdk-focused .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mdc-chip-selected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-evolution-chip--disabled:not(.mdc-evolution-chip--selected) .mat-mdc-chip-avatar{opacity:var(--mdc-chip-with-avatar-disabled-avatar-opacity, 0.38)}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{opacity:var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38)}.mdc-evolution-chip--disabled.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{opacity:var(--mdc-chip-with-icon-disabled-icon-opacity, 0.38)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{opacity:var(--mat-chip-disabled-container-opacity, 1)}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-trailing-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-remove{opacity:var(--mat-chip-trailing-action-opacity, 1)}.mat-mdc-chip-remove:focus{opacity:var(--mat-chip-trailing-action-focus-opacity, 1)}.mat-mdc-chip-remove::after{background-color:var(--mat-chip-trailing-action-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-remove:hover::after{opacity:var(--mat-chip-trailing-action-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-remove:focus::after{opacity:var(--mat-chip-trailing-action-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected .mat-mdc-chip-remove::after,.mat-mdc-chip-highlighted .mat-mdc-chip-remove::after{background-color:var(--mat-chip-selected-trailing-action-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-standard-chip .mdc-evolution-chip__cell--primary,.mat-mdc-standard-chip .mdc-evolution-chip__action--primary,.mat-mdc-standard-chip .mat-mdc-chip-action-label{overflow:visible}.mat-mdc-standard-chip .mat-mdc-chip-graphic,.mat-mdc-standard-chip .mat-mdc-chip-trailing-icon{box-sizing:content-box}.mat-mdc-standard-chip._mat-animation-noopable,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__graphic,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark-path{transition-duration:1ms;animation-duration:1ms}.mat-mdc-chip-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;opacity:0;border-radius:inherit;transition:opacity 150ms linear}._mat-animation-noopable .mat-mdc-chip-focus-overlay{transition:none}.mat-mdc-basic-chip .mat-mdc-chip-focus-overlay{display:none}.mat-mdc-chip .mat-ripple.mat-mdc-chip-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-chip-avatar{text-align:center;line-height:1;color:var(--mdc-chip-with-icon-icon-color, currentColor)}.mat-mdc-chip{position:relative;z-index:0}.mat-mdc-chip-action-label{text-align:left;z-index:1}[dir=rtl] .mat-mdc-chip-action-label{text-align:right}.mat-mdc-chip.mdc-evolution-chip--with-trailing-action .mat-mdc-chip-action-label{position:relative}.mat-mdc-chip-action-label .mat-mdc-chip-primary-focus-indicator{position:absolute;top:0;right:0;bottom:0;left:0;pointer-events:none}.mat-mdc-chip-action-label .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-mdc-chip-remove::before{margin:calc(var(--mat-focus-indicator-border-width, 3px)*-1);left:8px;right:8px}.mat-mdc-chip-remove::after{content:\\\"\\\";display:block;opacity:0;position:absolute;top:-3px;bottom:-3px;left:5px;right:5px;border-radius:50%;box-sizing:border-box;padding:12px;margin:-12px;background-clip:content-box}.mat-mdc-chip-remove .mat-icon{width:18px;height:18px;font-size:18px;box-sizing:content-box}.mat-chip-edit-input{cursor:text;display:inline-block;color:inherit;outline:0}@media(forced-colors: active){.mat-mdc-chip-selected:not(.mat-mdc-chip-multiple){outline-width:3px}}.mat-mdc-chip-action:focus .mat-focus-indicator::before{content:\\\"\\\"}.mdc-evolution-chip__icon,.mat-mdc-chip-remove .mat-icon{min-height:fit-content}\\n\"]\n    }]\n  }], null, {\n    selectable: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    selected: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    selectionChange: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * A directive that makes a span editable and exposes functions to modify and retrieve the\n * element's contents.\n */\nclass MatChipEditInput {\n  _elementRef = inject(ElementRef);\n  _document = inject(DOCUMENT);\n  constructor() {}\n  initialize(initialValue) {\n    this.getNativeElement().focus();\n    this.setValue(initialValue);\n  }\n  getNativeElement() {\n    return this._elementRef.nativeElement;\n  }\n  setValue(value) {\n    this.getNativeElement().textContent = value;\n    this._moveCursorToEndOfInput();\n  }\n  getValue() {\n    return this.getNativeElement().textContent || '';\n  }\n  _moveCursorToEndOfInput() {\n    const range = this._document.createRange();\n    range.selectNodeContents(this.getNativeElement());\n    range.collapse(false);\n    const sel = window.getSelection();\n    sel.removeAllRanges();\n    sel.addRange(range);\n  }\n  static ɵfac = function MatChipEditInput_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatChipEditInput)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatChipEditInput,\n    selectors: [[\"span\", \"matChipEditInput\", \"\"]],\n    hostAttrs: [\"role\", \"textbox\", \"tabindex\", \"-1\", \"contenteditable\", \"true\", 1, \"mat-chip-edit-input\"]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatChipEditInput, [{\n    type: Directive,\n    args: [{\n      selector: 'span[matChipEditInput]',\n      host: {\n        'class': 'mat-chip-edit-input',\n        'role': 'textbox',\n        'tabindex': '-1',\n        'contenteditable': 'true'\n      }\n    }]\n  }], () => [], null);\n})();\n\n/**\n * An extension of the MatChip component used with MatChipGrid and\n * the matChipInputFor directive.\n */\nclass MatChipRow extends MatChip {\n  basicChipAttrName = 'mat-basic-chip-row';\n  /**\n   * The editing action has to be triggered in a timeout. While we're waiting on it, a blur\n   * event might occur which will interrupt the editing. This flag is used to avoid interruptions\n   * while the editing action is being initialized.\n   */\n  _editStartPending = false;\n  editable = false;\n  /** Emitted when the chip is edited. */\n  edited = new EventEmitter();\n  /** The default chip edit input that is used if none is projected into this chip row. */\n  defaultEditInput;\n  /** The projected chip edit input. */\n  contentEditInput;\n  _isEditing = false;\n  constructor() {\n    super();\n    this.role = 'row';\n    this._onBlur.pipe(takeUntil(this.destroyed)).subscribe(() => {\n      if (this._isEditing && !this._editStartPending) {\n        this._onEditFinish();\n      }\n    });\n  }\n  _hasTrailingIcon() {\n    // The trailing icon is hidden while editing.\n    return !this._isEditing && super._hasTrailingIcon();\n  }\n  /** Sends focus to the first gridcell when the user clicks anywhere inside the chip. */\n  _handleFocus() {\n    if (!this._isEditing && !this.disabled) {\n      this.focus();\n    }\n  }\n  _handleKeydown(event) {\n    if (event.keyCode === ENTER && !this.disabled) {\n      if (this._isEditing) {\n        event.preventDefault();\n        this._onEditFinish();\n      } else if (this.editable) {\n        this._startEditing(event);\n      }\n    } else if (this._isEditing) {\n      // Stop the event from reaching the chip set in order to avoid navigating.\n      event.stopPropagation();\n    } else {\n      super._handleKeydown(event);\n    }\n  }\n  _handleDoubleclick(event) {\n    if (!this.disabled && this.editable) {\n      this._startEditing(event);\n    }\n  }\n  _startEditing(event) {\n    if (!this.primaryAction || this.removeIcon && this._getSourceAction(event.target) === this.removeIcon) {\n      return;\n    }\n    // The value depends on the DOM so we need to extract it before we flip the flag.\n    const value = this.value;\n    this._isEditing = this._editStartPending = true;\n    // Defer initializing the input until after it has been added to the DOM.\n    afterNextRender(() => {\n      this._getEditInput().initialize(value);\n      this._editStartPending = false;\n    }, {\n      injector: this._injector\n    });\n  }\n  _onEditFinish() {\n    this._isEditing = this._editStartPending = false;\n    this.edited.emit({\n      chip: this,\n      value: this._getEditInput().getValue()\n    });\n    // If the edit input is still focused or focus was returned to the body after it was destroyed,\n    // return focus to the chip contents.\n    if (this._document.activeElement === this._getEditInput().getNativeElement() || this._document.activeElement === this._document.body) {\n      this.primaryAction.focus();\n    }\n  }\n  _isRippleDisabled() {\n    return super._isRippleDisabled() || this._isEditing;\n  }\n  /**\n   * Gets the projected chip edit input, or the default input if none is projected in. One of these\n   * two values is guaranteed to be defined.\n   */\n  _getEditInput() {\n    return this.contentEditInput || this.defaultEditInput;\n  }\n  static ɵfac = function MatChipRow_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatChipRow)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatChipRow,\n    selectors: [[\"mat-chip-row\"], [\"\", \"mat-chip-row\", \"\"], [\"mat-basic-chip-row\"], [\"\", \"mat-basic-chip-row\", \"\"]],\n    contentQueries: function MatChipRow_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MatChipEditInput, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.contentEditInput = _t.first);\n      }\n    },\n    viewQuery: function MatChipRow_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(MatChipEditInput, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.defaultEditInput = _t.first);\n      }\n    },\n    hostAttrs: [1, \"mat-mdc-chip\", \"mat-mdc-chip-row\", \"mdc-evolution-chip\"],\n    hostVars: 27,\n    hostBindings: function MatChipRow_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"focus\", function MatChipRow_focus_HostBindingHandler() {\n          return ctx._handleFocus();\n        })(\"dblclick\", function MatChipRow_dblclick_HostBindingHandler($event) {\n          return ctx._handleDoubleclick($event);\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵhostProperty(\"id\", ctx.id);\n        i0.ɵɵattribute(\"tabindex\", ctx.disabled ? null : -1)(\"aria-label\", null)(\"aria-description\", null)(\"role\", ctx.role);\n        i0.ɵɵclassProp(\"mat-mdc-chip-with-avatar\", ctx.leadingIcon)(\"mat-mdc-chip-disabled\", ctx.disabled)(\"mat-mdc-chip-editing\", ctx._isEditing)(\"mat-mdc-chip-editable\", ctx.editable)(\"mdc-evolution-chip--disabled\", ctx.disabled)(\"mdc-evolution-chip--with-trailing-action\", ctx._hasTrailingIcon())(\"mdc-evolution-chip--with-primary-graphic\", ctx.leadingIcon)(\"mdc-evolution-chip--with-primary-icon\", ctx.leadingIcon)(\"mdc-evolution-chip--with-avatar\", ctx.leadingIcon)(\"mat-mdc-chip-highlighted\", ctx.highlighted)(\"mat-mdc-chip-with-trailing-icon\", ctx._hasTrailingIcon());\n      }\n    },\n    inputs: {\n      editable: \"editable\"\n    },\n    outputs: {\n      edited: \"edited\"\n    },\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MatChip,\n      useExisting: MatChipRow\n    }, {\n      provide: MAT_CHIP,\n      useExisting: MatChipRow\n    }]), i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c4,\n    decls: 10,\n    vars: 9,\n    consts: [[1, \"mat-mdc-chip-focus-overlay\"], [\"role\", \"gridcell\", \"matChipAction\", \"\", 1, \"mdc-evolution-chip__cell\", \"mdc-evolution-chip__cell--primary\", 3, \"disabled\"], [1, \"mdc-evolution-chip__graphic\", \"mat-mdc-chip-graphic\"], [1, \"mdc-evolution-chip__text-label\", \"mat-mdc-chip-action-label\"], [\"aria-hidden\", \"true\", 1, \"mat-mdc-chip-primary-focus-indicator\", \"mat-focus-indicator\"], [\"role\", \"gridcell\", 1, \"mdc-evolution-chip__cell\", \"mdc-evolution-chip__cell--trailing\"], [1, \"cdk-visually-hidden\", 3, \"id\"], [\"matChipEditInput\", \"\"]],\n    template: function MatChipRow_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef(_c3);\n        i0.ɵɵtemplate(0, MatChipRow_Conditional_0_Template, 1, 0, \"span\", 0);\n        i0.ɵɵelementStart(1, \"span\", 1);\n        i0.ɵɵtemplate(2, MatChipRow_Conditional_2_Template, 2, 0, \"span\", 2);\n        i0.ɵɵelementStart(3, \"span\", 3);\n        i0.ɵɵtemplate(4, MatChipRow_Conditional_4_Template, 2, 1)(5, MatChipRow_Conditional_5_Template, 1, 0);\n        i0.ɵɵelement(6, \"span\", 4);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(7, MatChipRow_Conditional_7_Template, 2, 0, \"span\", 5);\n        i0.ɵɵelementStart(8, \"span\", 6);\n        i0.ɵɵtext(9);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵconditional(!ctx._isEditing ? 0 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"disabled\", ctx.disabled);\n        i0.ɵɵattribute(\"aria-label\", ctx.ariaLabel)(\"aria-describedby\", ctx._ariaDescriptionId);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(ctx.leadingIcon ? 2 : -1);\n        i0.ɵɵadvance(2);\n        i0.ɵɵconditional(ctx._isEditing ? 4 : 5);\n        i0.ɵɵadvance(3);\n        i0.ɵɵconditional(ctx._hasTrailingIcon() ? 7 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"id\", ctx._ariaDescriptionId);\n        i0.ɵɵadvance();\n        i0.ɵɵtextInterpolate(ctx.ariaDescription);\n      }\n    },\n    dependencies: [MatChipAction, MatChipEditInput],\n    styles: [_c2],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatChipRow, [{\n    type: Component,\n    args: [{\n      selector: 'mat-chip-row, [mat-chip-row], mat-basic-chip-row, [mat-basic-chip-row]',\n      host: {\n        'class': 'mat-mdc-chip mat-mdc-chip-row mdc-evolution-chip',\n        '[class.mat-mdc-chip-with-avatar]': 'leadingIcon',\n        '[class.mat-mdc-chip-disabled]': 'disabled',\n        '[class.mat-mdc-chip-editing]': '_isEditing',\n        '[class.mat-mdc-chip-editable]': 'editable',\n        '[class.mdc-evolution-chip--disabled]': 'disabled',\n        '[class.mdc-evolution-chip--with-trailing-action]': '_hasTrailingIcon()',\n        '[class.mdc-evolution-chip--with-primary-graphic]': 'leadingIcon',\n        '[class.mdc-evolution-chip--with-primary-icon]': 'leadingIcon',\n        '[class.mdc-evolution-chip--with-avatar]': 'leadingIcon',\n        '[class.mat-mdc-chip-highlighted]': 'highlighted',\n        '[class.mat-mdc-chip-with-trailing-icon]': '_hasTrailingIcon()',\n        '[id]': 'id',\n        // Has to have a negative tabindex in order to capture\n        // focus and redirect it to the primary action.\n        '[attr.tabindex]': 'disabled ? null : -1',\n        '[attr.aria-label]': 'null',\n        '[attr.aria-description]': 'null',\n        '[attr.role]': 'role',\n        '(focus)': '_handleFocus()',\n        '(dblclick)': '_handleDoubleclick($event)'\n      },\n      providers: [{\n        provide: MatChip,\n        useExisting: MatChipRow\n      }, {\n        provide: MAT_CHIP,\n        useExisting: MatChipRow\n      }],\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      imports: [MatChipAction, MatChipEditInput],\n      template: \"@if (!_isEditing) {\\n  <span class=\\\"mat-mdc-chip-focus-overlay\\\"></span>\\n}\\n\\n<span class=\\\"mdc-evolution-chip__cell mdc-evolution-chip__cell--primary\\\" role=\\\"gridcell\\\"\\n    matChipAction\\n    [disabled]=\\\"disabled\\\"\\n    [attr.aria-label]=\\\"ariaLabel\\\"\\n    [attr.aria-describedby]=\\\"_ariaDescriptionId\\\">\\n  @if (leadingIcon) {\\n    <span class=\\\"mdc-evolution-chip__graphic mat-mdc-chip-graphic\\\">\\n      <ng-content select=\\\"mat-chip-avatar, [matChipAvatar]\\\"></ng-content>\\n    </span>\\n  }\\n\\n  <span class=\\\"mdc-evolution-chip__text-label mat-mdc-chip-action-label\\\">\\n    @if (_isEditing) {\\n      @if (contentEditInput) {\\n        <ng-content select=\\\"[matChipEditInput]\\\"></ng-content>\\n      } @else {\\n        <span matChipEditInput></span>\\n      }\\n    } @else {\\n      <ng-content></ng-content>\\n    }\\n\\n    <span class=\\\"mat-mdc-chip-primary-focus-indicator mat-focus-indicator\\\" aria-hidden=\\\"true\\\"></span>\\n  </span>\\n</span>\\n\\n@if (_hasTrailingIcon()) {\\n  <span\\n    class=\\\"mdc-evolution-chip__cell mdc-evolution-chip__cell--trailing\\\"\\n    role=\\\"gridcell\\\">\\n    <ng-content select=\\\"mat-chip-trailing-icon,[matChipRemove],[matChipTrailingIcon]\\\"></ng-content>\\n  </span>\\n}\\n\\n<span class=\\\"cdk-visually-hidden\\\" [id]=\\\"_ariaDescriptionId\\\">{{ariaDescription}}</span>\\n\",\n      styles: [\".mdc-evolution-chip,.mdc-evolution-chip__cell,.mdc-evolution-chip__action{display:inline-flex;align-items:center}.mdc-evolution-chip{position:relative;max-width:100%}.mdc-evolution-chip__cell,.mdc-evolution-chip__action{height:100%}.mdc-evolution-chip__cell--primary{flex-basis:100%;overflow-x:hidden}.mdc-evolution-chip__cell--trailing{flex:1 0 auto}.mdc-evolution-chip__action{align-items:center;background:none;border:none;box-sizing:content-box;cursor:pointer;display:inline-flex;justify-content:center;outline:none;padding:0;text-decoration:none;color:inherit}.mdc-evolution-chip__action--presentational{cursor:auto}.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{pointer-events:none}@media(forced-colors: active){.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{forced-color-adjust:none}}.mdc-evolution-chip__action--primary{font:inherit;letter-spacing:inherit;white-space:inherit;overflow-x:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary::before{border-width:var(--mdc-chip-outline-width, 1px);border-radius:var(--mdc-chip-container-shape-radius, 8px);box-sizing:border-box;content:\\\"\\\";height:100%;left:0;position:absolute;pointer-events:none;top:0;width:100%;z-index:1;border-style:solid}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--primary::before{border-color:var(--mdc-chip-outline-color, var(--mat-sys-outline))}.mdc-evolution-chip__action--primary:not(.mdc-evolution-chip__action--presentational):not(.mdc-ripple-upgraded):focus::before{border-color:var(--mdc-chip-focus-outline-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--primary::before{border-color:var(--mdc-chip-disabled-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__action--primary::before{border-width:var(--mdc-chip-flat-selected-outline-width, 0)}.mat-mdc-basic-chip .mdc-evolution-chip__action--primary{font:inherit}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip__action--trailing{position:relative;overflow:visible}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--trailing{color:var(--mdc-chip-with-trailing-icon-trailing-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--trailing{color:var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip__text-label{-webkit-user-select:none;user-select:none;white-space:nowrap;text-overflow:ellipsis;overflow:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__text-label{font-family:var(--mdc-chip-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mdc-chip-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mdc-chip-label-text-size, var(--mat-sys-label-large-size));font-weight:var(--mdc-chip-label-text-weight, var(--mat-sys-label-large-weight));letter-spacing:var(--mdc-chip-label-text-tracking, var(--mat-sys-label-large-tracking))}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mdc-chip-label-text-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mdc-chip-selected-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label,.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label{color:var(--mdc-chip-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-evolution-chip__graphic{align-items:center;display:inline-flex;justify-content:center;overflow:hidden;pointer-events:none;position:relative;flex:1 0 auto}.mat-mdc-standard-chip .mdc-evolution-chip__graphic{width:var(--mdc-chip-with-avatar-avatar-size, 24px);height:var(--mdc-chip-with-avatar-avatar-size, 24px);font-size:var(--mdc-chip-with-avatar-avatar-size, 24px)}.mdc-evolution-chip--selecting .mdc-evolution-chip__graphic{transition:width 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selectable:not(.mdc-evolution-chip--selected):not(.mdc-evolution-chip--with-primary-icon) .mdc-evolution-chip__graphic{width:0}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mdc-evolution-chip__checkmark{position:absolute;opacity:0;top:50%;left:50%;height:20px;width:20px}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__checkmark{color:var(--mdc-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__checkmark{color:var(--mdc-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark{transition:transform 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1);transform:translate(-75%, -50%)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{transform:translate(-50%, -50%);opacity:1}.mdc-evolution-chip__checkmark-svg{display:block}.mdc-evolution-chip__checkmark-path{stroke-width:2px;stroke-dasharray:29.7833385;stroke-dashoffset:29.7833385;stroke:currentColor}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark-path{transition:stroke-dashoffset 150ms 45ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark-path{stroke-dashoffset:0}@media(forced-colors: active){.mdc-evolution-chip__checkmark-path{stroke:CanvasText !important}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--trailing{height:18px;width:18px;font-size:18px}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove{opacity:calc(var(--mat-chip-trailing-action-opacity, 1)*var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove:focus{opacity:calc(var(--mat-chip-trailing-action-focus-opacity, 1)*var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mat-mdc-standard-chip{border-radius:var(--mdc-chip-container-shape-radius, 8px);height:var(--mdc-chip-container-height, 32px)}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled){background-color:var(--mdc-chip-elevated-container-color, transparent)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{background-color:var(--mdc-chip-elevated-disabled-container-color)}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled){background-color:var(--mdc-chip-elevated-selected-container-color, var(--mat-sys-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled{background-color:var(--mdc-chip-flat-disabled-selected-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}@media(forced-colors: active){.mat-mdc-standard-chip{outline:solid 1px}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--primary{border-radius:var(--mdc-chip-with-avatar-avatar-shape-radius, 24px);width:var(--mdc-chip-with-icon-icon-size, 18px);height:var(--mdc-chip-with-icon-icon-size, 18px);font-size:var(--mdc-chip-with-icon-icon-size, 18px)}.mdc-evolution-chip--selected .mdc-evolution-chip__icon--primary{opacity:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__icon--primary{color:var(--mdc-chip-with-icon-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--primary{color:var(--mdc-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-highlighted{--mdc-chip-with-icon-icon-color:var(--mdc-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container));--mdc-chip-elevated-container-color:var(--mdc-chip-elevated-selected-container-color, var(--mat-sys-secondary-container));--mdc-chip-label-text-color:var(--mdc-chip-selected-label-text-color, var(--mat-sys-on-secondary-container));--mdc-chip-outline-width:var(--mdc-chip-flat-selected-outline-width, 0)}.mat-mdc-chip-focus-overlay{background:var(--mdc-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-selected .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-chip:hover .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-hover-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mdc-chip-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-focus-overlay .mat-mdc-chip-selected:hover,.mat-mdc-chip-highlighted:hover .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-hover-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mdc-chip-selected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mdc-chip-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected.cdk-focused .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mdc-chip-selected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-evolution-chip--disabled:not(.mdc-evolution-chip--selected) .mat-mdc-chip-avatar{opacity:var(--mdc-chip-with-avatar-disabled-avatar-opacity, 0.38)}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{opacity:var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38)}.mdc-evolution-chip--disabled.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{opacity:var(--mdc-chip-with-icon-disabled-icon-opacity, 0.38)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{opacity:var(--mat-chip-disabled-container-opacity, 1)}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-trailing-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-remove{opacity:var(--mat-chip-trailing-action-opacity, 1)}.mat-mdc-chip-remove:focus{opacity:var(--mat-chip-trailing-action-focus-opacity, 1)}.mat-mdc-chip-remove::after{background-color:var(--mat-chip-trailing-action-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-remove:hover::after{opacity:var(--mat-chip-trailing-action-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-remove:focus::after{opacity:var(--mat-chip-trailing-action-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected .mat-mdc-chip-remove::after,.mat-mdc-chip-highlighted .mat-mdc-chip-remove::after{background-color:var(--mat-chip-selected-trailing-action-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-standard-chip .mdc-evolution-chip__cell--primary,.mat-mdc-standard-chip .mdc-evolution-chip__action--primary,.mat-mdc-standard-chip .mat-mdc-chip-action-label{overflow:visible}.mat-mdc-standard-chip .mat-mdc-chip-graphic,.mat-mdc-standard-chip .mat-mdc-chip-trailing-icon{box-sizing:content-box}.mat-mdc-standard-chip._mat-animation-noopable,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__graphic,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark-path{transition-duration:1ms;animation-duration:1ms}.mat-mdc-chip-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;opacity:0;border-radius:inherit;transition:opacity 150ms linear}._mat-animation-noopable .mat-mdc-chip-focus-overlay{transition:none}.mat-mdc-basic-chip .mat-mdc-chip-focus-overlay{display:none}.mat-mdc-chip .mat-ripple.mat-mdc-chip-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-chip-avatar{text-align:center;line-height:1;color:var(--mdc-chip-with-icon-icon-color, currentColor)}.mat-mdc-chip{position:relative;z-index:0}.mat-mdc-chip-action-label{text-align:left;z-index:1}[dir=rtl] .mat-mdc-chip-action-label{text-align:right}.mat-mdc-chip.mdc-evolution-chip--with-trailing-action .mat-mdc-chip-action-label{position:relative}.mat-mdc-chip-action-label .mat-mdc-chip-primary-focus-indicator{position:absolute;top:0;right:0;bottom:0;left:0;pointer-events:none}.mat-mdc-chip-action-label .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-mdc-chip-remove::before{margin:calc(var(--mat-focus-indicator-border-width, 3px)*-1);left:8px;right:8px}.mat-mdc-chip-remove::after{content:\\\"\\\";display:block;opacity:0;position:absolute;top:-3px;bottom:-3px;left:5px;right:5px;border-radius:50%;box-sizing:border-box;padding:12px;margin:-12px;background-clip:content-box}.mat-mdc-chip-remove .mat-icon{width:18px;height:18px;font-size:18px;box-sizing:content-box}.mat-chip-edit-input{cursor:text;display:inline-block;color:inherit;outline:0}@media(forced-colors: active){.mat-mdc-chip-selected:not(.mat-mdc-chip-multiple){outline-width:3px}}.mat-mdc-chip-action:focus .mat-focus-indicator::before{content:\\\"\\\"}.mdc-evolution-chip__icon,.mat-mdc-chip-remove .mat-icon{min-height:fit-content}\\n\"]\n    }]\n  }], () => [], {\n    editable: [{\n      type: Input\n    }],\n    edited: [{\n      type: Output\n    }],\n    defaultEditInput: [{\n      type: ViewChild,\n      args: [MatChipEditInput]\n    }],\n    contentEditInput: [{\n      type: ContentChild,\n      args: [MatChipEditInput]\n    }]\n  });\n})();\n\n/**\n * Basic container component for the MatChip component.\n *\n * Extended by MatChipListbox and MatChipGrid for different interaction patterns.\n */\nclass MatChipSet {\n  _elementRef = inject(ElementRef);\n  _changeDetectorRef = inject(ChangeDetectorRef);\n  _dir = inject(Directionality, {\n    optional: true\n  });\n  /** Index of the last destroyed chip that had focus. */\n  _lastDestroyedFocusedChipIndex = null;\n  /** Used to manage focus within the chip list. */\n  _keyManager;\n  /** Subject that emits when the component has been destroyed. */\n  _destroyed = new Subject();\n  /** Role to use if it hasn't been overwritten by the user. */\n  _defaultRole = 'presentation';\n  /** Combined stream of all of the child chips' focus events. */\n  get chipFocusChanges() {\n    return this._getChipStream(chip => chip._onFocus);\n  }\n  /** Combined stream of all of the child chips' destroy events. */\n  get chipDestroyedChanges() {\n    return this._getChipStream(chip => chip.destroyed);\n  }\n  /** Combined stream of all of the child chips' remove events. */\n  get chipRemovedChanges() {\n    return this._getChipStream(chip => chip.removed);\n  }\n  /** Whether the chip set is disabled. */\n  get disabled() {\n    return this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = value;\n    this._syncChipsState();\n  }\n  _disabled = false;\n  /** Whether the chip list contains chips or not. */\n  get empty() {\n    return !this._chips || this._chips.length === 0;\n  }\n  /** The ARIA role applied to the chip set. */\n  get role() {\n    if (this._explicitRole) {\n      return this._explicitRole;\n    }\n    return this.empty ? null : this._defaultRole;\n  }\n  /** Tabindex of the chip set. */\n  tabIndex = 0;\n  set role(value) {\n    this._explicitRole = value;\n  }\n  _explicitRole = null;\n  /** Whether any of the chips inside of this chip-set has focus. */\n  get focused() {\n    return this._hasFocusedChip();\n  }\n  /** The chips that are part of this chip set. */\n  _chips;\n  /** Flat list of all the actions contained within the chips. */\n  _chipActions = new QueryList();\n  constructor() {}\n  ngAfterViewInit() {\n    this._setUpFocusManagement();\n    this._trackChipSetChanges();\n    this._trackDestroyedFocusedChip();\n  }\n  ngOnDestroy() {\n    this._keyManager?.destroy();\n    this._chipActions.destroy();\n    this._destroyed.next();\n    this._destroyed.complete();\n  }\n  /** Checks whether any of the chips is focused. */\n  _hasFocusedChip() {\n    return this._chips && this._chips.some(chip => chip._hasFocus());\n  }\n  /** Syncs the chip-set's state with the individual chips. */\n  _syncChipsState() {\n    this._chips?.forEach(chip => {\n      chip._chipListDisabled = this._disabled;\n      chip._changeDetectorRef.markForCheck();\n    });\n  }\n  /** Dummy method for subclasses to override. Base chip set cannot be focused. */\n  focus() {}\n  /** Handles keyboard events on the chip set. */\n  _handleKeydown(event) {\n    if (this._originatesFromChip(event)) {\n      this._keyManager.onKeydown(event);\n    }\n  }\n  /**\n   * Utility to ensure all indexes are valid.\n   *\n   * @param index The index to be checked.\n   * @returns True if the index is valid for our list of chips.\n   */\n  _isValidIndex(index) {\n    return index >= 0 && index < this._chips.length;\n  }\n  /**\n   * Removes the `tabindex` from the chip set and resets it back afterwards, allowing the\n   * user to tab out of it. This prevents the set from capturing focus and redirecting\n   * it back to the first chip, creating a focus trap, if it user tries to tab away.\n   */\n  _allowFocusEscape() {\n    const previous = this._elementRef.nativeElement.tabIndex;\n    if (previous !== -1) {\n      // Set the tabindex directly on the element, instead of going through\n      // the data binding, because we aren't guaranteed that change detection\n      // will run quickly enough to allow focus to escape.\n      this._elementRef.nativeElement.tabIndex = -1;\n      // Note that this needs to be a `setTimeout`, because a `Promise.resolve`\n      // doesn't allow enough time for the focus to escape.\n      setTimeout(() => this._elementRef.nativeElement.tabIndex = previous);\n    }\n  }\n  /**\n   * Gets a stream of events from all the chips within the set.\n   * The stream will automatically incorporate any newly-added chips.\n   */\n  _getChipStream(mappingFunction) {\n    return this._chips.changes.pipe(startWith(null), switchMap(() => merge(...this._chips.map(mappingFunction))));\n  }\n  /** Checks whether an event comes from inside a chip element. */\n  _originatesFromChip(event) {\n    let currentElement = event.target;\n    while (currentElement && currentElement !== this._elementRef.nativeElement) {\n      if (currentElement.classList.contains('mat-mdc-chip')) {\n        return true;\n      }\n      currentElement = currentElement.parentElement;\n    }\n    return false;\n  }\n  /** Sets up the chip set's focus management logic. */\n  _setUpFocusManagement() {\n    // Create a flat `QueryList` containing the actions of all of the chips.\n    // This allows us to navigate both within the chip and move to the next/previous\n    // one using the existing `ListKeyManager`.\n    this._chips.changes.pipe(startWith(this._chips)).subscribe(chips => {\n      const actions = [];\n      chips.forEach(chip => chip._getActions().forEach(action => actions.push(action)));\n      this._chipActions.reset(actions);\n      this._chipActions.notifyOnChanges();\n    });\n    this._keyManager = new FocusKeyManager(this._chipActions).withVerticalOrientation().withHorizontalOrientation(this._dir ? this._dir.value : 'ltr').withHomeAndEnd().skipPredicate(action => this._skipPredicate(action));\n    // Keep the manager active index in sync so that navigation picks\n    // up from the current chip if the user clicks into the list directly.\n    this.chipFocusChanges.pipe(takeUntil(this._destroyed)).subscribe(({\n      chip\n    }) => {\n      const action = chip._getSourceAction(document.activeElement);\n      if (action) {\n        this._keyManager.updateActiveItem(action);\n      }\n    });\n    this._dir?.change.pipe(takeUntil(this._destroyed)).subscribe(direction => this._keyManager.withHorizontalOrientation(direction));\n  }\n  /**\n   * Determines if key manager should avoid putting a given chip action in the tab index. Skip\n   * non-interactive and disabled actions since the user can't do anything with them.\n   */\n  _skipPredicate(action) {\n    // Skip chips that the user cannot interact with. `mat-chip-set` does not permit focusing disabled\n    // chips.\n    return !action.isInteractive || action.disabled;\n  }\n  /** Listens to changes in the chip set and syncs up the state of the individual chips. */\n  _trackChipSetChanges() {\n    this._chips.changes.pipe(startWith(null), takeUntil(this._destroyed)).subscribe(() => {\n      if (this.disabled) {\n        // Since this happens after the content has been\n        // checked, we need to defer it to the next tick.\n        Promise.resolve().then(() => this._syncChipsState());\n      }\n      this._redirectDestroyedChipFocus();\n    });\n  }\n  /** Starts tracking the destroyed chips in order to capture the focused one. */\n  _trackDestroyedFocusedChip() {\n    this.chipDestroyedChanges.pipe(takeUntil(this._destroyed)).subscribe(event => {\n      const chipArray = this._chips.toArray();\n      const chipIndex = chipArray.indexOf(event.chip);\n      // If the focused chip is destroyed, save its index so that we can move focus to the next\n      // chip. We only save the index here, rather than move the focus immediately, because we want\n      // to wait until the chip is removed from the chip list before focusing the next one. This\n      // allows us to keep focus on the same index if the chip gets swapped out.\n      if (this._isValidIndex(chipIndex) && event.chip._hasFocus()) {\n        this._lastDestroyedFocusedChipIndex = chipIndex;\n      }\n    });\n  }\n  /**\n   * Finds the next appropriate chip to move focus to,\n   * if the currently-focused chip is destroyed.\n   */\n  _redirectDestroyedChipFocus() {\n    if (this._lastDestroyedFocusedChipIndex == null) {\n      return;\n    }\n    if (this._chips.length) {\n      const newIndex = Math.min(this._lastDestroyedFocusedChipIndex, this._chips.length - 1);\n      const chipToFocus = this._chips.toArray()[newIndex];\n      if (chipToFocus.disabled) {\n        // If we're down to one disabled chip, move focus back to the set.\n        if (this._chips.length === 1) {\n          this.focus();\n        } else {\n          this._keyManager.setPreviousItemActive();\n        }\n      } else {\n        chipToFocus.focus();\n      }\n    } else {\n      this.focus();\n    }\n    this._lastDestroyedFocusedChipIndex = null;\n  }\n  static ɵfac = function MatChipSet_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatChipSet)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatChipSet,\n    selectors: [[\"mat-chip-set\"]],\n    contentQueries: function MatChipSet_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MatChip, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._chips = _t);\n      }\n    },\n    hostAttrs: [1, \"mat-mdc-chip-set\", \"mdc-evolution-chip-set\"],\n    hostVars: 1,\n    hostBindings: function MatChipSet_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"keydown\", function MatChipSet_keydown_HostBindingHandler($event) {\n          return ctx._handleKeydown($event);\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵattribute(\"role\", ctx.role);\n      }\n    },\n    inputs: {\n      disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n      role: \"role\",\n      tabIndex: [2, \"tabIndex\", \"tabIndex\", value => value == null ? 0 : numberAttribute(value)]\n    },\n    ngContentSelectors: _c5,\n    decls: 2,\n    vars: 0,\n    consts: [[\"role\", \"presentation\", 1, \"mdc-evolution-chip-set__chips\"]],\n    template: function MatChipSet_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵprojection(1);\n        i0.ɵɵelementEnd();\n      }\n    },\n    styles: [\".mat-mdc-chip-set{display:flex}.mat-mdc-chip-set:focus{outline:none}.mat-mdc-chip-set .mdc-evolution-chip-set__chips{min-width:100%;margin-left:-8px;margin-right:0}.mat-mdc-chip-set .mdc-evolution-chip{margin:4px 0 4px 8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip-set__chips{margin-left:0;margin-right:-8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip{margin-left:0;margin-right:8px}.mdc-evolution-chip-set__chips{display:flex;flex-flow:wrap;min-width:0}.mat-mdc-chip-set-stacked{flex-direction:column;align-items:flex-start}.mat-mdc-chip-set-stacked .mat-mdc-chip{width:100%}.mat-mdc-chip-set-stacked .mdc-evolution-chip__graphic{flex-grow:0}.mat-mdc-chip-set-stacked .mdc-evolution-chip__action--primary{flex-basis:100%;justify-content:start}input.mat-mdc-chip-input{flex:1 0 150px;margin-left:8px}[dir=rtl] input.mat-mdc-chip-input{margin-left:0;margin-right:8px}\\n\"],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatChipSet, [{\n    type: Component,\n    args: [{\n      selector: 'mat-chip-set',\n      template: `\n    <div class=\"mdc-evolution-chip-set__chips\" role=\"presentation\">\n      <ng-content></ng-content>\n    </div>\n  `,\n      host: {\n        'class': 'mat-mdc-chip-set mdc-evolution-chip-set',\n        '(keydown)': '_handleKeydown($event)',\n        '[attr.role]': 'role'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      styles: [\".mat-mdc-chip-set{display:flex}.mat-mdc-chip-set:focus{outline:none}.mat-mdc-chip-set .mdc-evolution-chip-set__chips{min-width:100%;margin-left:-8px;margin-right:0}.mat-mdc-chip-set .mdc-evolution-chip{margin:4px 0 4px 8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip-set__chips{margin-left:0;margin-right:-8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip{margin-left:0;margin-right:8px}.mdc-evolution-chip-set__chips{display:flex;flex-flow:wrap;min-width:0}.mat-mdc-chip-set-stacked{flex-direction:column;align-items:flex-start}.mat-mdc-chip-set-stacked .mat-mdc-chip{width:100%}.mat-mdc-chip-set-stacked .mdc-evolution-chip__graphic{flex-grow:0}.mat-mdc-chip-set-stacked .mdc-evolution-chip__action--primary{flex-basis:100%;justify-content:start}input.mat-mdc-chip-input{flex:1 0 150px;margin-left:8px}[dir=rtl] input.mat-mdc-chip-input{margin-left:0;margin-right:8px}\\n\"]\n    }]\n  }], () => [], {\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    role: [{\n      type: Input\n    }],\n    tabIndex: [{\n      type: Input,\n      args: [{\n        transform: value => value == null ? 0 : numberAttribute(value)\n      }]\n    }],\n    _chips: [{\n      type: ContentChildren,\n      args: [MatChip, {\n        // We need to use `descendants: true`, because Ivy will no longer match\n        // indirect descendants if it's left as false.\n        descendants: true\n      }]\n    }]\n  });\n})();\n\n/** Change event object that is emitted when the chip listbox value has changed. */\nclass MatChipListboxChange {\n  source;\n  value;\n  constructor(/** Chip listbox that emitted the event. */\n  source, /** Value of the chip listbox when the event was emitted. */\n  value) {\n    this.source = source;\n    this.value = value;\n  }\n}\n/**\n * Provider Expression that allows mat-chip-listbox to register as a ControlValueAccessor.\n * This allows it to support [(ngModel)].\n * @docs-private\n */\nconst MAT_CHIP_LISTBOX_CONTROL_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => MatChipListbox),\n  multi: true\n};\n/**\n * An extension of the MatChipSet component that supports chip selection.\n * Used with MatChipOption chips.\n */\nclass MatChipListbox extends MatChipSet {\n  /**\n   * Function when touched. Set as part of ControlValueAccessor implementation.\n   * @docs-private\n   */\n  _onTouched = () => {};\n  /**\n   * Function when changed. Set as part of ControlValueAccessor implementation.\n   * @docs-private\n   */\n  _onChange = () => {};\n  // TODO: MDC uses `grid` here\n  _defaultRole = 'listbox';\n  /** Default chip options. */\n  _defaultOptions = inject(MAT_CHIPS_DEFAULT_OPTIONS, {\n    optional: true\n  });\n  /** Whether the user should be allowed to select multiple chips. */\n  get multiple() {\n    return this._multiple;\n  }\n  set multiple(value) {\n    this._multiple = value;\n    this._syncListboxProperties();\n  }\n  _multiple = false;\n  /** The array of selected chips inside the chip listbox. */\n  get selected() {\n    const selectedChips = this._chips.toArray().filter(chip => chip.selected);\n    return this.multiple ? selectedChips : selectedChips[0];\n  }\n  /** Orientation of the chip list. */\n  ariaOrientation = 'horizontal';\n  /**\n   * Whether or not this chip listbox is selectable.\n   *\n   * When a chip listbox is not selectable, the selected states for all\n   * the chips inside the chip listbox are always ignored.\n   */\n  get selectable() {\n    return this._selectable;\n  }\n  set selectable(value) {\n    this._selectable = value;\n    this._syncListboxProperties();\n  }\n  _selectable = true;\n  /**\n   * A function to compare the option values with the selected values. The first argument\n   * is a value from an option. The second is a value from the selection. A boolean\n   * should be returned.\n   */\n  compareWith = (o1, o2) => o1 === o2;\n  /** Whether this chip listbox is required. */\n  required = false;\n  /** Whether checkmark indicator for single-selection options is hidden. */\n  get hideSingleSelectionIndicator() {\n    return this._hideSingleSelectionIndicator;\n  }\n  set hideSingleSelectionIndicator(value) {\n    this._hideSingleSelectionIndicator = value;\n    this._syncListboxProperties();\n  }\n  _hideSingleSelectionIndicator = this._defaultOptions?.hideSingleSelectionIndicator ?? false;\n  /** Combined stream of all of the child chips' selection change events. */\n  get chipSelectionChanges() {\n    return this._getChipStream(chip => chip.selectionChange);\n  }\n  /** Combined stream of all of the child chips' blur events. */\n  get chipBlurChanges() {\n    return this._getChipStream(chip => chip._onBlur);\n  }\n  /** The value of the listbox, which is the combined value of the selected chips. */\n  get value() {\n    return this._value;\n  }\n  set value(value) {\n    if (this._chips && this._chips.length) {\n      this._setSelectionByValue(value, false);\n    }\n    this._value = value;\n  }\n  _value;\n  /** Event emitted when the selected chip listbox value has been changed by the user. */\n  change = new EventEmitter();\n  _chips = undefined;\n  ngAfterContentInit() {\n    this._chips.changes.pipe(startWith(null), takeUntil(this._destroyed)).subscribe(() => {\n      if (this.value !== undefined) {\n        Promise.resolve().then(() => {\n          this._setSelectionByValue(this.value, false);\n        });\n      }\n      // Update listbox selectable/multiple properties on chips\n      this._syncListboxProperties();\n    });\n    this.chipBlurChanges.pipe(takeUntil(this._destroyed)).subscribe(() => this._blur());\n    this.chipSelectionChanges.pipe(takeUntil(this._destroyed)).subscribe(event => {\n      if (!this.multiple) {\n        this._chips.forEach(chip => {\n          if (chip !== event.source) {\n            chip._setSelectedState(false, false, false);\n          }\n        });\n      }\n      if (event.isUserInput) {\n        this._propagateChanges();\n      }\n    });\n  }\n  /**\n   * Focuses the first selected chip in this chip listbox, or the first non-disabled chip when there\n   * are no selected chips.\n   */\n  focus() {\n    if (this.disabled) {\n      return;\n    }\n    const firstSelectedChip = this._getFirstSelectedChip();\n    if (firstSelectedChip && !firstSelectedChip.disabled) {\n      firstSelectedChip.focus();\n    } else if (this._chips.length > 0) {\n      this._keyManager.setFirstItemActive();\n    } else {\n      this._elementRef.nativeElement.focus();\n    }\n  }\n  /**\n   * Implemented as part of ControlValueAccessor.\n   * @docs-private\n   */\n  writeValue(value) {\n    if (value != null) {\n      this.value = value;\n    } else {\n      this.value = undefined;\n    }\n  }\n  /**\n   * Implemented as part of ControlValueAccessor.\n   * @docs-private\n   */\n  registerOnChange(fn) {\n    this._onChange = fn;\n  }\n  /**\n   * Implemented as part of ControlValueAccessor.\n   * @docs-private\n   */\n  registerOnTouched(fn) {\n    this._onTouched = fn;\n  }\n  /**\n   * Implemented as part of ControlValueAccessor.\n   * @docs-private\n   */\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n  }\n  /** Selects all chips with value. */\n  _setSelectionByValue(value, isUserInput = true) {\n    this._clearSelection();\n    if (Array.isArray(value)) {\n      value.forEach(currentValue => this._selectValue(currentValue, isUserInput));\n    } else {\n      this._selectValue(value, isUserInput);\n    }\n  }\n  /** When blurred, marks the field as touched when focus moved outside the chip listbox. */\n  _blur() {\n    if (!this.disabled) {\n      // Wait to see if focus moves to an individual chip.\n      setTimeout(() => {\n        if (!this.focused) {\n          this._markAsTouched();\n        }\n      });\n    }\n  }\n  _keydown(event) {\n    if (event.keyCode === TAB) {\n      super._allowFocusEscape();\n    }\n  }\n  /** Marks the field as touched */\n  _markAsTouched() {\n    this._onTouched();\n    this._changeDetectorRef.markForCheck();\n  }\n  /** Emits change event to set the model value. */\n  _propagateChanges() {\n    let valueToEmit = null;\n    if (Array.isArray(this.selected)) {\n      valueToEmit = this.selected.map(chip => chip.value);\n    } else {\n      valueToEmit = this.selected ? this.selected.value : undefined;\n    }\n    this._value = valueToEmit;\n    this.change.emit(new MatChipListboxChange(this, valueToEmit));\n    this._onChange(valueToEmit);\n    this._changeDetectorRef.markForCheck();\n  }\n  /**\n   * Deselects every chip in the listbox.\n   * @param skip Chip that should not be deselected.\n   */\n  _clearSelection(skip) {\n    this._chips.forEach(chip => {\n      if (chip !== skip) {\n        chip.deselect();\n      }\n    });\n  }\n  /**\n   * Finds and selects the chip based on its value.\n   * @returns Chip that has the corresponding value.\n   */\n  _selectValue(value, isUserInput) {\n    const correspondingChip = this._chips.find(chip => {\n      return chip.value != null && this.compareWith(chip.value, value);\n    });\n    if (correspondingChip) {\n      isUserInput ? correspondingChip.selectViaInteraction() : correspondingChip.select();\n    }\n    return correspondingChip;\n  }\n  /** Syncs the chip-listbox selection state with the individual chips. */\n  _syncListboxProperties() {\n    if (this._chips) {\n      // Defer setting the value in order to avoid the \"Expression\n      // has changed after it was checked\" errors from Angular.\n      Promise.resolve().then(() => {\n        this._chips.forEach(chip => {\n          chip._chipListMultiple = this.multiple;\n          chip.chipListSelectable = this._selectable;\n          chip._chipListHideSingleSelectionIndicator = this.hideSingleSelectionIndicator;\n          chip._changeDetectorRef.markForCheck();\n        });\n      });\n    }\n  }\n  /** Returns the first selected chip in this listbox, or undefined if no chips are selected. */\n  _getFirstSelectedChip() {\n    if (Array.isArray(this.selected)) {\n      return this.selected.length ? this.selected[0] : undefined;\n    } else {\n      return this.selected;\n    }\n  }\n  /**\n   * Determines if key manager should avoid putting a given chip action in the tab index. Skip\n   * non-interactive actions since the user can't do anything with them.\n   */\n  _skipPredicate(action) {\n    // Override the skip predicate in the base class to avoid skipping disabled chips. Allow\n    // disabled chip options to receive focus to align with WAI ARIA recommendation. Normally WAI\n    // ARIA's instructions are to exclude disabled items from the tab order, but it makes a few\n    // exceptions for compound widgets.\n    //\n    // From [Developing a Keyboard Interface](\n    // https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/):\n    //   \"For the following composite widget elements, keep them focusable when disabled: Options in a\n    //   Listbox...\"\n    return !action.isInteractive;\n  }\n  static ɵfac = /* @__PURE__ */(() => {\n    let ɵMatChipListbox_BaseFactory;\n    return function MatChipListbox_Factory(__ngFactoryType__) {\n      return (ɵMatChipListbox_BaseFactory || (ɵMatChipListbox_BaseFactory = i0.ɵɵgetInheritedFactory(MatChipListbox)))(__ngFactoryType__ || MatChipListbox);\n    };\n  })();\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatChipListbox,\n    selectors: [[\"mat-chip-listbox\"]],\n    contentQueries: function MatChipListbox_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MatChipOption, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._chips = _t);\n      }\n    },\n    hostAttrs: [1, \"mdc-evolution-chip-set\", \"mat-mdc-chip-listbox\"],\n    hostVars: 10,\n    hostBindings: function MatChipListbox_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"focus\", function MatChipListbox_focus_HostBindingHandler() {\n          return ctx.focus();\n        })(\"blur\", function MatChipListbox_blur_HostBindingHandler() {\n          return ctx._blur();\n        })(\"keydown\", function MatChipListbox_keydown_HostBindingHandler($event) {\n          return ctx._keydown($event);\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵhostProperty(\"tabIndex\", ctx.disabled || ctx.empty ? -1 : ctx.tabIndex);\n        i0.ɵɵattribute(\"role\", ctx.role)(\"aria-required\", ctx.role ? ctx.required : null)(\"aria-disabled\", ctx.disabled.toString())(\"aria-multiselectable\", ctx.multiple)(\"aria-orientation\", ctx.ariaOrientation);\n        i0.ɵɵclassProp(\"mat-mdc-chip-list-disabled\", ctx.disabled)(\"mat-mdc-chip-list-required\", ctx.required);\n      }\n    },\n    inputs: {\n      multiple: [2, \"multiple\", \"multiple\", booleanAttribute],\n      ariaOrientation: [0, \"aria-orientation\", \"ariaOrientation\"],\n      selectable: [2, \"selectable\", \"selectable\", booleanAttribute],\n      compareWith: \"compareWith\",\n      required: [2, \"required\", \"required\", booleanAttribute],\n      hideSingleSelectionIndicator: [2, \"hideSingleSelectionIndicator\", \"hideSingleSelectionIndicator\", booleanAttribute],\n      value: \"value\"\n    },\n    outputs: {\n      change: \"change\"\n    },\n    features: [i0.ɵɵProvidersFeature([MAT_CHIP_LISTBOX_CONTROL_VALUE_ACCESSOR]), i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c5,\n    decls: 2,\n    vars: 0,\n    consts: [[\"role\", \"presentation\", 1, \"mdc-evolution-chip-set__chips\"]],\n    template: function MatChipListbox_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵprojection(1);\n        i0.ɵɵelementEnd();\n      }\n    },\n    styles: [_c6],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatChipListbox, [{\n    type: Component,\n    args: [{\n      selector: 'mat-chip-listbox',\n      template: `\n    <div class=\"mdc-evolution-chip-set__chips\" role=\"presentation\">\n      <ng-content></ng-content>\n    </div>\n  `,\n      host: {\n        'class': 'mdc-evolution-chip-set mat-mdc-chip-listbox',\n        '[attr.role]': 'role',\n        '[tabIndex]': '(disabled || empty) ? -1 : tabIndex',\n        '[attr.aria-required]': 'role ? required : null',\n        '[attr.aria-disabled]': 'disabled.toString()',\n        '[attr.aria-multiselectable]': 'multiple',\n        '[attr.aria-orientation]': 'ariaOrientation',\n        '[class.mat-mdc-chip-list-disabled]': 'disabled',\n        '[class.mat-mdc-chip-list-required]': 'required',\n        '(focus)': 'focus()',\n        '(blur)': '_blur()',\n        '(keydown)': '_keydown($event)'\n      },\n      providers: [MAT_CHIP_LISTBOX_CONTROL_VALUE_ACCESSOR],\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      styles: [\".mat-mdc-chip-set{display:flex}.mat-mdc-chip-set:focus{outline:none}.mat-mdc-chip-set .mdc-evolution-chip-set__chips{min-width:100%;margin-left:-8px;margin-right:0}.mat-mdc-chip-set .mdc-evolution-chip{margin:4px 0 4px 8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip-set__chips{margin-left:0;margin-right:-8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip{margin-left:0;margin-right:8px}.mdc-evolution-chip-set__chips{display:flex;flex-flow:wrap;min-width:0}.mat-mdc-chip-set-stacked{flex-direction:column;align-items:flex-start}.mat-mdc-chip-set-stacked .mat-mdc-chip{width:100%}.mat-mdc-chip-set-stacked .mdc-evolution-chip__graphic{flex-grow:0}.mat-mdc-chip-set-stacked .mdc-evolution-chip__action--primary{flex-basis:100%;justify-content:start}input.mat-mdc-chip-input{flex:1 0 150px;margin-left:8px}[dir=rtl] input.mat-mdc-chip-input{margin-left:0;margin-right:8px}\\n\"]\n    }]\n  }], null, {\n    multiple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    ariaOrientation: [{\n      type: Input,\n      args: ['aria-orientation']\n    }],\n    selectable: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    compareWith: [{\n      type: Input\n    }],\n    required: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    hideSingleSelectionIndicator: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    value: [{\n      type: Input\n    }],\n    change: [{\n      type: Output\n    }],\n    _chips: [{\n      type: ContentChildren,\n      args: [MatChipOption, {\n        // We need to use `descendants: true`, because Ivy will no longer match\n        // indirect descendants if it's left as false.\n        descendants: true\n      }]\n    }]\n  });\n})();\n\n/** Change event object that is emitted when the chip grid value has changed. */\nclass MatChipGridChange {\n  source;\n  value;\n  constructor(/** Chip grid that emitted the event. */\n  source, /** Value of the chip grid when the event was emitted. */\n  value) {\n    this.source = source;\n    this.value = value;\n  }\n}\n/**\n * An extension of the MatChipSet component used with MatChipRow chips and\n * the matChipInputFor directive.\n */\nclass MatChipGrid extends MatChipSet {\n  ngControl = inject(NgControl, {\n    optional: true,\n    self: true\n  });\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  controlType = 'mat-chip-grid';\n  /** The chip input to add more chips */\n  _chipInput;\n  _defaultRole = 'grid';\n  _errorStateTracker;\n  /**\n   * List of element ids to propagate to the chipInput's aria-describedby attribute.\n   */\n  _ariaDescribedbyIds = [];\n  /**\n   * Function when touched. Set as part of ControlValueAccessor implementation.\n   * @docs-private\n   */\n  _onTouched = () => {};\n  /**\n   * Function when changed. Set as part of ControlValueAccessor implementation.\n   * @docs-private\n   */\n  _onChange = () => {};\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get disabled() {\n    return this.ngControl ? !!this.ngControl.disabled : this._disabled;\n  }\n  set disabled(value) {\n    this._disabled = value;\n    this._syncChipsState();\n    this.stateChanges.next();\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get id() {\n    return this._chipInput.id;\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get empty() {\n    return (!this._chipInput || this._chipInput.empty) && (!this._chips || this._chips.length === 0);\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get placeholder() {\n    return this._chipInput ? this._chipInput.placeholder : this._placeholder;\n  }\n  set placeholder(value) {\n    this._placeholder = value;\n    this.stateChanges.next();\n  }\n  _placeholder;\n  /** Whether any chips or the matChipInput inside of this chip-grid has focus. */\n  get focused() {\n    return this._chipInput.focused || this._hasFocusedChip();\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get required() {\n    return this._required ?? this.ngControl?.control?.hasValidator(Validators.required) ?? false;\n  }\n  set required(value) {\n    this._required = value;\n    this.stateChanges.next();\n  }\n  _required;\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get shouldLabelFloat() {\n    return !this.empty || this.focused;\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  get value() {\n    return this._value;\n  }\n  set value(value) {\n    this._value = value;\n  }\n  _value = [];\n  /** An object used to control when error messages are shown. */\n  get errorStateMatcher() {\n    return this._errorStateTracker.matcher;\n  }\n  set errorStateMatcher(value) {\n    this._errorStateTracker.matcher = value;\n  }\n  /** Combined stream of all of the child chips' blur events. */\n  get chipBlurChanges() {\n    return this._getChipStream(chip => chip._onBlur);\n  }\n  /** Emits when the chip grid value has been changed by the user. */\n  change = new EventEmitter();\n  /**\n   * Emits whenever the raw value of the chip-grid changes. This is here primarily\n   * to facilitate the two-way binding for the `value` input.\n   * @docs-private\n   */\n  valueChange = new EventEmitter();\n  _chips = undefined;\n  /**\n   * Emits whenever the component state changes and should cause the parent\n   * form-field to update. Implemented as part of `MatFormFieldControl`.\n   * @docs-private\n   */\n  stateChanges = new Subject();\n  /** Whether the chip grid is in an error state. */\n  get errorState() {\n    return this._errorStateTracker.errorState;\n  }\n  set errorState(value) {\n    this._errorStateTracker.errorState = value;\n  }\n  constructor() {\n    super();\n    const parentForm = inject(NgForm, {\n      optional: true\n    });\n    const parentFormGroup = inject(FormGroupDirective, {\n      optional: true\n    });\n    const defaultErrorStateMatcher = inject(ErrorStateMatcher);\n    if (this.ngControl) {\n      this.ngControl.valueAccessor = this;\n    }\n    this._errorStateTracker = new _ErrorStateTracker(defaultErrorStateMatcher, this.ngControl, parentFormGroup, parentForm, this.stateChanges);\n  }\n  ngAfterContentInit() {\n    this.chipBlurChanges.pipe(takeUntil(this._destroyed)).subscribe(() => {\n      this._blur();\n      this.stateChanges.next();\n    });\n    merge(this.chipFocusChanges, this._chips.changes).pipe(takeUntil(this._destroyed)).subscribe(() => this.stateChanges.next());\n  }\n  ngAfterViewInit() {\n    super.ngAfterViewInit();\n    if (!this._chipInput && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('mat-chip-grid must be used in combination with matChipInputFor.');\n    }\n  }\n  ngDoCheck() {\n    if (this.ngControl) {\n      // We need to re-evaluate this on every change detection cycle, because there are some\n      // error triggers that we can't subscribe to (e.g. parent form submissions). This means\n      // that whatever logic is in here has to be super lean or we risk destroying the performance.\n      this.updateErrorState();\n    }\n  }\n  ngOnDestroy() {\n    super.ngOnDestroy();\n    this.stateChanges.complete();\n  }\n  /** Associates an HTML input element with this chip grid. */\n  registerInput(inputElement) {\n    this._chipInput = inputElement;\n    this._chipInput.setDescribedByIds(this._ariaDescribedbyIds);\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  onContainerClick(event) {\n    if (!this.disabled && !this._originatesFromChip(event)) {\n      this.focus();\n    }\n  }\n  /**\n   * Focuses the first chip in this chip grid, or the associated input when there\n   * are no eligible chips.\n   */\n  focus() {\n    if (this.disabled || this._chipInput.focused) {\n      return;\n    }\n    if (!this._chips.length || this._chips.first.disabled) {\n      // Delay until the next tick, because this can cause a \"changed after checked\"\n      // error if the input does something on focus (e.g. opens an autocomplete).\n      Promise.resolve().then(() => this._chipInput.focus());\n    } else {\n      const activeItem = this._keyManager.activeItem;\n      if (activeItem) {\n        activeItem.focus();\n      } else {\n        this._keyManager.setFirstItemActive();\n      }\n    }\n    this.stateChanges.next();\n  }\n  /**\n   * Implemented as part of MatFormFieldControl.\n   * @docs-private\n   */\n  setDescribedByIds(ids) {\n    // We must keep this up to date to handle the case where ids are set\n    // before the chip input is registered.\n    this._ariaDescribedbyIds = ids;\n    this._chipInput?.setDescribedByIds(ids);\n  }\n  /**\n   * Implemented as part of ControlValueAccessor.\n   * @docs-private\n   */\n  writeValue(value) {\n    // The user is responsible for creating the child chips, so we just store the value.\n    this._value = value;\n  }\n  /**\n   * Implemented as part of ControlValueAccessor.\n   * @docs-private\n   */\n  registerOnChange(fn) {\n    this._onChange = fn;\n  }\n  /**\n   * Implemented as part of ControlValueAccessor.\n   * @docs-private\n   */\n  registerOnTouched(fn) {\n    this._onTouched = fn;\n  }\n  /**\n   * Implemented as part of ControlValueAccessor.\n   * @docs-private\n   */\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n    this.stateChanges.next();\n  }\n  /** Refreshes the error state of the chip grid. */\n  updateErrorState() {\n    this._errorStateTracker.updateErrorState();\n  }\n  /** When blurred, mark the field as touched when focus moved outside the chip grid. */\n  _blur() {\n    if (!this.disabled) {\n      // Check whether the focus moved to chip input.\n      // If the focus is not moved to chip input, mark the field as touched. If the focus moved\n      // to chip input, do nothing.\n      // Timeout is needed to wait for the focus() event trigger on chip input.\n      setTimeout(() => {\n        if (!this.focused) {\n          this._propagateChanges();\n          this._markAsTouched();\n        }\n      });\n    }\n  }\n  /**\n   * Removes the `tabindex` from the chip grid and resets it back afterwards, allowing the\n   * user to tab out of it. This prevents the grid from capturing focus and redirecting\n   * it back to the first chip, creating a focus trap, if it user tries to tab away.\n   */\n  _allowFocusEscape() {\n    if (!this._chipInput.focused) {\n      super._allowFocusEscape();\n    }\n  }\n  /** Handles custom keyboard events. */\n  _handleKeydown(event) {\n    const keyCode = event.keyCode;\n    const activeItem = this._keyManager.activeItem;\n    if (keyCode === TAB) {\n      if (this._chipInput.focused && hasModifierKey(event, 'shiftKey') && this._chips.length && !this._chips.last.disabled) {\n        event.preventDefault();\n        if (activeItem) {\n          this._keyManager.setActiveItem(activeItem);\n        } else {\n          this._focusLastChip();\n        }\n      } else {\n        // Use the super method here since it doesn't check for the input\n        // focused state. This allows focus to escape if there's only one\n        // disabled chip left in the list.\n        super._allowFocusEscape();\n      }\n    } else if (!this._chipInput.focused) {\n      // The up and down arrows are supposed to navigate between the individual rows in the grid.\n      // We do this by filtering the actions down to the ones that have the same `_isPrimary`\n      // flag as the active action and moving focus between them ourseles instead of delegating\n      // to the key manager. For more information, see #29359 and:\n      // https://www.w3.org/WAI/ARIA/apg/patterns/grid/examples/layout-grids/#ex2_label\n      if ((keyCode === UP_ARROW || keyCode === DOWN_ARROW) && activeItem) {\n        const eligibleActions = this._chipActions.filter(action => action._isPrimary === activeItem._isPrimary && !this._skipPredicate(action));\n        const currentIndex = eligibleActions.indexOf(activeItem);\n        const delta = event.keyCode === UP_ARROW ? -1 : 1;\n        event.preventDefault();\n        if (currentIndex > -1 && this._isValidIndex(currentIndex + delta)) {\n          this._keyManager.setActiveItem(eligibleActions[currentIndex + delta]);\n        }\n      } else {\n        super._handleKeydown(event);\n      }\n    }\n    this.stateChanges.next();\n  }\n  _focusLastChip() {\n    if (this._chips.length) {\n      this._chips.last.focus();\n    }\n  }\n  /** Emits change event to set the model value. */\n  _propagateChanges() {\n    const valueToEmit = this._chips.length ? this._chips.toArray().map(chip => chip.value) : [];\n    this._value = valueToEmit;\n    this.change.emit(new MatChipGridChange(this, valueToEmit));\n    this.valueChange.emit(valueToEmit);\n    this._onChange(valueToEmit);\n    this._changeDetectorRef.markForCheck();\n  }\n  /** Mark the field as touched */\n  _markAsTouched() {\n    this._onTouched();\n    this._changeDetectorRef.markForCheck();\n    this.stateChanges.next();\n  }\n  static ɵfac = function MatChipGrid_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatChipGrid)();\n  };\n  static ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n    type: MatChipGrid,\n    selectors: [[\"mat-chip-grid\"]],\n    contentQueries: function MatChipGrid_ContentQueries(rf, ctx, dirIndex) {\n      if (rf & 1) {\n        i0.ɵɵcontentQuery(dirIndex, MatChipRow, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._chips = _t);\n      }\n    },\n    hostAttrs: [1, \"mat-mdc-chip-set\", \"mat-mdc-chip-grid\", \"mdc-evolution-chip-set\"],\n    hostVars: 10,\n    hostBindings: function MatChipGrid_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"focus\", function MatChipGrid_focus_HostBindingHandler() {\n          return ctx.focus();\n        })(\"blur\", function MatChipGrid_blur_HostBindingHandler() {\n          return ctx._blur();\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵattribute(\"role\", ctx.role)(\"tabindex\", ctx.disabled || ctx._chips && ctx._chips.length === 0 ? -1 : ctx.tabIndex)(\"aria-disabled\", ctx.disabled.toString())(\"aria-invalid\", ctx.errorState);\n        i0.ɵɵclassProp(\"mat-mdc-chip-list-disabled\", ctx.disabled)(\"mat-mdc-chip-list-invalid\", ctx.errorState)(\"mat-mdc-chip-list-required\", ctx.required);\n      }\n    },\n    inputs: {\n      disabled: [2, \"disabled\", \"disabled\", booleanAttribute],\n      placeholder: \"placeholder\",\n      required: [2, \"required\", \"required\", booleanAttribute],\n      value: \"value\",\n      errorStateMatcher: \"errorStateMatcher\"\n    },\n    outputs: {\n      change: \"change\",\n      valueChange: \"valueChange\"\n    },\n    features: [i0.ɵɵProvidersFeature([{\n      provide: MatFormFieldControl,\n      useExisting: MatChipGrid\n    }]), i0.ɵɵInheritDefinitionFeature],\n    ngContentSelectors: _c5,\n    decls: 2,\n    vars: 0,\n    consts: [[\"role\", \"presentation\", 1, \"mdc-evolution-chip-set__chips\"]],\n    template: function MatChipGrid_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵprojectionDef();\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵprojection(1);\n        i0.ɵɵelementEnd();\n      }\n    },\n    styles: [_c6],\n    encapsulation: 2,\n    changeDetection: 0\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatChipGrid, [{\n    type: Component,\n    args: [{\n      selector: 'mat-chip-grid',\n      template: `\n    <div class=\"mdc-evolution-chip-set__chips\" role=\"presentation\">\n      <ng-content></ng-content>\n    </div>\n  `,\n      host: {\n        'class': 'mat-mdc-chip-set mat-mdc-chip-grid mdc-evolution-chip-set',\n        '[attr.role]': 'role',\n        '[attr.tabindex]': '(disabled || (_chips && _chips.length === 0)) ? -1 : tabIndex',\n        '[attr.aria-disabled]': 'disabled.toString()',\n        '[attr.aria-invalid]': 'errorState',\n        '[class.mat-mdc-chip-list-disabled]': 'disabled',\n        '[class.mat-mdc-chip-list-invalid]': 'errorState',\n        '[class.mat-mdc-chip-list-required]': 'required',\n        '(focus)': 'focus()',\n        '(blur)': '_blur()'\n      },\n      providers: [{\n        provide: MatFormFieldControl,\n        useExisting: MatChipGrid\n      }],\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      styles: [\".mat-mdc-chip-set{display:flex}.mat-mdc-chip-set:focus{outline:none}.mat-mdc-chip-set .mdc-evolution-chip-set__chips{min-width:100%;margin-left:-8px;margin-right:0}.mat-mdc-chip-set .mdc-evolution-chip{margin:4px 0 4px 8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip-set__chips{margin-left:0;margin-right:-8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip{margin-left:0;margin-right:8px}.mdc-evolution-chip-set__chips{display:flex;flex-flow:wrap;min-width:0}.mat-mdc-chip-set-stacked{flex-direction:column;align-items:flex-start}.mat-mdc-chip-set-stacked .mat-mdc-chip{width:100%}.mat-mdc-chip-set-stacked .mdc-evolution-chip__graphic{flex-grow:0}.mat-mdc-chip-set-stacked .mdc-evolution-chip__action--primary{flex-basis:100%;justify-content:start}input.mat-mdc-chip-input{flex:1 0 150px;margin-left:8px}[dir=rtl] input.mat-mdc-chip-input{margin-left:0;margin-right:8px}\\n\"]\n    }]\n  }], () => [], {\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    placeholder: [{\n      type: Input\n    }],\n    required: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    value: [{\n      type: Input\n    }],\n    errorStateMatcher: [{\n      type: Input\n    }],\n    change: [{\n      type: Output\n    }],\n    valueChange: [{\n      type: Output\n    }],\n    _chips: [{\n      type: ContentChildren,\n      args: [MatChipRow, {\n        // We need to use `descendants: true`, because Ivy will no longer match\n        // indirect descendants if it's left as false.\n        descendants: true\n      }]\n    }]\n  });\n})();\n\n/**\n * Directive that adds chip-specific behaviors to an input element inside `<mat-form-field>`.\n * May be placed inside or outside of a `<mat-chip-grid>`.\n */\nclass MatChipInput {\n  _elementRef = inject(ElementRef);\n  /** Whether the control is focused. */\n  focused = false;\n  /** Register input for chip list */\n  get chipGrid() {\n    return this._chipGrid;\n  }\n  set chipGrid(value) {\n    if (value) {\n      this._chipGrid = value;\n      this._chipGrid.registerInput(this);\n    }\n  }\n  _chipGrid;\n  /**\n   * Whether or not the chipEnd event will be emitted when the input is blurred.\n   */\n  addOnBlur = false;\n  /**\n   * The list of key codes that will trigger a chipEnd event.\n   *\n   * Defaults to `[ENTER]`.\n   */\n  separatorKeyCodes;\n  /** Emitted when a chip is to be added. */\n  chipEnd = new EventEmitter();\n  /** The input's placeholder text. */\n  placeholder = '';\n  /** Unique id for the input. */\n  id = inject(_IdGenerator).getId('mat-mdc-chip-list-input-');\n  /** Whether the input is disabled. */\n  get disabled() {\n    return this._disabled || this._chipGrid && this._chipGrid.disabled;\n  }\n  set disabled(value) {\n    this._disabled = value;\n  }\n  _disabled = false;\n  /** Whether the input is empty. */\n  get empty() {\n    return !this.inputElement.value;\n  }\n  /** The native input element to which this directive is attached. */\n  inputElement;\n  constructor() {\n    const defaultOptions = inject(MAT_CHIPS_DEFAULT_OPTIONS);\n    const formField = inject(MAT_FORM_FIELD, {\n      optional: true\n    });\n    this.inputElement = this._elementRef.nativeElement;\n    this.separatorKeyCodes = defaultOptions.separatorKeyCodes;\n    if (formField) {\n      this.inputElement.classList.add('mat-mdc-form-field-input-control');\n    }\n  }\n  ngOnChanges() {\n    this._chipGrid.stateChanges.next();\n  }\n  ngOnDestroy() {\n    this.chipEnd.complete();\n  }\n  /** Utility method to make host definition/tests more clear. */\n  _keydown(event) {\n    if (this.empty && event.keyCode === BACKSPACE) {\n      // Ignore events where the user is holding down backspace\n      // so that we don't accidentally remove too many chips.\n      if (!event.repeat) {\n        this._chipGrid._focusLastChip();\n      }\n      event.preventDefault();\n    } else {\n      this._emitChipEnd(event);\n    }\n  }\n  /** Checks to see if the blur should emit the (chipEnd) event. */\n  _blur() {\n    if (this.addOnBlur) {\n      this._emitChipEnd();\n    }\n    this.focused = false;\n    // Blur the chip list if it is not focused\n    if (!this._chipGrid.focused) {\n      this._chipGrid._blur();\n    }\n    this._chipGrid.stateChanges.next();\n  }\n  _focus() {\n    this.focused = true;\n    this._chipGrid.stateChanges.next();\n  }\n  /** Checks to see if the (chipEnd) event needs to be emitted. */\n  _emitChipEnd(event) {\n    if (!event || this._isSeparatorKey(event) && !event.repeat) {\n      this.chipEnd.emit({\n        input: this.inputElement,\n        value: this.inputElement.value,\n        chipInput: this\n      });\n      event?.preventDefault();\n    }\n  }\n  _onInput() {\n    // Let chip list know whenever the value changes.\n    this._chipGrid.stateChanges.next();\n  }\n  /** Focuses the input. */\n  focus() {\n    this.inputElement.focus();\n  }\n  /** Clears the input */\n  clear() {\n    this.inputElement.value = '';\n  }\n  setDescribedByIds(ids) {\n    const element = this._elementRef.nativeElement;\n    // Set the value directly in the DOM since this binding\n    // is prone to \"changed after checked\" errors.\n    if (ids.length) {\n      element.setAttribute('aria-describedby', ids.join(' '));\n    } else {\n      element.removeAttribute('aria-describedby');\n    }\n  }\n  /** Checks whether a keycode is one of the configured separators. */\n  _isSeparatorKey(event) {\n    return !hasModifierKey(event) && new Set(this.separatorKeyCodes).has(event.keyCode);\n  }\n  static ɵfac = function MatChipInput_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatChipInput)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: MatChipInput,\n    selectors: [[\"input\", \"matChipInputFor\", \"\"]],\n    hostAttrs: [1, \"mat-mdc-chip-input\", \"mat-mdc-input-element\", \"mdc-text-field__input\", \"mat-input-element\"],\n    hostVars: 6,\n    hostBindings: function MatChipInput_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"keydown\", function MatChipInput_keydown_HostBindingHandler($event) {\n          return ctx._keydown($event);\n        })(\"blur\", function MatChipInput_blur_HostBindingHandler() {\n          return ctx._blur();\n        })(\"focus\", function MatChipInput_focus_HostBindingHandler() {\n          return ctx._focus();\n        })(\"input\", function MatChipInput_input_HostBindingHandler() {\n          return ctx._onInput();\n        });\n      }\n      if (rf & 2) {\n        i0.ɵɵhostProperty(\"id\", ctx.id);\n        i0.ɵɵattribute(\"disabled\", ctx.disabled || null)(\"placeholder\", ctx.placeholder || null)(\"aria-invalid\", ctx._chipGrid && ctx._chipGrid.ngControl ? ctx._chipGrid.ngControl.invalid : null)(\"aria-required\", ctx._chipGrid && ctx._chipGrid.required || null)(\"required\", ctx._chipGrid && ctx._chipGrid.required || null);\n      }\n    },\n    inputs: {\n      chipGrid: [0, \"matChipInputFor\", \"chipGrid\"],\n      addOnBlur: [2, \"matChipInputAddOnBlur\", \"addOnBlur\", booleanAttribute],\n      separatorKeyCodes: [0, \"matChipInputSeparatorKeyCodes\", \"separatorKeyCodes\"],\n      placeholder: \"placeholder\",\n      id: \"id\",\n      disabled: [2, \"disabled\", \"disabled\", booleanAttribute]\n    },\n    outputs: {\n      chipEnd: \"matChipInputTokenEnd\"\n    },\n    exportAs: [\"matChipInput\", \"matChipInputFor\"],\n    features: [i0.ɵɵNgOnChangesFeature]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatChipInput, [{\n    type: Directive,\n    args: [{\n      selector: 'input[matChipInputFor]',\n      exportAs: 'matChipInput, matChipInputFor',\n      host: {\n        // TODO: eventually we should remove `mat-input-element` from here since it comes from the\n        // non-MDC version of the input. It's currently being kept for backwards compatibility, because\n        // the MDC chips were landed initially with it.\n        'class': 'mat-mdc-chip-input mat-mdc-input-element mdc-text-field__input mat-input-element',\n        '(keydown)': '_keydown($event)',\n        '(blur)': '_blur()',\n        '(focus)': '_focus()',\n        '(input)': '_onInput()',\n        '[id]': 'id',\n        '[attr.disabled]': 'disabled || null',\n        '[attr.placeholder]': 'placeholder || null',\n        '[attr.aria-invalid]': '_chipGrid && _chipGrid.ngControl ? _chipGrid.ngControl.invalid : null',\n        '[attr.aria-required]': '_chipGrid && _chipGrid.required || null',\n        '[attr.required]': '_chipGrid && _chipGrid.required || null'\n      }\n    }]\n  }], () => [], {\n    chipGrid: [{\n      type: Input,\n      args: ['matChipInputFor']\n    }],\n    addOnBlur: [{\n      type: Input,\n      args: [{\n        alias: 'matChipInputAddOnBlur',\n        transform: booleanAttribute\n      }]\n    }],\n    separatorKeyCodes: [{\n      type: Input,\n      args: ['matChipInputSeparatorKeyCodes']\n    }],\n    chipEnd: [{\n      type: Output,\n      args: ['matChipInputTokenEnd']\n    }],\n    placeholder: [{\n      type: Input\n    }],\n    id: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }]\n  });\n})();\nconst CHIP_DECLARATIONS = [MatChip, MatChipAvatar, MatChipEditInput, MatChipGrid, MatChipInput, MatChipListbox, MatChipOption, MatChipRemove, MatChipRow, MatChipSet, MatChipTrailingIcon];\nclass MatChipsModule {\n  static ɵfac = function MatChipsModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || MatChipsModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: MatChipsModule\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n    providers: [ErrorStateMatcher, {\n      provide: MAT_CHIPS_DEFAULT_OPTIONS,\n      useValue: {\n        separatorKeyCodes: [ENTER]\n      }\n    }],\n    imports: [MatCommonModule, MatRippleModule, MatCommonModule]\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatChipsModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, MatRippleModule, MatChipAction, CHIP_DECLARATIONS],\n      exports: [MatCommonModule, CHIP_DECLARATIONS],\n      providers: [ErrorStateMatcher, {\n        provide: MAT_CHIPS_DEFAULT_OPTIONS,\n        useValue: {\n          separatorKeyCodes: [ENTER]\n        }\n      }]\n    }]\n  }], null, null);\n})();\nexport { MAT_CHIP, MAT_CHIPS_DEFAULT_OPTIONS, MAT_CHIP_AVATAR, MAT_CHIP_LISTBOX_CONTROL_VALUE_ACCESSOR, MAT_CHIP_REMOVE, MAT_CHIP_TRAILING_ICON, MatChip, MatChipAvatar, MatChipEditInput, MatChipGrid, MatChipGridChange, MatChipInput, MatChipListbox, MatChipListboxChange, MatChipOption, MatChipRemove, MatChipRow, MatChipSelectionChange, MatChipSet, MatChipTrailingIcon, MatChipsModule };", "map": {"version": 3, "names": ["FocusMonitor", "_IdGenerator", "FocusKeyManager", "ENTER", "SPACE", "BACKSPACE", "DELETE", "TAB", "hasModifierKey", "UP_ARROW", "DOWN_ARROW", "_CdkPrivateStyleLoader", "_VisuallyHiddenLoader", "DOCUMENT", "i0", "InjectionToken", "inject", "ElementRef", "booleanAttribute", "numberAttribute", "Directive", "Input", "ChangeDetectorRef", "NgZone", "EventEmitter", "Injector", "ANIMATION_MODULE_TYPE", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "ContentChildren", "Output", "ContentChild", "ViewChild", "afterNextRender", "QueryList", "forwardRef", "NgModule", "Subject", "merge", "_", "_StructuralStylesLoader", "a", "MAT_RIPPLE_GLOBAL_OPTIONS", "M", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "takeUntil", "startWith", "switchMap", "Directionality", "NG_VALUE_ACCESSOR", "NgControl", "Validators", "NgForm", "FormGroupDirective", "E", "ErrorStateMatcher", "_ErrorStateTracker", "k", "MatFormFieldControl", "h", "MAT_FORM_FIELD", "MatCommonModule", "MatRippleModule", "_c0", "_c1", "MatChip_Conditional_3_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵprojection", "ɵɵelementEnd", "MatChip_Conditional_7_Template", "MatChipOption_Conditional_3_Template", "ɵɵnamespaceSVG", "ɵɵelement", "MatChipOption_Conditional_7_Template", "_c2", "_c3", "_c4", "MatChipRow_Conditional_0_Template", "MatChipRow_Conditional_2_Template", "MatChipRow_Conditional_4_Conditional_0_Template", "MatChipRow_Conditional_4_Conditional_1_Template", "MatChipRow_Conditional_4_Template", "ɵɵtemplate", "ctx_r0", "ɵɵnextContext", "ɵɵconditional", "contentEditInput", "MatChipRow_Conditional_5_Template", "MatChipRow_Conditional_7_Template", "_c5", "_c6", "MAT_CHIPS_DEFAULT_OPTIONS", "providedIn", "factory", "separatorKeyCodes", "MAT_CHIP_AVATAR", "MAT_CHIP_TRAILING_ICON", "MAT_CHIP_REMOVE", "MAT_CHIP", "MatChipAction", "_elementRef", "_parentChip", "isInteractive", "_isPrimary", "disabled", "_disabled", "value", "tabIndex", "_allowFocusWhenDisabled", "_getDisabledAttribute", "_getTabindex", "toString", "constructor", "load", "nativeElement", "nodeName", "setAttribute", "focus", "_handleClick", "event", "preventDefault", "_handlePrimaryActionInteraction", "_handleKeydown", "keyCode", "_isEditing", "ɵfac", "MatChipAction_Factory", "__ngFactoryType__", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "hostAttrs", "hostVars", "hostBindings", "MatChipAction_HostBindings", "ɵɵlistener", "MatChipAction_click_HostBindingHandler", "$event", "MatChipAction_keydown_HostBindingHandler", "ɵɵattribute", "ɵɵclassProp", "inputs", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "host", "transform", "MatChipAvatar", "MatChipAvatar_Factory", "features", "ɵɵProvidersFeature", "provide", "useExisting", "providers", "MatChipTrailingIcon", "ɵMatChipTrailingIcon_BaseFactory", "MatChipTrailingIcon_Factory", "ɵɵgetInheritedFactory", "ɵɵInheritDefinitionFeature", "MatChipRemove", "stopPropagation", "remove", "ɵMatChipRemove_BaseFactory", "MatChipRemove_Factory", "MatChipRemove_HostBindings", "MatChip", "_changeDetectorRef", "_ngZone", "_focusMonitor", "_globalRippleOptions", "optional", "_document", "_onFocus", "_onBlur", "_isBasicChip", "role", "_hasFocusInternal", "_pendingFocus", "_actionChanges", "_animationsDisabled", "_allLeadingIcons", "_allTrailingIcons", "_allRemoveIcons", "_hasFocus", "id", "getId", "aria<PERSON><PERSON><PERSON>", "ariaDescription", "_ariaDescriptionId", "_chipListDisabled", "_textElement", "_value", "undefined", "textContent", "trim", "color", "removable", "highlighted", "disable<PERSON><PERSON><PERSON>", "removed", "destroyed", "basicChipAttrName", "leadingIcon", "trailingIcon", "removeIcon", "primaryAction", "_ripple<PERSON><PERSON>der", "_injector", "<PERSON><PERSON><PERSON><PERSON>", "animationMode", "_monitorFocus", "configureRipple", "className", "_isRippleDisabled", "ngOnInit", "element", "hasAttribute", "tagName", "toLowerCase", "ngAfterViewInit", "querySelector", "ngAfterContentInit", "changes", "subscribe", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ngDoCheck", "setDisabled", "ngOnDestroy", "stopMonitoring", "destroyRipple", "unsubscribe", "emit", "chip", "complete", "_hasTrailingIcon", "repeat", "_getSourceAction", "target", "_getActions", "find", "action", "contains", "result", "push", "monitor", "origin", "hasFocus", "next", "setTimeout", "run", "MatChip_Factory", "ɵcmp", "ɵɵdefineComponent", "contentQueries", "MatChip_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "viewQuery", "MatChip_Query", "ɵɵviewQuery", "MatChip_HostBindings", "MatChip_keydown_HostBindingHandler", "ɵɵhostProperty", "ɵɵclassMap", "outputs", "exportAs", "ngContentSelectors", "decls", "vars", "consts", "template", "MatChip_Template", "ɵɵprojectionDef", "ɵɵadvance", "ɵɵproperty", "dependencies", "styles", "encapsulation", "changeDetection", "None", "OnPush", "imports", "descendants", "MatChipSelectionChange", "source", "selected", "isUserInput", "MatChipOption", "_defaultOptions", "chipListSelectable", "_chipListMultiple", "_chipListHideSingleSelectionIndicator", "hideSingleSelectionIndicator", "selectable", "_selectable", "_selected", "_setSelectedState", "ariaSelected", "selectionChange", "select", "deselect", "selectViaInteraction", "toggleSelected", "_hasLeadingGraphic", "isSelected", "emitEvent", "ɵMatChipOption_BaseFactory", "MatChipOption_Factory", "MatChipOption_HostBindings", "MatChipOption_Template", "ɵɵtext", "ɵɵtextInterpolate", "MatChipEditInput", "initialize", "initialValue", "getNativeElement", "setValue", "_moveCursorToEndOfInput", "getValue", "range", "createRange", "selectNodeContents", "collapse", "sel", "window", "getSelection", "removeAllRanges", "addRange", "MatChipEditInput_Factory", "MatChipRow", "_editStartPending", "editable", "edited", "defaultEditInput", "pipe", "_onEditFinish", "_handleFocus", "_startEditing", "_handleDoubleclick", "_getEditInput", "injector", "activeElement", "body", "MatChipRow_Factory", "MatChipRow_ContentQueries", "MatChipRow_Query", "MatChipRow_HostBindings", "MatChipRow_focus_HostBindingHandler", "MatChipRow_dblclick_HostBindingHandler", "MatChipRow_Template", "MatChipSet", "_dir", "_lastDestroyedFocusedChipIndex", "_keyManager", "_destroyed", "_defaultRole", "chipFocusChanges", "_getChipStream", "chipDestroyedChanges", "chipRemovedChanges", "_syncChipsState", "empty", "_chips", "length", "_explicitRole", "focused", "_hasFocusedChip", "_chipActions", "_setUpFocusManagement", "_trackChipSetChanges", "_trackDestroyedFocusedChip", "destroy", "some", "for<PERSON>ach", "_originatesFromChip", "onKeydown", "_isValidIndex", "index", "_allowFocusEscape", "previous", "mappingFunction", "map", "currentElement", "classList", "parentElement", "chips", "actions", "reset", "notifyOn<PERSON><PERSON>es", "withVerticalOrientation", "withHorizontalOrientation", "withHomeAndEnd", "skipPredicate", "_skipPredicate", "document", "updateActiveItem", "change", "direction", "Promise", "resolve", "then", "_redirectDestroyedChipFocus", "chipArray", "toArray", "chipIndex", "indexOf", "newIndex", "Math", "min", "chipToFocus", "setPreviousItemActive", "MatChipSet_Factory", "MatChipSet_ContentQueries", "MatChipSet_HostBindings", "MatChipSet_keydown_HostBindingHandler", "MatChipSet_Template", "MatChipListboxChange", "MAT_CHIP_LISTBOX_CONTROL_VALUE_ACCESSOR", "MatChipListbox", "multi", "_onTouched", "_onChange", "multiple", "_multiple", "_syncListboxProperties", "selectedChips", "filter", "ariaOrientation", "compareWith", "o1", "o2", "required", "_hideSingleSelectionIndicator", "chipSelectionChanges", "chipBlurChanges", "_setSelectionByValue", "_blur", "_propagateChanges", "firstSelectedChip", "_getFirstSelectedChip", "setFirstItemActive", "writeValue", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "isDisabled", "_clearSelection", "Array", "isArray", "currentValue", "_selectValue", "_markAsTouched", "_keydown", "valueToEmit", "skip", "correspondingChip", "ɵMatChipListbox_BaseFactory", "MatChipListbox_Factory", "MatChipListbox_ContentQueries", "MatChipListbox_HostBindings", "MatChipListbox_focus_HostBindingHandler", "MatChipListbox_blur_HostBindingHandler", "MatChipListbox_keydown_HostBindingHandler", "MatChipListbox_Template", "MatChipGridChange", "MatChipGrid", "ngControl", "self", "controlType", "_chipInput", "_errorStateTracker", "_ariaDescribedbyIds", "stateChanges", "placeholder", "_placeholder", "_required", "control", "hasValidator", "shouldLabelFloat", "errorStateMatcher", "matcher", "valueChange", "errorState", "parentForm", "parentFormGroup", "defaultErrorStateMatcher", "valueAccessor", "Error", "updateErrorState", "registerInput", "inputElement", "setDescribedByIds", "onContainerClick", "activeItem", "ids", "last", "setActiveItem", "_focusLastChip", "eligibleActions", "currentIndex", "delta", "MatChipGrid_Factory", "MatChipGrid_ContentQueries", "MatChipGrid_HostBindings", "MatChipGrid_focus_HostBindingHandler", "MatChipGrid_blur_HostBindingHandler", "MatChipGrid_Template", "MatChipInput", "chipGrid", "_chipGrid", "addOnBlur", "chipEnd", "defaultOptions", "formField", "add", "ngOnChanges", "_emitChipEnd", "_focus", "_isSeparator<PERSON>ey", "input", "chipInput", "_onInput", "clear", "join", "removeAttribute", "Set", "has", "MatChipInput_Factory", "MatChipInput_HostBindings", "MatChipInput_keydown_HostBindingHandler", "MatChipInput_blur_HostBindingHandler", "MatChipInput_focus_HostBindingHandler", "MatChipInput_input_HostBindingHandler", "invalid", "ɵɵNgOnChangesFeature", "alias", "CHIP_DECLARATIONS", "MatChipsModule", "MatChipsModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "useValue", "exports"], "sources": ["D:/TGI/Blockchain.SPT/frontend/node_modules/@angular/material/fesm2022/chips.mjs"], "sourcesContent": ["import { FocusMonitor, _Id<PERSON>enerator, FocusKeyManager } from '@angular/cdk/a11y';\nimport { ENTER, SPACE, BACKSPACE, DELETE, TAB, hasModifierKey, UP_ARROW, DOWN_ARROW } from '@angular/cdk/keycodes';\nimport { _CdkPrivateStyleLoader, _VisuallyHiddenLoader } from '@angular/cdk/private';\nimport { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, ElementRef, booleanAttribute, numberAttribute, Directive, Input, ChangeDetectorRef, NgZone, EventEmitter, Injector, ANIMATION_MODULE_TYPE, Component, ViewEncapsulation, ChangeDetectionStrategy, ContentChildren, Output, ContentChild, ViewChild, afterNextRender, QueryList, forwardRef, NgModule } from '@angular/core';\nimport { Subject, merge } from 'rxjs';\nimport { _ as _StructuralStylesLoader } from './structural-styles-BQUT6wsL.mjs';\nimport { a as MAT_RIPPLE_GLOBAL_OPTIONS } from './ripple-BT3tzh6F.mjs';\nimport { M as MatRippleLoader } from './ripple-loader-Ce3DAhPW.mjs';\nimport { takeUntil, startWith, switchMap } from 'rxjs/operators';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { NG_VALUE_ACCESSOR, NgControl, Validators, NgForm, FormGroupDirective } from '@angular/forms';\nimport { E as ErrorStateMatcher } from './error-options-Dm2JJUbF.mjs';\nimport { _ as _ErrorStateTracker } from './error-state-Dtb1IHM-.mjs';\nimport { k as MatFormFieldControl, h as MAT_FORM_FIELD } from './form-field-DqPi4knt.mjs';\nimport { M as MatCommonModule } from './common-module-WayjW0Pb.mjs';\nimport { M as MatRippleModule } from './index-SYVYjXwK.mjs';\nimport '@angular/cdk/platform';\nimport '@angular/cdk/coercion';\nimport '@angular/cdk/observers/private';\n\n/** Injection token to be used to override the default options for the chips module. */\nconst MAT_CHIPS_DEFAULT_OPTIONS = new InjectionToken('mat-chips-default-options', {\n    providedIn: 'root',\n    factory: () => ({\n        separatorKeyCodes: [ENTER],\n    }),\n});\n/**\n * Injection token that can be used to reference instances of `MatChipAvatar`. It serves as\n * alternative token to the actual `MatChipAvatar` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_CHIP_AVATAR = new InjectionToken('MatChipAvatar');\n/**\n * Injection token that can be used to reference instances of `MatChipTrailingIcon`. It serves as\n * alternative token to the actual `MatChipTrailingIcon` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_CHIP_TRAILING_ICON = new InjectionToken('MatChipTrailingIcon');\n/**\n * Injection token that can be used to reference instances of `MatChipRemove`. It serves as\n * alternative token to the actual `MatChipRemove` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_CHIP_REMOVE = new InjectionToken('MatChipRemove');\n/**\n * Injection token used to avoid a circular dependency between the `MatChip` and `MatChipAction`.\n */\nconst MAT_CHIP = new InjectionToken('MatChip');\n\n/**\n * Section within a chip.\n * @docs-private\n */\nclass MatChipAction {\n    _elementRef = inject(ElementRef);\n    _parentChip = inject(MAT_CHIP);\n    /** Whether the action is interactive. */\n    isInteractive = true;\n    /** Whether this is the primary action in the chip. */\n    _isPrimary = true;\n    /** Whether the action is disabled. */\n    get disabled() {\n        return this._disabled || this._parentChip?.disabled || false;\n    }\n    set disabled(value) {\n        this._disabled = value;\n    }\n    _disabled = false;\n    /** Tab index of the action. */\n    tabIndex = -1;\n    /**\n     * Private API to allow focusing this chip when it is disabled.\n     */\n    _allowFocusWhenDisabled = false;\n    /**\n     * Determine the value of the disabled attribute for this chip action.\n     */\n    _getDisabledAttribute() {\n        // When this chip action is disabled and focusing disabled chips is not permitted, return empty\n        // string to indicate that disabled attribute should be included.\n        return this.disabled && !this._allowFocusWhenDisabled ? '' : null;\n    }\n    /**\n     * Determine the value of the tabindex attribute for this chip action.\n     */\n    _getTabindex() {\n        return (this.disabled && !this._allowFocusWhenDisabled) || !this.isInteractive\n            ? null\n            : this.tabIndex.toString();\n    }\n    constructor() {\n        inject(_CdkPrivateStyleLoader).load(_StructuralStylesLoader);\n        if (this._elementRef.nativeElement.nodeName === 'BUTTON') {\n            this._elementRef.nativeElement.setAttribute('type', 'button');\n        }\n    }\n    focus() {\n        this._elementRef.nativeElement.focus();\n    }\n    _handleClick(event) {\n        if (!this.disabled && this.isInteractive && this._isPrimary) {\n            event.preventDefault();\n            this._parentChip._handlePrimaryActionInteraction();\n        }\n    }\n    _handleKeydown(event) {\n        if ((event.keyCode === ENTER || event.keyCode === SPACE) &&\n            !this.disabled &&\n            this.isInteractive &&\n            this._isPrimary &&\n            !this._parentChip._isEditing) {\n            event.preventDefault();\n            this._parentChip._handlePrimaryActionInteraction();\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatChipAction, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"19.2.6\", type: MatChipAction, isStandalone: true, selector: \"[matChipAction]\", inputs: { isInteractive: \"isInteractive\", disabled: [\"disabled\", \"disabled\", booleanAttribute], tabIndex: [\"tabIndex\", \"tabIndex\", (value) => (value == null ? -1 : numberAttribute(value))], _allowFocusWhenDisabled: \"_allowFocusWhenDisabled\" }, host: { listeners: { \"click\": \"_handleClick($event)\", \"keydown\": \"_handleKeydown($event)\" }, properties: { \"class.mdc-evolution-chip__action--primary\": \"_isPrimary\", \"class.mdc-evolution-chip__action--presentational\": \"!isInteractive\", \"class.mdc-evolution-chip__action--trailing\": \"!_isPrimary\", \"attr.tabindex\": \"_getTabindex()\", \"attr.disabled\": \"_getDisabledAttribute()\", \"attr.aria-disabled\": \"disabled\" }, classAttribute: \"mdc-evolution-chip__action mat-mdc-chip-action\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatChipAction, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matChipAction]',\n                    host: {\n                        'class': 'mdc-evolution-chip__action mat-mdc-chip-action',\n                        '[class.mdc-evolution-chip__action--primary]': '_isPrimary',\n                        '[class.mdc-evolution-chip__action--presentational]': '!isInteractive',\n                        '[class.mdc-evolution-chip__action--trailing]': '!_isPrimary',\n                        '[attr.tabindex]': '_getTabindex()',\n                        '[attr.disabled]': '_getDisabledAttribute()',\n                        '[attr.aria-disabled]': 'disabled',\n                        '(click)': '_handleClick($event)',\n                        '(keydown)': '_handleKeydown($event)',\n                    },\n                }]\n        }], ctorParameters: () => [], propDecorators: { isInteractive: [{\n                type: Input\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], tabIndex: [{\n                type: Input,\n                args: [{\n                        transform: (value) => (value == null ? -1 : numberAttribute(value)),\n                    }]\n            }], _allowFocusWhenDisabled: [{\n                type: Input\n            }] } });\n\n/** Avatar image within a chip. */\nclass MatChipAvatar {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatChipAvatar, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatChipAvatar, isStandalone: true, selector: \"mat-chip-avatar, [matChipAvatar]\", host: { attributes: { \"role\": \"img\" }, classAttribute: \"mat-mdc-chip-avatar mdc-evolution-chip__icon mdc-evolution-chip__icon--primary\" }, providers: [{ provide: MAT_CHIP_AVATAR, useExisting: MatChipAvatar }], ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatChipAvatar, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-chip-avatar, [matChipAvatar]',\n                    host: {\n                        'class': 'mat-mdc-chip-avatar mdc-evolution-chip__icon mdc-evolution-chip__icon--primary',\n                        'role': 'img',\n                    },\n                    providers: [{ provide: MAT_CHIP_AVATAR, useExisting: MatChipAvatar }],\n                }]\n        }] });\n/** Non-interactive trailing icon in a chip. */\nclass MatChipTrailingIcon extends MatChipAction {\n    /**\n     * MDC considers all trailing actions as a remove icon,\n     * but we support non-interactive trailing icons.\n     */\n    isInteractive = false;\n    _isPrimary = false;\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatChipTrailingIcon, deps: null, target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatChipTrailingIcon, isStandalone: true, selector: \"mat-chip-trailing-icon, [matChipTrailingIcon]\", host: { attributes: { \"aria-hidden\": \"true\" }, classAttribute: \"mat-mdc-chip-trailing-icon mdc-evolution-chip__icon mdc-evolution-chip__icon--trailing\" }, providers: [{ provide: MAT_CHIP_TRAILING_ICON, useExisting: MatChipTrailingIcon }], usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatChipTrailingIcon, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-chip-trailing-icon, [matChipTrailingIcon]',\n                    host: {\n                        'class': 'mat-mdc-chip-trailing-icon mdc-evolution-chip__icon mdc-evolution-chip__icon--trailing',\n                        'aria-hidden': 'true',\n                    },\n                    providers: [{ provide: MAT_CHIP_TRAILING_ICON, useExisting: MatChipTrailingIcon }],\n                }]\n        }] });\n/**\n * Directive to remove the parent chip when the trailing icon is clicked or\n * when the ENTER key is pressed on it.\n *\n * Recommended for use with the Material Design \"cancel\" icon\n * available at https://material.io/icons/#ic_cancel.\n *\n * Example:\n *\n * ```\n * <mat-chip>\n *   <mat-icon matChipRemove>cancel</mat-icon>\n * </mat-chip>\n * ```\n */\nclass MatChipRemove extends MatChipAction {\n    _isPrimary = false;\n    _handleClick(event) {\n        if (!this.disabled) {\n            event.stopPropagation();\n            event.preventDefault();\n            this._parentChip.remove();\n        }\n    }\n    _handleKeydown(event) {\n        if ((event.keyCode === ENTER || event.keyCode === SPACE) && !this.disabled) {\n            event.stopPropagation();\n            event.preventDefault();\n            this._parentChip.remove();\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatChipRemove, deps: null, target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatChipRemove, isStandalone: true, selector: \"[matChipRemove]\", host: { attributes: { \"role\": \"button\" }, properties: { \"attr.aria-hidden\": \"null\" }, classAttribute: \"mat-mdc-chip-remove mat-mdc-chip-trailing-icon mat-focus-indicator mdc-evolution-chip__icon mdc-evolution-chip__icon--trailing\" }, providers: [{ provide: MAT_CHIP_REMOVE, useExisting: MatChipRemove }], usesInheritance: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatChipRemove, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matChipRemove]',\n                    host: {\n                        'class': 'mat-mdc-chip-remove mat-mdc-chip-trailing-icon mat-focus-indicator ' +\n                            'mdc-evolution-chip__icon mdc-evolution-chip__icon--trailing',\n                        'role': 'button',\n                        '[attr.aria-hidden]': 'null',\n                    },\n                    providers: [{ provide: MAT_CHIP_REMOVE, useExisting: MatChipRemove }],\n                }]\n        }] });\n\n/**\n * Material design styled Chip base component. Used inside the MatChipSet component.\n *\n * Extended by MatChipOption and MatChipRow for different interaction patterns.\n */\nclass MatChip {\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _elementRef = inject(ElementRef);\n    _ngZone = inject(NgZone);\n    _focusMonitor = inject(FocusMonitor);\n    _globalRippleOptions = inject(MAT_RIPPLE_GLOBAL_OPTIONS, {\n        optional: true,\n    });\n    _document = inject(DOCUMENT);\n    /** Emits when the chip is focused. */\n    _onFocus = new Subject();\n    /** Emits when the chip is blurred. */\n    _onBlur = new Subject();\n    /** Whether this chip is a basic (unstyled) chip. */\n    _isBasicChip;\n    /** Role for the root of the chip. */\n    role = null;\n    /** Whether the chip has focus. */\n    _hasFocusInternal = false;\n    /** Whether moving focus into the chip is pending. */\n    _pendingFocus;\n    /** Subscription to changes in the chip's actions. */\n    _actionChanges;\n    /** Whether animations for the chip are enabled. */\n    _animationsDisabled;\n    /** All avatars present in the chip. */\n    _allLeadingIcons;\n    /** All trailing icons present in the chip. */\n    _allTrailingIcons;\n    /** All remove icons present in the chip. */\n    _allRemoveIcons;\n    _hasFocus() {\n        return this._hasFocusInternal;\n    }\n    /** A unique id for the chip. If none is supplied, it will be auto-generated. */\n    id = inject(_IdGenerator).getId('mat-mdc-chip-');\n    // TODO(#26104): Consider deprecating and using `_computeAriaAccessibleName` instead.\n    // `ariaLabel` may be unnecessary, and `_computeAriaAccessibleName` only supports\n    // datepicker's use case.\n    /** ARIA label for the content of the chip. */\n    ariaLabel = null;\n    // TODO(#26104): Consider deprecating and using `_computeAriaAccessibleName` instead.\n    // `ariaDescription` may be unnecessary, and `_computeAriaAccessibleName` only supports\n    // datepicker's use case.\n    /** ARIA description for the content of the chip. */\n    ariaDescription = null;\n    /** Id of a span that contains this chip's aria description. */\n    _ariaDescriptionId = `${this.id}-aria-description`;\n    /** Whether the chip list is disabled. */\n    _chipListDisabled = false;\n    _textElement;\n    /**\n     * The value of the chip. Defaults to the content inside\n     * the `mat-mdc-chip-action-label` element.\n     */\n    get value() {\n        return this._value !== undefined ? this._value : this._textElement.textContent.trim();\n    }\n    set value(value) {\n        this._value = value;\n    }\n    _value;\n    // TODO: should be typed as `ThemePalette` but internal apps pass in arbitrary strings.\n    /**\n     * Theme color of the chip. This API is supported in M2 themes only, it has no\n     * effect in M3 themes. For color customization in M3, see https://material.angular.dev/components/chips/styling.\n     *\n     * For information on applying color variants in M3, see\n     * https://material.angular.dev/guide/material-2-theming#optional-add-backwards-compatibility-styles-for-color-variants\n     */\n    color;\n    /**\n     * Determines whether or not the chip displays the remove styling and emits (removed) events.\n     */\n    removable = true;\n    /**\n     * Colors the chip for emphasis as if it were selected.\n     */\n    highlighted = false;\n    /** Whether the ripple effect is disabled or not. */\n    disableRipple = false;\n    /** Whether the chip is disabled. */\n    get disabled() {\n        return this._disabled || this._chipListDisabled;\n    }\n    set disabled(value) {\n        this._disabled = value;\n    }\n    _disabled = false;\n    /** Emitted when a chip is to be removed. */\n    removed = new EventEmitter();\n    /** Emitted when the chip is destroyed. */\n    destroyed = new EventEmitter();\n    /** The unstyled chip selector for this component. */\n    basicChipAttrName = 'mat-basic-chip';\n    /** The chip's leading icon. */\n    leadingIcon;\n    /** The chip's trailing icon. */\n    trailingIcon;\n    /** The chip's trailing remove icon. */\n    removeIcon;\n    /** Action receiving the primary set of user interactions. */\n    primaryAction;\n    /**\n     * Handles the lazy creation of the MatChip ripple.\n     * Used to improve initial load time of large applications.\n     */\n    _rippleLoader = inject(MatRippleLoader);\n    _injector = inject(Injector);\n    constructor() {\n        const styleLoader = inject(_CdkPrivateStyleLoader);\n        styleLoader.load(_StructuralStylesLoader);\n        styleLoader.load(_VisuallyHiddenLoader);\n        const animationMode = inject(ANIMATION_MODULE_TYPE, { optional: true });\n        this._animationsDisabled = animationMode === 'NoopAnimations';\n        this._monitorFocus();\n        this._rippleLoader?.configureRipple(this._elementRef.nativeElement, {\n            className: 'mat-mdc-chip-ripple',\n            disabled: this._isRippleDisabled(),\n        });\n    }\n    ngOnInit() {\n        // This check needs to happen in `ngOnInit` so the overridden value of\n        // `basicChipAttrName` coming from base classes can be picked up.\n        const element = this._elementRef.nativeElement;\n        this._isBasicChip =\n            element.hasAttribute(this.basicChipAttrName) ||\n                element.tagName.toLowerCase() === this.basicChipAttrName;\n    }\n    ngAfterViewInit() {\n        this._textElement = this._elementRef.nativeElement.querySelector('.mat-mdc-chip-action-label');\n        if (this._pendingFocus) {\n            this._pendingFocus = false;\n            this.focus();\n        }\n    }\n    ngAfterContentInit() {\n        // Since the styling depends on the presence of some\n        // actions, we have to mark for check on changes.\n        this._actionChanges = merge(this._allLeadingIcons.changes, this._allTrailingIcons.changes, this._allRemoveIcons.changes).subscribe(() => this._changeDetectorRef.markForCheck());\n    }\n    ngDoCheck() {\n        this._rippleLoader.setDisabled(this._elementRef.nativeElement, this._isRippleDisabled());\n    }\n    ngOnDestroy() {\n        this._focusMonitor.stopMonitoring(this._elementRef);\n        this._rippleLoader?.destroyRipple(this._elementRef.nativeElement);\n        this._actionChanges?.unsubscribe();\n        this.destroyed.emit({ chip: this });\n        this.destroyed.complete();\n    }\n    /**\n     * Allows for programmatic removal of the chip.\n     *\n     * Informs any listeners of the removal request. Does not remove the chip from the DOM.\n     */\n    remove() {\n        if (this.removable) {\n            this.removed.emit({ chip: this });\n        }\n    }\n    /** Whether or not the ripple should be disabled. */\n    _isRippleDisabled() {\n        return (this.disabled ||\n            this.disableRipple ||\n            this._animationsDisabled ||\n            this._isBasicChip ||\n            !!this._globalRippleOptions?.disabled);\n    }\n    /** Returns whether the chip has a trailing icon. */\n    _hasTrailingIcon() {\n        return !!(this.trailingIcon || this.removeIcon);\n    }\n    /** Handles keyboard events on the chip. */\n    _handleKeydown(event) {\n        // Ignore backspace events where the user is holding down the key\n        // so that we don't accidentally remove too many chips.\n        if ((event.keyCode === BACKSPACE && !event.repeat) || event.keyCode === DELETE) {\n            event.preventDefault();\n            this.remove();\n        }\n    }\n    /** Allows for programmatic focusing of the chip. */\n    focus() {\n        if (!this.disabled) {\n            // If `focus` is called before `ngAfterViewInit`, we won't have access to the primary action.\n            // This can happen if the consumer tries to focus a chip immediately after it is added.\n            // Queue the method to be called again on init.\n            if (this.primaryAction) {\n                this.primaryAction.focus();\n            }\n            else {\n                this._pendingFocus = true;\n            }\n        }\n    }\n    /** Gets the action that contains a specific target node. */\n    _getSourceAction(target) {\n        return this._getActions().find(action => {\n            const element = action._elementRef.nativeElement;\n            return element === target || element.contains(target);\n        });\n    }\n    /** Gets all of the actions within the chip. */\n    _getActions() {\n        const result = [];\n        if (this.primaryAction) {\n            result.push(this.primaryAction);\n        }\n        if (this.removeIcon) {\n            result.push(this.removeIcon);\n        }\n        if (this.trailingIcon) {\n            result.push(this.trailingIcon);\n        }\n        return result;\n    }\n    /** Handles interactions with the primary action of the chip. */\n    _handlePrimaryActionInteraction() {\n        // Empty here, but is overwritten in child classes.\n    }\n    /** Starts the focus monitoring process on the chip. */\n    _monitorFocus() {\n        this._focusMonitor.monitor(this._elementRef, true).subscribe(origin => {\n            const hasFocus = origin !== null;\n            if (hasFocus !== this._hasFocusInternal) {\n                this._hasFocusInternal = hasFocus;\n                if (hasFocus) {\n                    this._onFocus.next({ chip: this });\n                }\n                else {\n                    // When animations are enabled, Angular may end up removing the chip from the DOM a little\n                    // earlier than usual, causing it to be blurred and throwing off the logic in the chip list\n                    // that moves focus to the next item. To work around the issue, we defer marking the chip\n                    // as not focused until after the next render.\n                    this._changeDetectorRef.markForCheck();\n                    setTimeout(() => this._ngZone.run(() => this._onBlur.next({ chip: this })));\n                }\n            }\n        });\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatChip, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"19.2.6\", type: MatChip, isStandalone: true, selector: \"mat-basic-chip, [mat-basic-chip], mat-chip, [mat-chip]\", inputs: { role: \"role\", id: \"id\", ariaLabel: [\"aria-label\", \"ariaLabel\"], ariaDescription: [\"aria-description\", \"ariaDescription\"], value: \"value\", color: \"color\", removable: [\"removable\", \"removable\", booleanAttribute], highlighted: [\"highlighted\", \"highlighted\", booleanAttribute], disableRipple: [\"disableRipple\", \"disableRipple\", booleanAttribute], disabled: [\"disabled\", \"disabled\", booleanAttribute] }, outputs: { removed: \"removed\", destroyed: \"destroyed\" }, host: { listeners: { \"keydown\": \"_handleKeydown($event)\" }, properties: { \"class\": \"\\\"mat-\\\" + (color || \\\"primary\\\")\", \"class.mdc-evolution-chip\": \"!_isBasicChip\", \"class.mdc-evolution-chip--disabled\": \"disabled\", \"class.mdc-evolution-chip--with-trailing-action\": \"_hasTrailingIcon()\", \"class.mdc-evolution-chip--with-primary-graphic\": \"leadingIcon\", \"class.mdc-evolution-chip--with-primary-icon\": \"leadingIcon\", \"class.mdc-evolution-chip--with-avatar\": \"leadingIcon\", \"class.mat-mdc-chip-with-avatar\": \"leadingIcon\", \"class.mat-mdc-chip-highlighted\": \"highlighted\", \"class.mat-mdc-chip-disabled\": \"disabled\", \"class.mat-mdc-basic-chip\": \"_isBasicChip\", \"class.mat-mdc-standard-chip\": \"!_isBasicChip\", \"class.mat-mdc-chip-with-trailing-icon\": \"_hasTrailingIcon()\", \"class._mat-animation-noopable\": \"_animationsDisabled\", \"id\": \"id\", \"attr.role\": \"role\", \"attr.aria-label\": \"ariaLabel\" }, classAttribute: \"mat-mdc-chip\" }, providers: [{ provide: MAT_CHIP, useExisting: MatChip }], queries: [{ propertyName: \"leadingIcon\", first: true, predicate: MAT_CHIP_AVATAR, descendants: true }, { propertyName: \"trailingIcon\", first: true, predicate: MAT_CHIP_TRAILING_ICON, descendants: true }, { propertyName: \"removeIcon\", first: true, predicate: MAT_CHIP_REMOVE, descendants: true }, { propertyName: \"_allLeadingIcons\", predicate: MAT_CHIP_AVATAR, descendants: true }, { propertyName: \"_allTrailingIcons\", predicate: MAT_CHIP_TRAILING_ICON, descendants: true }, { propertyName: \"_allRemoveIcons\", predicate: MAT_CHIP_REMOVE, descendants: true }], viewQueries: [{ propertyName: \"primaryAction\", first: true, predicate: MatChipAction, descendants: true }], exportAs: [\"matChip\"], ngImport: i0, template: \"<span class=\\\"mat-mdc-chip-focus-overlay\\\"></span>\\n\\n<span class=\\\"mdc-evolution-chip__cell mdc-evolution-chip__cell--primary\\\">\\n  <span matChipAction [isInteractive]=\\\"false\\\">\\n    @if (leadingIcon) {\\n      <span class=\\\"mdc-evolution-chip__graphic mat-mdc-chip-graphic\\\">\\n        <ng-content select=\\\"mat-chip-avatar, [matChipAvatar]\\\"></ng-content>\\n      </span>\\n    }\\n    <span class=\\\"mdc-evolution-chip__text-label mat-mdc-chip-action-label\\\">\\n      <ng-content></ng-content>\\n      <span class=\\\"mat-mdc-chip-primary-focus-indicator mat-focus-indicator\\\"></span>\\n    </span>\\n  </span>\\n</span>\\n\\n@if (_hasTrailingIcon()) {\\n  <span class=\\\"mdc-evolution-chip__cell mdc-evolution-chip__cell--trailing\\\">\\n    <ng-content select=\\\"mat-chip-trailing-icon,[matChipRemove],[matChipTrailingIcon]\\\"></ng-content>\\n  </span>\\n}\\n\", styles: [\".mdc-evolution-chip,.mdc-evolution-chip__cell,.mdc-evolution-chip__action{display:inline-flex;align-items:center}.mdc-evolution-chip{position:relative;max-width:100%}.mdc-evolution-chip__cell,.mdc-evolution-chip__action{height:100%}.mdc-evolution-chip__cell--primary{flex-basis:100%;overflow-x:hidden}.mdc-evolution-chip__cell--trailing{flex:1 0 auto}.mdc-evolution-chip__action{align-items:center;background:none;border:none;box-sizing:content-box;cursor:pointer;display:inline-flex;justify-content:center;outline:none;padding:0;text-decoration:none;color:inherit}.mdc-evolution-chip__action--presentational{cursor:auto}.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{pointer-events:none}@media(forced-colors: active){.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{forced-color-adjust:none}}.mdc-evolution-chip__action--primary{font:inherit;letter-spacing:inherit;white-space:inherit;overflow-x:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary::before{border-width:var(--mdc-chip-outline-width, 1px);border-radius:var(--mdc-chip-container-shape-radius, 8px);box-sizing:border-box;content:\\\"\\\";height:100%;left:0;position:absolute;pointer-events:none;top:0;width:100%;z-index:1;border-style:solid}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--primary::before{border-color:var(--mdc-chip-outline-color, var(--mat-sys-outline))}.mdc-evolution-chip__action--primary:not(.mdc-evolution-chip__action--presentational):not(.mdc-ripple-upgraded):focus::before{border-color:var(--mdc-chip-focus-outline-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--primary::before{border-color:var(--mdc-chip-disabled-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__action--primary::before{border-width:var(--mdc-chip-flat-selected-outline-width, 0)}.mat-mdc-basic-chip .mdc-evolution-chip__action--primary{font:inherit}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip__action--trailing{position:relative;overflow:visible}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--trailing{color:var(--mdc-chip-with-trailing-icon-trailing-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--trailing{color:var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip__text-label{-webkit-user-select:none;user-select:none;white-space:nowrap;text-overflow:ellipsis;overflow:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__text-label{font-family:var(--mdc-chip-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mdc-chip-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mdc-chip-label-text-size, var(--mat-sys-label-large-size));font-weight:var(--mdc-chip-label-text-weight, var(--mat-sys-label-large-weight));letter-spacing:var(--mdc-chip-label-text-tracking, var(--mat-sys-label-large-tracking))}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mdc-chip-label-text-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mdc-chip-selected-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label,.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label{color:var(--mdc-chip-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-evolution-chip__graphic{align-items:center;display:inline-flex;justify-content:center;overflow:hidden;pointer-events:none;position:relative;flex:1 0 auto}.mat-mdc-standard-chip .mdc-evolution-chip__graphic{width:var(--mdc-chip-with-avatar-avatar-size, 24px);height:var(--mdc-chip-with-avatar-avatar-size, 24px);font-size:var(--mdc-chip-with-avatar-avatar-size, 24px)}.mdc-evolution-chip--selecting .mdc-evolution-chip__graphic{transition:width 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selectable:not(.mdc-evolution-chip--selected):not(.mdc-evolution-chip--with-primary-icon) .mdc-evolution-chip__graphic{width:0}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mdc-evolution-chip__checkmark{position:absolute;opacity:0;top:50%;left:50%;height:20px;width:20px}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__checkmark{color:var(--mdc-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__checkmark{color:var(--mdc-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark{transition:transform 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1);transform:translate(-75%, -50%)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{transform:translate(-50%, -50%);opacity:1}.mdc-evolution-chip__checkmark-svg{display:block}.mdc-evolution-chip__checkmark-path{stroke-width:2px;stroke-dasharray:29.7833385;stroke-dashoffset:29.7833385;stroke:currentColor}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark-path{transition:stroke-dashoffset 150ms 45ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark-path{stroke-dashoffset:0}@media(forced-colors: active){.mdc-evolution-chip__checkmark-path{stroke:CanvasText !important}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--trailing{height:18px;width:18px;font-size:18px}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove{opacity:calc(var(--mat-chip-trailing-action-opacity, 1)*var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove:focus{opacity:calc(var(--mat-chip-trailing-action-focus-opacity, 1)*var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mat-mdc-standard-chip{border-radius:var(--mdc-chip-container-shape-radius, 8px);height:var(--mdc-chip-container-height, 32px)}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled){background-color:var(--mdc-chip-elevated-container-color, transparent)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{background-color:var(--mdc-chip-elevated-disabled-container-color)}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled){background-color:var(--mdc-chip-elevated-selected-container-color, var(--mat-sys-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled{background-color:var(--mdc-chip-flat-disabled-selected-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}@media(forced-colors: active){.mat-mdc-standard-chip{outline:solid 1px}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--primary{border-radius:var(--mdc-chip-with-avatar-avatar-shape-radius, 24px);width:var(--mdc-chip-with-icon-icon-size, 18px);height:var(--mdc-chip-with-icon-icon-size, 18px);font-size:var(--mdc-chip-with-icon-icon-size, 18px)}.mdc-evolution-chip--selected .mdc-evolution-chip__icon--primary{opacity:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__icon--primary{color:var(--mdc-chip-with-icon-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--primary{color:var(--mdc-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-highlighted{--mdc-chip-with-icon-icon-color:var(--mdc-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container));--mdc-chip-elevated-container-color:var(--mdc-chip-elevated-selected-container-color, var(--mat-sys-secondary-container));--mdc-chip-label-text-color:var(--mdc-chip-selected-label-text-color, var(--mat-sys-on-secondary-container));--mdc-chip-outline-width:var(--mdc-chip-flat-selected-outline-width, 0)}.mat-mdc-chip-focus-overlay{background:var(--mdc-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-selected .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-chip:hover .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-hover-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mdc-chip-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-focus-overlay .mat-mdc-chip-selected:hover,.mat-mdc-chip-highlighted:hover .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-hover-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mdc-chip-selected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mdc-chip-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected.cdk-focused .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mdc-chip-selected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-evolution-chip--disabled:not(.mdc-evolution-chip--selected) .mat-mdc-chip-avatar{opacity:var(--mdc-chip-with-avatar-disabled-avatar-opacity, 0.38)}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{opacity:var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38)}.mdc-evolution-chip--disabled.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{opacity:var(--mdc-chip-with-icon-disabled-icon-opacity, 0.38)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{opacity:var(--mat-chip-disabled-container-opacity, 1)}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-trailing-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-remove{opacity:var(--mat-chip-trailing-action-opacity, 1)}.mat-mdc-chip-remove:focus{opacity:var(--mat-chip-trailing-action-focus-opacity, 1)}.mat-mdc-chip-remove::after{background-color:var(--mat-chip-trailing-action-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-remove:hover::after{opacity:var(--mat-chip-trailing-action-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-remove:focus::after{opacity:var(--mat-chip-trailing-action-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected .mat-mdc-chip-remove::after,.mat-mdc-chip-highlighted .mat-mdc-chip-remove::after{background-color:var(--mat-chip-selected-trailing-action-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-standard-chip .mdc-evolution-chip__cell--primary,.mat-mdc-standard-chip .mdc-evolution-chip__action--primary,.mat-mdc-standard-chip .mat-mdc-chip-action-label{overflow:visible}.mat-mdc-standard-chip .mat-mdc-chip-graphic,.mat-mdc-standard-chip .mat-mdc-chip-trailing-icon{box-sizing:content-box}.mat-mdc-standard-chip._mat-animation-noopable,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__graphic,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark-path{transition-duration:1ms;animation-duration:1ms}.mat-mdc-chip-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;opacity:0;border-radius:inherit;transition:opacity 150ms linear}._mat-animation-noopable .mat-mdc-chip-focus-overlay{transition:none}.mat-mdc-basic-chip .mat-mdc-chip-focus-overlay{display:none}.mat-mdc-chip .mat-ripple.mat-mdc-chip-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-chip-avatar{text-align:center;line-height:1;color:var(--mdc-chip-with-icon-icon-color, currentColor)}.mat-mdc-chip{position:relative;z-index:0}.mat-mdc-chip-action-label{text-align:left;z-index:1}[dir=rtl] .mat-mdc-chip-action-label{text-align:right}.mat-mdc-chip.mdc-evolution-chip--with-trailing-action .mat-mdc-chip-action-label{position:relative}.mat-mdc-chip-action-label .mat-mdc-chip-primary-focus-indicator{position:absolute;top:0;right:0;bottom:0;left:0;pointer-events:none}.mat-mdc-chip-action-label .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-mdc-chip-remove::before{margin:calc(var(--mat-focus-indicator-border-width, 3px)*-1);left:8px;right:8px}.mat-mdc-chip-remove::after{content:\\\"\\\";display:block;opacity:0;position:absolute;top:-3px;bottom:-3px;left:5px;right:5px;border-radius:50%;box-sizing:border-box;padding:12px;margin:-12px;background-clip:content-box}.mat-mdc-chip-remove .mat-icon{width:18px;height:18px;font-size:18px;box-sizing:content-box}.mat-chip-edit-input{cursor:text;display:inline-block;color:inherit;outline:0}@media(forced-colors: active){.mat-mdc-chip-selected:not(.mat-mdc-chip-multiple){outline-width:3px}}.mat-mdc-chip-action:focus .mat-focus-indicator::before{content:\\\"\\\"}.mdc-evolution-chip__icon,.mat-mdc-chip-remove .mat-icon{min-height:fit-content}\\n\"], dependencies: [{ kind: \"directive\", type: MatChipAction, selector: \"[matChipAction]\", inputs: [\"isInteractive\", \"disabled\", \"tabIndex\", \"_allowFocusWhenDisabled\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatChip, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-basic-chip, [mat-basic-chip], mat-chip, [mat-chip]', exportAs: 'matChip', host: {\n                        'class': 'mat-mdc-chip',\n                        '[class]': '\"mat-\" + (color || \"primary\")',\n                        '[class.mdc-evolution-chip]': '!_isBasicChip',\n                        '[class.mdc-evolution-chip--disabled]': 'disabled',\n                        '[class.mdc-evolution-chip--with-trailing-action]': '_hasTrailingIcon()',\n                        '[class.mdc-evolution-chip--with-primary-graphic]': 'leadingIcon',\n                        '[class.mdc-evolution-chip--with-primary-icon]': 'leadingIcon',\n                        '[class.mdc-evolution-chip--with-avatar]': 'leadingIcon',\n                        '[class.mat-mdc-chip-with-avatar]': 'leadingIcon',\n                        '[class.mat-mdc-chip-highlighted]': 'highlighted',\n                        '[class.mat-mdc-chip-disabled]': 'disabled',\n                        '[class.mat-mdc-basic-chip]': '_isBasicChip',\n                        '[class.mat-mdc-standard-chip]': '!_isBasicChip',\n                        '[class.mat-mdc-chip-with-trailing-icon]': '_hasTrailingIcon()',\n                        '[class._mat-animation-noopable]': '_animationsDisabled',\n                        '[id]': 'id',\n                        '[attr.role]': 'role',\n                        '[attr.aria-label]': 'ariaLabel',\n                        '(keydown)': '_handleKeydown($event)',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, providers: [{ provide: MAT_CHIP, useExisting: MatChip }], imports: [MatChipAction], template: \"<span class=\\\"mat-mdc-chip-focus-overlay\\\"></span>\\n\\n<span class=\\\"mdc-evolution-chip__cell mdc-evolution-chip__cell--primary\\\">\\n  <span matChipAction [isInteractive]=\\\"false\\\">\\n    @if (leadingIcon) {\\n      <span class=\\\"mdc-evolution-chip__graphic mat-mdc-chip-graphic\\\">\\n        <ng-content select=\\\"mat-chip-avatar, [matChipAvatar]\\\"></ng-content>\\n      </span>\\n    }\\n    <span class=\\\"mdc-evolution-chip__text-label mat-mdc-chip-action-label\\\">\\n      <ng-content></ng-content>\\n      <span class=\\\"mat-mdc-chip-primary-focus-indicator mat-focus-indicator\\\"></span>\\n    </span>\\n  </span>\\n</span>\\n\\n@if (_hasTrailingIcon()) {\\n  <span class=\\\"mdc-evolution-chip__cell mdc-evolution-chip__cell--trailing\\\">\\n    <ng-content select=\\\"mat-chip-trailing-icon,[matChipRemove],[matChipTrailingIcon]\\\"></ng-content>\\n  </span>\\n}\\n\", styles: [\".mdc-evolution-chip,.mdc-evolution-chip__cell,.mdc-evolution-chip__action{display:inline-flex;align-items:center}.mdc-evolution-chip{position:relative;max-width:100%}.mdc-evolution-chip__cell,.mdc-evolution-chip__action{height:100%}.mdc-evolution-chip__cell--primary{flex-basis:100%;overflow-x:hidden}.mdc-evolution-chip__cell--trailing{flex:1 0 auto}.mdc-evolution-chip__action{align-items:center;background:none;border:none;box-sizing:content-box;cursor:pointer;display:inline-flex;justify-content:center;outline:none;padding:0;text-decoration:none;color:inherit}.mdc-evolution-chip__action--presentational{cursor:auto}.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{pointer-events:none}@media(forced-colors: active){.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{forced-color-adjust:none}}.mdc-evolution-chip__action--primary{font:inherit;letter-spacing:inherit;white-space:inherit;overflow-x:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary::before{border-width:var(--mdc-chip-outline-width, 1px);border-radius:var(--mdc-chip-container-shape-radius, 8px);box-sizing:border-box;content:\\\"\\\";height:100%;left:0;position:absolute;pointer-events:none;top:0;width:100%;z-index:1;border-style:solid}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--primary::before{border-color:var(--mdc-chip-outline-color, var(--mat-sys-outline))}.mdc-evolution-chip__action--primary:not(.mdc-evolution-chip__action--presentational):not(.mdc-ripple-upgraded):focus::before{border-color:var(--mdc-chip-focus-outline-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--primary::before{border-color:var(--mdc-chip-disabled-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__action--primary::before{border-width:var(--mdc-chip-flat-selected-outline-width, 0)}.mat-mdc-basic-chip .mdc-evolution-chip__action--primary{font:inherit}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip__action--trailing{position:relative;overflow:visible}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--trailing{color:var(--mdc-chip-with-trailing-icon-trailing-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--trailing{color:var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip__text-label{-webkit-user-select:none;user-select:none;white-space:nowrap;text-overflow:ellipsis;overflow:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__text-label{font-family:var(--mdc-chip-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mdc-chip-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mdc-chip-label-text-size, var(--mat-sys-label-large-size));font-weight:var(--mdc-chip-label-text-weight, var(--mat-sys-label-large-weight));letter-spacing:var(--mdc-chip-label-text-tracking, var(--mat-sys-label-large-tracking))}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mdc-chip-label-text-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mdc-chip-selected-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label,.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label{color:var(--mdc-chip-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-evolution-chip__graphic{align-items:center;display:inline-flex;justify-content:center;overflow:hidden;pointer-events:none;position:relative;flex:1 0 auto}.mat-mdc-standard-chip .mdc-evolution-chip__graphic{width:var(--mdc-chip-with-avatar-avatar-size, 24px);height:var(--mdc-chip-with-avatar-avatar-size, 24px);font-size:var(--mdc-chip-with-avatar-avatar-size, 24px)}.mdc-evolution-chip--selecting .mdc-evolution-chip__graphic{transition:width 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selectable:not(.mdc-evolution-chip--selected):not(.mdc-evolution-chip--with-primary-icon) .mdc-evolution-chip__graphic{width:0}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mdc-evolution-chip__checkmark{position:absolute;opacity:0;top:50%;left:50%;height:20px;width:20px}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__checkmark{color:var(--mdc-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__checkmark{color:var(--mdc-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark{transition:transform 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1);transform:translate(-75%, -50%)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{transform:translate(-50%, -50%);opacity:1}.mdc-evolution-chip__checkmark-svg{display:block}.mdc-evolution-chip__checkmark-path{stroke-width:2px;stroke-dasharray:29.7833385;stroke-dashoffset:29.7833385;stroke:currentColor}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark-path{transition:stroke-dashoffset 150ms 45ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark-path{stroke-dashoffset:0}@media(forced-colors: active){.mdc-evolution-chip__checkmark-path{stroke:CanvasText !important}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--trailing{height:18px;width:18px;font-size:18px}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove{opacity:calc(var(--mat-chip-trailing-action-opacity, 1)*var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove:focus{opacity:calc(var(--mat-chip-trailing-action-focus-opacity, 1)*var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mat-mdc-standard-chip{border-radius:var(--mdc-chip-container-shape-radius, 8px);height:var(--mdc-chip-container-height, 32px)}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled){background-color:var(--mdc-chip-elevated-container-color, transparent)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{background-color:var(--mdc-chip-elevated-disabled-container-color)}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled){background-color:var(--mdc-chip-elevated-selected-container-color, var(--mat-sys-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled{background-color:var(--mdc-chip-flat-disabled-selected-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}@media(forced-colors: active){.mat-mdc-standard-chip{outline:solid 1px}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--primary{border-radius:var(--mdc-chip-with-avatar-avatar-shape-radius, 24px);width:var(--mdc-chip-with-icon-icon-size, 18px);height:var(--mdc-chip-with-icon-icon-size, 18px);font-size:var(--mdc-chip-with-icon-icon-size, 18px)}.mdc-evolution-chip--selected .mdc-evolution-chip__icon--primary{opacity:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__icon--primary{color:var(--mdc-chip-with-icon-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--primary{color:var(--mdc-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-highlighted{--mdc-chip-with-icon-icon-color:var(--mdc-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container));--mdc-chip-elevated-container-color:var(--mdc-chip-elevated-selected-container-color, var(--mat-sys-secondary-container));--mdc-chip-label-text-color:var(--mdc-chip-selected-label-text-color, var(--mat-sys-on-secondary-container));--mdc-chip-outline-width:var(--mdc-chip-flat-selected-outline-width, 0)}.mat-mdc-chip-focus-overlay{background:var(--mdc-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-selected .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-chip:hover .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-hover-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mdc-chip-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-focus-overlay .mat-mdc-chip-selected:hover,.mat-mdc-chip-highlighted:hover .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-hover-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mdc-chip-selected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mdc-chip-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected.cdk-focused .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mdc-chip-selected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-evolution-chip--disabled:not(.mdc-evolution-chip--selected) .mat-mdc-chip-avatar{opacity:var(--mdc-chip-with-avatar-disabled-avatar-opacity, 0.38)}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{opacity:var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38)}.mdc-evolution-chip--disabled.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{opacity:var(--mdc-chip-with-icon-disabled-icon-opacity, 0.38)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{opacity:var(--mat-chip-disabled-container-opacity, 1)}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-trailing-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-remove{opacity:var(--mat-chip-trailing-action-opacity, 1)}.mat-mdc-chip-remove:focus{opacity:var(--mat-chip-trailing-action-focus-opacity, 1)}.mat-mdc-chip-remove::after{background-color:var(--mat-chip-trailing-action-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-remove:hover::after{opacity:var(--mat-chip-trailing-action-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-remove:focus::after{opacity:var(--mat-chip-trailing-action-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected .mat-mdc-chip-remove::after,.mat-mdc-chip-highlighted .mat-mdc-chip-remove::after{background-color:var(--mat-chip-selected-trailing-action-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-standard-chip .mdc-evolution-chip__cell--primary,.mat-mdc-standard-chip .mdc-evolution-chip__action--primary,.mat-mdc-standard-chip .mat-mdc-chip-action-label{overflow:visible}.mat-mdc-standard-chip .mat-mdc-chip-graphic,.mat-mdc-standard-chip .mat-mdc-chip-trailing-icon{box-sizing:content-box}.mat-mdc-standard-chip._mat-animation-noopable,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__graphic,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark-path{transition-duration:1ms;animation-duration:1ms}.mat-mdc-chip-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;opacity:0;border-radius:inherit;transition:opacity 150ms linear}._mat-animation-noopable .mat-mdc-chip-focus-overlay{transition:none}.mat-mdc-basic-chip .mat-mdc-chip-focus-overlay{display:none}.mat-mdc-chip .mat-ripple.mat-mdc-chip-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-chip-avatar{text-align:center;line-height:1;color:var(--mdc-chip-with-icon-icon-color, currentColor)}.mat-mdc-chip{position:relative;z-index:0}.mat-mdc-chip-action-label{text-align:left;z-index:1}[dir=rtl] .mat-mdc-chip-action-label{text-align:right}.mat-mdc-chip.mdc-evolution-chip--with-trailing-action .mat-mdc-chip-action-label{position:relative}.mat-mdc-chip-action-label .mat-mdc-chip-primary-focus-indicator{position:absolute;top:0;right:0;bottom:0;left:0;pointer-events:none}.mat-mdc-chip-action-label .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-mdc-chip-remove::before{margin:calc(var(--mat-focus-indicator-border-width, 3px)*-1);left:8px;right:8px}.mat-mdc-chip-remove::after{content:\\\"\\\";display:block;opacity:0;position:absolute;top:-3px;bottom:-3px;left:5px;right:5px;border-radius:50%;box-sizing:border-box;padding:12px;margin:-12px;background-clip:content-box}.mat-mdc-chip-remove .mat-icon{width:18px;height:18px;font-size:18px;box-sizing:content-box}.mat-chip-edit-input{cursor:text;display:inline-block;color:inherit;outline:0}@media(forced-colors: active){.mat-mdc-chip-selected:not(.mat-mdc-chip-multiple){outline-width:3px}}.mat-mdc-chip-action:focus .mat-focus-indicator::before{content:\\\"\\\"}.mdc-evolution-chip__icon,.mat-mdc-chip-remove .mat-icon{min-height:fit-content}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { role: [{\n                type: Input\n            }], _allLeadingIcons: [{\n                type: ContentChildren,\n                args: [MAT_CHIP_AVATAR, { descendants: true }]\n            }], _allTrailingIcons: [{\n                type: ContentChildren,\n                args: [MAT_CHIP_TRAILING_ICON, { descendants: true }]\n            }], _allRemoveIcons: [{\n                type: ContentChildren,\n                args: [MAT_CHIP_REMOVE, { descendants: true }]\n            }], id: [{\n                type: Input\n            }], ariaLabel: [{\n                type: Input,\n                args: ['aria-label']\n            }], ariaDescription: [{\n                type: Input,\n                args: ['aria-description']\n            }], value: [{\n                type: Input\n            }], color: [{\n                type: Input\n            }], removable: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], highlighted: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], disableRipple: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], removed: [{\n                type: Output\n            }], destroyed: [{\n                type: Output\n            }], leadingIcon: [{\n                type: ContentChild,\n                args: [MAT_CHIP_AVATAR]\n            }], trailingIcon: [{\n                type: ContentChild,\n                args: [MAT_CHIP_TRAILING_ICON]\n            }], removeIcon: [{\n                type: ContentChild,\n                args: [MAT_CHIP_REMOVE]\n            }], primaryAction: [{\n                type: ViewChild,\n                args: [MatChipAction]\n            }] } });\n\n/** Event object emitted by MatChipOption when selected or deselected. */\nclass MatChipSelectionChange {\n    source;\n    selected;\n    isUserInput;\n    constructor(\n    /** Reference to the chip that emitted the event. */\n    source, \n    /** Whether the chip that emitted the event is selected. */\n    selected, \n    /** Whether the selection change was a result of a user interaction. */\n    isUserInput = false) {\n        this.source = source;\n        this.selected = selected;\n        this.isUserInput = isUserInput;\n    }\n}\n/**\n * An extension of the MatChip component that supports chip selection. Used with MatChipListbox.\n *\n * Unlike other chips, the user can focus on disabled chip options inside a MatChipListbox. The\n * user cannot click disabled chips.\n */\nclass MatChipOption extends MatChip {\n    /** Default chip options. */\n    _defaultOptions = inject(MAT_CHIPS_DEFAULT_OPTIONS, { optional: true });\n    /** Whether the chip list is selectable. */\n    chipListSelectable = true;\n    /** Whether the chip list is in multi-selection mode. */\n    _chipListMultiple = false;\n    /** Whether the chip list hides single-selection indicator. */\n    _chipListHideSingleSelectionIndicator = this._defaultOptions?.hideSingleSelectionIndicator ?? false;\n    /**\n     * Whether or not the chip is selectable.\n     *\n     * When a chip is not selectable, changes to its selected state are always\n     * ignored. By default an option chip is selectable, and it becomes\n     * non-selectable if its parent chip list is not selectable.\n     */\n    get selectable() {\n        return this._selectable && this.chipListSelectable;\n    }\n    set selectable(value) {\n        this._selectable = value;\n        this._changeDetectorRef.markForCheck();\n    }\n    _selectable = true;\n    /** Whether the chip is selected. */\n    get selected() {\n        return this._selected;\n    }\n    set selected(value) {\n        this._setSelectedState(value, false, true);\n    }\n    _selected = false;\n    /**\n     * The ARIA selected applied to the chip. Conforms to WAI ARIA best practices for listbox\n     * interaction patterns.\n     *\n     * From [WAI ARIA Listbox authoring practices guide](\n     * https://www.w3.org/WAI/ARIA/apg/patterns/listbox/):\n     *  \"If any options are selected, each selected option has either aria-selected or aria-checked\n     *  set to true. All options that are selectable but not selected have either aria-selected or\n     *  aria-checked set to false.\"\n     *\n     * Set `aria-selected=\"false\"` on not-selected listbox options that are selectable to fix\n     * VoiceOver reading every option as \"selected\" (#25736).\n     */\n    get ariaSelected() {\n        return this.selectable ? this.selected.toString() : null;\n    }\n    /** The unstyled chip selector for this component. */\n    basicChipAttrName = 'mat-basic-chip-option';\n    /** Emitted when the chip is selected or deselected. */\n    selectionChange = new EventEmitter();\n    ngOnInit() {\n        super.ngOnInit();\n        this.role = 'presentation';\n    }\n    /** Selects the chip. */\n    select() {\n        this._setSelectedState(true, false, true);\n    }\n    /** Deselects the chip. */\n    deselect() {\n        this._setSelectedState(false, false, true);\n    }\n    /** Selects this chip and emits userInputSelection event */\n    selectViaInteraction() {\n        this._setSelectedState(true, true, true);\n    }\n    /** Toggles the current selected state of this chip. */\n    toggleSelected(isUserInput = false) {\n        this._setSelectedState(!this.selected, isUserInput, true);\n        return this.selected;\n    }\n    _handlePrimaryActionInteraction() {\n        if (!this.disabled) {\n            // Interacting with the primary action implies that the chip already has focus, however\n            // there's a bug in Safari where focus ends up lingering on the previous chip (see #27544).\n            // We work around it by explicitly focusing the primary action of the current chip.\n            this.focus();\n            if (this.selectable) {\n                this.toggleSelected(true);\n            }\n        }\n    }\n    _hasLeadingGraphic() {\n        if (this.leadingIcon) {\n            return true;\n        }\n        // The checkmark graphic communicates selected state for both single-select and multi-select.\n        // Include checkmark in single-select to fix a11y issue where selected state is communicated\n        // visually only using color (#25886).\n        return !this._chipListHideSingleSelectionIndicator || this._chipListMultiple;\n    }\n    _setSelectedState(isSelected, isUserInput, emitEvent) {\n        if (isSelected !== this.selected) {\n            this._selected = isSelected;\n            if (emitEvent) {\n                this.selectionChange.emit({\n                    source: this,\n                    isUserInput,\n                    selected: this.selected,\n                });\n            }\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatChipOption, deps: null, target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"19.2.6\", type: MatChipOption, isStandalone: true, selector: \"mat-basic-chip-option, [mat-basic-chip-option], mat-chip-option, [mat-chip-option]\", inputs: { selectable: [\"selectable\", \"selectable\", booleanAttribute], selected: [\"selected\", \"selected\", booleanAttribute] }, outputs: { selectionChange: \"selectionChange\" }, host: { properties: { \"class.mdc-evolution-chip\": \"!_isBasicChip\", \"class.mdc-evolution-chip--filter\": \"!_isBasicChip\", \"class.mdc-evolution-chip--selectable\": \"!_isBasicChip\", \"class.mat-mdc-chip-selected\": \"selected\", \"class.mat-mdc-chip-multiple\": \"_chipListMultiple\", \"class.mat-mdc-chip-disabled\": \"disabled\", \"class.mat-mdc-chip-with-avatar\": \"leadingIcon\", \"class.mdc-evolution-chip--disabled\": \"disabled\", \"class.mdc-evolution-chip--selected\": \"selected\", \"class.mdc-evolution-chip--selecting\": \"!_animationsDisabled\", \"class.mdc-evolution-chip--with-trailing-action\": \"_hasTrailingIcon()\", \"class.mdc-evolution-chip--with-primary-icon\": \"leadingIcon\", \"class.mdc-evolution-chip--with-primary-graphic\": \"_hasLeadingGraphic()\", \"class.mdc-evolution-chip--with-avatar\": \"leadingIcon\", \"class.mat-mdc-chip-highlighted\": \"highlighted\", \"class.mat-mdc-chip-with-trailing-icon\": \"_hasTrailingIcon()\", \"attr.tabindex\": \"null\", \"attr.aria-label\": \"null\", \"attr.aria-description\": \"null\", \"attr.role\": \"role\", \"id\": \"id\" }, classAttribute: \"mat-mdc-chip mat-mdc-chip-option\" }, providers: [\n            { provide: MatChip, useExisting: MatChipOption },\n            { provide: MAT_CHIP, useExisting: MatChipOption },\n        ], usesInheritance: true, ngImport: i0, template: \"<span class=\\\"mat-mdc-chip-focus-overlay\\\"></span>\\n\\n<span class=\\\"mdc-evolution-chip__cell mdc-evolution-chip__cell--primary\\\">\\n  <button\\n    matChipAction\\n    [_allowFocusWhenDisabled]=\\\"true\\\"\\n    [attr.aria-selected]=\\\"ariaSelected\\\"\\n    [attr.aria-label]=\\\"ariaLabel\\\"\\n    [attr.aria-describedby]=\\\"_ariaDescriptionId\\\"\\n    role=\\\"option\\\">\\n    @if (_hasLeadingGraphic()) {\\n      <span class=\\\"mdc-evolution-chip__graphic mat-mdc-chip-graphic\\\">\\n        <ng-content select=\\\"mat-chip-avatar, [matChipAvatar]\\\"></ng-content>\\n        <span class=\\\"mdc-evolution-chip__checkmark\\\">\\n          <svg\\n            class=\\\"mdc-evolution-chip__checkmark-svg\\\"\\n            viewBox=\\\"-2 -3 30 30\\\"\\n            focusable=\\\"false\\\"\\n            aria-hidden=\\\"true\\\">\\n            <path class=\\\"mdc-evolution-chip__checkmark-path\\\"\\n                  fill=\\\"none\\\" stroke=\\\"currentColor\\\" d=\\\"M1.73,12.91 8.1,19.28 22.79,4.59\\\" />\\n          </svg>\\n        </span>\\n      </span>\\n    }\\n    <span class=\\\"mdc-evolution-chip__text-label mat-mdc-chip-action-label\\\">\\n      <ng-content></ng-content>\\n      <span class=\\\"mat-mdc-chip-primary-focus-indicator mat-focus-indicator\\\"></span>\\n    </span>\\n  </button>\\n</span>\\n\\n@if (_hasTrailingIcon()) {\\n  <span class=\\\"mdc-evolution-chip__cell mdc-evolution-chip__cell--trailing\\\">\\n    <ng-content select=\\\"mat-chip-trailing-icon,[matChipRemove],[matChipTrailingIcon]\\\"></ng-content>\\n  </span>\\n}\\n\\n<span class=\\\"cdk-visually-hidden\\\" [id]=\\\"_ariaDescriptionId\\\">{{ariaDescription}}</span>\\n\", styles: [\".mdc-evolution-chip,.mdc-evolution-chip__cell,.mdc-evolution-chip__action{display:inline-flex;align-items:center}.mdc-evolution-chip{position:relative;max-width:100%}.mdc-evolution-chip__cell,.mdc-evolution-chip__action{height:100%}.mdc-evolution-chip__cell--primary{flex-basis:100%;overflow-x:hidden}.mdc-evolution-chip__cell--trailing{flex:1 0 auto}.mdc-evolution-chip__action{align-items:center;background:none;border:none;box-sizing:content-box;cursor:pointer;display:inline-flex;justify-content:center;outline:none;padding:0;text-decoration:none;color:inherit}.mdc-evolution-chip__action--presentational{cursor:auto}.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{pointer-events:none}@media(forced-colors: active){.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{forced-color-adjust:none}}.mdc-evolution-chip__action--primary{font:inherit;letter-spacing:inherit;white-space:inherit;overflow-x:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary::before{border-width:var(--mdc-chip-outline-width, 1px);border-radius:var(--mdc-chip-container-shape-radius, 8px);box-sizing:border-box;content:\\\"\\\";height:100%;left:0;position:absolute;pointer-events:none;top:0;width:100%;z-index:1;border-style:solid}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--primary::before{border-color:var(--mdc-chip-outline-color, var(--mat-sys-outline))}.mdc-evolution-chip__action--primary:not(.mdc-evolution-chip__action--presentational):not(.mdc-ripple-upgraded):focus::before{border-color:var(--mdc-chip-focus-outline-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--primary::before{border-color:var(--mdc-chip-disabled-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__action--primary::before{border-width:var(--mdc-chip-flat-selected-outline-width, 0)}.mat-mdc-basic-chip .mdc-evolution-chip__action--primary{font:inherit}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip__action--trailing{position:relative;overflow:visible}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--trailing{color:var(--mdc-chip-with-trailing-icon-trailing-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--trailing{color:var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip__text-label{-webkit-user-select:none;user-select:none;white-space:nowrap;text-overflow:ellipsis;overflow:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__text-label{font-family:var(--mdc-chip-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mdc-chip-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mdc-chip-label-text-size, var(--mat-sys-label-large-size));font-weight:var(--mdc-chip-label-text-weight, var(--mat-sys-label-large-weight));letter-spacing:var(--mdc-chip-label-text-tracking, var(--mat-sys-label-large-tracking))}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mdc-chip-label-text-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mdc-chip-selected-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label,.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label{color:var(--mdc-chip-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-evolution-chip__graphic{align-items:center;display:inline-flex;justify-content:center;overflow:hidden;pointer-events:none;position:relative;flex:1 0 auto}.mat-mdc-standard-chip .mdc-evolution-chip__graphic{width:var(--mdc-chip-with-avatar-avatar-size, 24px);height:var(--mdc-chip-with-avatar-avatar-size, 24px);font-size:var(--mdc-chip-with-avatar-avatar-size, 24px)}.mdc-evolution-chip--selecting .mdc-evolution-chip__graphic{transition:width 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selectable:not(.mdc-evolution-chip--selected):not(.mdc-evolution-chip--with-primary-icon) .mdc-evolution-chip__graphic{width:0}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mdc-evolution-chip__checkmark{position:absolute;opacity:0;top:50%;left:50%;height:20px;width:20px}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__checkmark{color:var(--mdc-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__checkmark{color:var(--mdc-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark{transition:transform 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1);transform:translate(-75%, -50%)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{transform:translate(-50%, -50%);opacity:1}.mdc-evolution-chip__checkmark-svg{display:block}.mdc-evolution-chip__checkmark-path{stroke-width:2px;stroke-dasharray:29.7833385;stroke-dashoffset:29.7833385;stroke:currentColor}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark-path{transition:stroke-dashoffset 150ms 45ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark-path{stroke-dashoffset:0}@media(forced-colors: active){.mdc-evolution-chip__checkmark-path{stroke:CanvasText !important}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--trailing{height:18px;width:18px;font-size:18px}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove{opacity:calc(var(--mat-chip-trailing-action-opacity, 1)*var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove:focus{opacity:calc(var(--mat-chip-trailing-action-focus-opacity, 1)*var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mat-mdc-standard-chip{border-radius:var(--mdc-chip-container-shape-radius, 8px);height:var(--mdc-chip-container-height, 32px)}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled){background-color:var(--mdc-chip-elevated-container-color, transparent)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{background-color:var(--mdc-chip-elevated-disabled-container-color)}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled){background-color:var(--mdc-chip-elevated-selected-container-color, var(--mat-sys-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled{background-color:var(--mdc-chip-flat-disabled-selected-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}@media(forced-colors: active){.mat-mdc-standard-chip{outline:solid 1px}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--primary{border-radius:var(--mdc-chip-with-avatar-avatar-shape-radius, 24px);width:var(--mdc-chip-with-icon-icon-size, 18px);height:var(--mdc-chip-with-icon-icon-size, 18px);font-size:var(--mdc-chip-with-icon-icon-size, 18px)}.mdc-evolution-chip--selected .mdc-evolution-chip__icon--primary{opacity:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__icon--primary{color:var(--mdc-chip-with-icon-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--primary{color:var(--mdc-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-highlighted{--mdc-chip-with-icon-icon-color:var(--mdc-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container));--mdc-chip-elevated-container-color:var(--mdc-chip-elevated-selected-container-color, var(--mat-sys-secondary-container));--mdc-chip-label-text-color:var(--mdc-chip-selected-label-text-color, var(--mat-sys-on-secondary-container));--mdc-chip-outline-width:var(--mdc-chip-flat-selected-outline-width, 0)}.mat-mdc-chip-focus-overlay{background:var(--mdc-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-selected .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-chip:hover .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-hover-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mdc-chip-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-focus-overlay .mat-mdc-chip-selected:hover,.mat-mdc-chip-highlighted:hover .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-hover-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mdc-chip-selected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mdc-chip-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected.cdk-focused .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mdc-chip-selected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-evolution-chip--disabled:not(.mdc-evolution-chip--selected) .mat-mdc-chip-avatar{opacity:var(--mdc-chip-with-avatar-disabled-avatar-opacity, 0.38)}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{opacity:var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38)}.mdc-evolution-chip--disabled.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{opacity:var(--mdc-chip-with-icon-disabled-icon-opacity, 0.38)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{opacity:var(--mat-chip-disabled-container-opacity, 1)}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-trailing-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-remove{opacity:var(--mat-chip-trailing-action-opacity, 1)}.mat-mdc-chip-remove:focus{opacity:var(--mat-chip-trailing-action-focus-opacity, 1)}.mat-mdc-chip-remove::after{background-color:var(--mat-chip-trailing-action-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-remove:hover::after{opacity:var(--mat-chip-trailing-action-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-remove:focus::after{opacity:var(--mat-chip-trailing-action-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected .mat-mdc-chip-remove::after,.mat-mdc-chip-highlighted .mat-mdc-chip-remove::after{background-color:var(--mat-chip-selected-trailing-action-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-standard-chip .mdc-evolution-chip__cell--primary,.mat-mdc-standard-chip .mdc-evolution-chip__action--primary,.mat-mdc-standard-chip .mat-mdc-chip-action-label{overflow:visible}.mat-mdc-standard-chip .mat-mdc-chip-graphic,.mat-mdc-standard-chip .mat-mdc-chip-trailing-icon{box-sizing:content-box}.mat-mdc-standard-chip._mat-animation-noopable,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__graphic,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark-path{transition-duration:1ms;animation-duration:1ms}.mat-mdc-chip-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;opacity:0;border-radius:inherit;transition:opacity 150ms linear}._mat-animation-noopable .mat-mdc-chip-focus-overlay{transition:none}.mat-mdc-basic-chip .mat-mdc-chip-focus-overlay{display:none}.mat-mdc-chip .mat-ripple.mat-mdc-chip-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-chip-avatar{text-align:center;line-height:1;color:var(--mdc-chip-with-icon-icon-color, currentColor)}.mat-mdc-chip{position:relative;z-index:0}.mat-mdc-chip-action-label{text-align:left;z-index:1}[dir=rtl] .mat-mdc-chip-action-label{text-align:right}.mat-mdc-chip.mdc-evolution-chip--with-trailing-action .mat-mdc-chip-action-label{position:relative}.mat-mdc-chip-action-label .mat-mdc-chip-primary-focus-indicator{position:absolute;top:0;right:0;bottom:0;left:0;pointer-events:none}.mat-mdc-chip-action-label .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-mdc-chip-remove::before{margin:calc(var(--mat-focus-indicator-border-width, 3px)*-1);left:8px;right:8px}.mat-mdc-chip-remove::after{content:\\\"\\\";display:block;opacity:0;position:absolute;top:-3px;bottom:-3px;left:5px;right:5px;border-radius:50%;box-sizing:border-box;padding:12px;margin:-12px;background-clip:content-box}.mat-mdc-chip-remove .mat-icon{width:18px;height:18px;font-size:18px;box-sizing:content-box}.mat-chip-edit-input{cursor:text;display:inline-block;color:inherit;outline:0}@media(forced-colors: active){.mat-mdc-chip-selected:not(.mat-mdc-chip-multiple){outline-width:3px}}.mat-mdc-chip-action:focus .mat-focus-indicator::before{content:\\\"\\\"}.mdc-evolution-chip__icon,.mat-mdc-chip-remove .mat-icon{min-height:fit-content}\\n\"], dependencies: [{ kind: \"directive\", type: MatChipAction, selector: \"[matChipAction]\", inputs: [\"isInteractive\", \"disabled\", \"tabIndex\", \"_allowFocusWhenDisabled\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatChipOption, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-basic-chip-option, [mat-basic-chip-option], mat-chip-option, [mat-chip-option]', host: {\n                        'class': 'mat-mdc-chip mat-mdc-chip-option',\n                        '[class.mdc-evolution-chip]': '!_isBasicChip',\n                        '[class.mdc-evolution-chip--filter]': '!_isBasicChip',\n                        '[class.mdc-evolution-chip--selectable]': '!_isBasicChip',\n                        '[class.mat-mdc-chip-selected]': 'selected',\n                        '[class.mat-mdc-chip-multiple]': '_chipListMultiple',\n                        '[class.mat-mdc-chip-disabled]': 'disabled',\n                        '[class.mat-mdc-chip-with-avatar]': 'leadingIcon',\n                        '[class.mdc-evolution-chip--disabled]': 'disabled',\n                        '[class.mdc-evolution-chip--selected]': 'selected',\n                        // This class enables the transition on the checkmark. Usually MDC adds it when selection\n                        // starts and removes it once the animation is finished. We don't need to go through all\n                        // the trouble, because we only care about the selection animation. MDC needs to do it,\n                        // because they also have an exit animation that we don't care about.\n                        '[class.mdc-evolution-chip--selecting]': '!_animationsDisabled',\n                        '[class.mdc-evolution-chip--with-trailing-action]': '_hasTrailingIcon()',\n                        '[class.mdc-evolution-chip--with-primary-icon]': 'leadingIcon',\n                        '[class.mdc-evolution-chip--with-primary-graphic]': '_hasLeadingGraphic()',\n                        '[class.mdc-evolution-chip--with-avatar]': 'leadingIcon',\n                        '[class.mat-mdc-chip-highlighted]': 'highlighted',\n                        '[class.mat-mdc-chip-with-trailing-icon]': '_hasTrailingIcon()',\n                        '[attr.tabindex]': 'null',\n                        '[attr.aria-label]': 'null',\n                        '[attr.aria-description]': 'null',\n                        '[attr.role]': 'role',\n                        '[id]': 'id',\n                    }, providers: [\n                        { provide: MatChip, useExisting: MatChipOption },\n                        { provide: MAT_CHIP, useExisting: MatChipOption },\n                    ], encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, imports: [MatChipAction], template: \"<span class=\\\"mat-mdc-chip-focus-overlay\\\"></span>\\n\\n<span class=\\\"mdc-evolution-chip__cell mdc-evolution-chip__cell--primary\\\">\\n  <button\\n    matChipAction\\n    [_allowFocusWhenDisabled]=\\\"true\\\"\\n    [attr.aria-selected]=\\\"ariaSelected\\\"\\n    [attr.aria-label]=\\\"ariaLabel\\\"\\n    [attr.aria-describedby]=\\\"_ariaDescriptionId\\\"\\n    role=\\\"option\\\">\\n    @if (_hasLeadingGraphic()) {\\n      <span class=\\\"mdc-evolution-chip__graphic mat-mdc-chip-graphic\\\">\\n        <ng-content select=\\\"mat-chip-avatar, [matChipAvatar]\\\"></ng-content>\\n        <span class=\\\"mdc-evolution-chip__checkmark\\\">\\n          <svg\\n            class=\\\"mdc-evolution-chip__checkmark-svg\\\"\\n            viewBox=\\\"-2 -3 30 30\\\"\\n            focusable=\\\"false\\\"\\n            aria-hidden=\\\"true\\\">\\n            <path class=\\\"mdc-evolution-chip__checkmark-path\\\"\\n                  fill=\\\"none\\\" stroke=\\\"currentColor\\\" d=\\\"M1.73,12.91 8.1,19.28 22.79,4.59\\\" />\\n          </svg>\\n        </span>\\n      </span>\\n    }\\n    <span class=\\\"mdc-evolution-chip__text-label mat-mdc-chip-action-label\\\">\\n      <ng-content></ng-content>\\n      <span class=\\\"mat-mdc-chip-primary-focus-indicator mat-focus-indicator\\\"></span>\\n    </span>\\n  </button>\\n</span>\\n\\n@if (_hasTrailingIcon()) {\\n  <span class=\\\"mdc-evolution-chip__cell mdc-evolution-chip__cell--trailing\\\">\\n    <ng-content select=\\\"mat-chip-trailing-icon,[matChipRemove],[matChipTrailingIcon]\\\"></ng-content>\\n  </span>\\n}\\n\\n<span class=\\\"cdk-visually-hidden\\\" [id]=\\\"_ariaDescriptionId\\\">{{ariaDescription}}</span>\\n\", styles: [\".mdc-evolution-chip,.mdc-evolution-chip__cell,.mdc-evolution-chip__action{display:inline-flex;align-items:center}.mdc-evolution-chip{position:relative;max-width:100%}.mdc-evolution-chip__cell,.mdc-evolution-chip__action{height:100%}.mdc-evolution-chip__cell--primary{flex-basis:100%;overflow-x:hidden}.mdc-evolution-chip__cell--trailing{flex:1 0 auto}.mdc-evolution-chip__action{align-items:center;background:none;border:none;box-sizing:content-box;cursor:pointer;display:inline-flex;justify-content:center;outline:none;padding:0;text-decoration:none;color:inherit}.mdc-evolution-chip__action--presentational{cursor:auto}.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{pointer-events:none}@media(forced-colors: active){.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{forced-color-adjust:none}}.mdc-evolution-chip__action--primary{font:inherit;letter-spacing:inherit;white-space:inherit;overflow-x:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary::before{border-width:var(--mdc-chip-outline-width, 1px);border-radius:var(--mdc-chip-container-shape-radius, 8px);box-sizing:border-box;content:\\\"\\\";height:100%;left:0;position:absolute;pointer-events:none;top:0;width:100%;z-index:1;border-style:solid}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--primary::before{border-color:var(--mdc-chip-outline-color, var(--mat-sys-outline))}.mdc-evolution-chip__action--primary:not(.mdc-evolution-chip__action--presentational):not(.mdc-ripple-upgraded):focus::before{border-color:var(--mdc-chip-focus-outline-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--primary::before{border-color:var(--mdc-chip-disabled-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__action--primary::before{border-width:var(--mdc-chip-flat-selected-outline-width, 0)}.mat-mdc-basic-chip .mdc-evolution-chip__action--primary{font:inherit}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip__action--trailing{position:relative;overflow:visible}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--trailing{color:var(--mdc-chip-with-trailing-icon-trailing-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--trailing{color:var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip__text-label{-webkit-user-select:none;user-select:none;white-space:nowrap;text-overflow:ellipsis;overflow:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__text-label{font-family:var(--mdc-chip-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mdc-chip-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mdc-chip-label-text-size, var(--mat-sys-label-large-size));font-weight:var(--mdc-chip-label-text-weight, var(--mat-sys-label-large-weight));letter-spacing:var(--mdc-chip-label-text-tracking, var(--mat-sys-label-large-tracking))}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mdc-chip-label-text-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mdc-chip-selected-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label,.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label{color:var(--mdc-chip-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-evolution-chip__graphic{align-items:center;display:inline-flex;justify-content:center;overflow:hidden;pointer-events:none;position:relative;flex:1 0 auto}.mat-mdc-standard-chip .mdc-evolution-chip__graphic{width:var(--mdc-chip-with-avatar-avatar-size, 24px);height:var(--mdc-chip-with-avatar-avatar-size, 24px);font-size:var(--mdc-chip-with-avatar-avatar-size, 24px)}.mdc-evolution-chip--selecting .mdc-evolution-chip__graphic{transition:width 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selectable:not(.mdc-evolution-chip--selected):not(.mdc-evolution-chip--with-primary-icon) .mdc-evolution-chip__graphic{width:0}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mdc-evolution-chip__checkmark{position:absolute;opacity:0;top:50%;left:50%;height:20px;width:20px}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__checkmark{color:var(--mdc-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__checkmark{color:var(--mdc-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark{transition:transform 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1);transform:translate(-75%, -50%)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{transform:translate(-50%, -50%);opacity:1}.mdc-evolution-chip__checkmark-svg{display:block}.mdc-evolution-chip__checkmark-path{stroke-width:2px;stroke-dasharray:29.7833385;stroke-dashoffset:29.7833385;stroke:currentColor}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark-path{transition:stroke-dashoffset 150ms 45ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark-path{stroke-dashoffset:0}@media(forced-colors: active){.mdc-evolution-chip__checkmark-path{stroke:CanvasText !important}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--trailing{height:18px;width:18px;font-size:18px}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove{opacity:calc(var(--mat-chip-trailing-action-opacity, 1)*var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove:focus{opacity:calc(var(--mat-chip-trailing-action-focus-opacity, 1)*var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mat-mdc-standard-chip{border-radius:var(--mdc-chip-container-shape-radius, 8px);height:var(--mdc-chip-container-height, 32px)}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled){background-color:var(--mdc-chip-elevated-container-color, transparent)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{background-color:var(--mdc-chip-elevated-disabled-container-color)}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled){background-color:var(--mdc-chip-elevated-selected-container-color, var(--mat-sys-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled{background-color:var(--mdc-chip-flat-disabled-selected-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}@media(forced-colors: active){.mat-mdc-standard-chip{outline:solid 1px}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--primary{border-radius:var(--mdc-chip-with-avatar-avatar-shape-radius, 24px);width:var(--mdc-chip-with-icon-icon-size, 18px);height:var(--mdc-chip-with-icon-icon-size, 18px);font-size:var(--mdc-chip-with-icon-icon-size, 18px)}.mdc-evolution-chip--selected .mdc-evolution-chip__icon--primary{opacity:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__icon--primary{color:var(--mdc-chip-with-icon-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--primary{color:var(--mdc-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-highlighted{--mdc-chip-with-icon-icon-color:var(--mdc-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container));--mdc-chip-elevated-container-color:var(--mdc-chip-elevated-selected-container-color, var(--mat-sys-secondary-container));--mdc-chip-label-text-color:var(--mdc-chip-selected-label-text-color, var(--mat-sys-on-secondary-container));--mdc-chip-outline-width:var(--mdc-chip-flat-selected-outline-width, 0)}.mat-mdc-chip-focus-overlay{background:var(--mdc-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-selected .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-chip:hover .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-hover-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mdc-chip-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-focus-overlay .mat-mdc-chip-selected:hover,.mat-mdc-chip-highlighted:hover .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-hover-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mdc-chip-selected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mdc-chip-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected.cdk-focused .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mdc-chip-selected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-evolution-chip--disabled:not(.mdc-evolution-chip--selected) .mat-mdc-chip-avatar{opacity:var(--mdc-chip-with-avatar-disabled-avatar-opacity, 0.38)}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{opacity:var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38)}.mdc-evolution-chip--disabled.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{opacity:var(--mdc-chip-with-icon-disabled-icon-opacity, 0.38)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{opacity:var(--mat-chip-disabled-container-opacity, 1)}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-trailing-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-remove{opacity:var(--mat-chip-trailing-action-opacity, 1)}.mat-mdc-chip-remove:focus{opacity:var(--mat-chip-trailing-action-focus-opacity, 1)}.mat-mdc-chip-remove::after{background-color:var(--mat-chip-trailing-action-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-remove:hover::after{opacity:var(--mat-chip-trailing-action-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-remove:focus::after{opacity:var(--mat-chip-trailing-action-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected .mat-mdc-chip-remove::after,.mat-mdc-chip-highlighted .mat-mdc-chip-remove::after{background-color:var(--mat-chip-selected-trailing-action-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-standard-chip .mdc-evolution-chip__cell--primary,.mat-mdc-standard-chip .mdc-evolution-chip__action--primary,.mat-mdc-standard-chip .mat-mdc-chip-action-label{overflow:visible}.mat-mdc-standard-chip .mat-mdc-chip-graphic,.mat-mdc-standard-chip .mat-mdc-chip-trailing-icon{box-sizing:content-box}.mat-mdc-standard-chip._mat-animation-noopable,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__graphic,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark-path{transition-duration:1ms;animation-duration:1ms}.mat-mdc-chip-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;opacity:0;border-radius:inherit;transition:opacity 150ms linear}._mat-animation-noopable .mat-mdc-chip-focus-overlay{transition:none}.mat-mdc-basic-chip .mat-mdc-chip-focus-overlay{display:none}.mat-mdc-chip .mat-ripple.mat-mdc-chip-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-chip-avatar{text-align:center;line-height:1;color:var(--mdc-chip-with-icon-icon-color, currentColor)}.mat-mdc-chip{position:relative;z-index:0}.mat-mdc-chip-action-label{text-align:left;z-index:1}[dir=rtl] .mat-mdc-chip-action-label{text-align:right}.mat-mdc-chip.mdc-evolution-chip--with-trailing-action .mat-mdc-chip-action-label{position:relative}.mat-mdc-chip-action-label .mat-mdc-chip-primary-focus-indicator{position:absolute;top:0;right:0;bottom:0;left:0;pointer-events:none}.mat-mdc-chip-action-label .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-mdc-chip-remove::before{margin:calc(var(--mat-focus-indicator-border-width, 3px)*-1);left:8px;right:8px}.mat-mdc-chip-remove::after{content:\\\"\\\";display:block;opacity:0;position:absolute;top:-3px;bottom:-3px;left:5px;right:5px;border-radius:50%;box-sizing:border-box;padding:12px;margin:-12px;background-clip:content-box}.mat-mdc-chip-remove .mat-icon{width:18px;height:18px;font-size:18px;box-sizing:content-box}.mat-chip-edit-input{cursor:text;display:inline-block;color:inherit;outline:0}@media(forced-colors: active){.mat-mdc-chip-selected:not(.mat-mdc-chip-multiple){outline-width:3px}}.mat-mdc-chip-action:focus .mat-focus-indicator::before{content:\\\"\\\"}.mdc-evolution-chip__icon,.mat-mdc-chip-remove .mat-icon{min-height:fit-content}\\n\"] }]\n        }], propDecorators: { selectable: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], selected: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], selectionChange: [{\n                type: Output\n            }] } });\n\n/**\n * A directive that makes a span editable and exposes functions to modify and retrieve the\n * element's contents.\n */\nclass MatChipEditInput {\n    _elementRef = inject(ElementRef);\n    _document = inject(DOCUMENT);\n    constructor() { }\n    initialize(initialValue) {\n        this.getNativeElement().focus();\n        this.setValue(initialValue);\n    }\n    getNativeElement() {\n        return this._elementRef.nativeElement;\n    }\n    setValue(value) {\n        this.getNativeElement().textContent = value;\n        this._moveCursorToEndOfInput();\n    }\n    getValue() {\n        return this.getNativeElement().textContent || '';\n    }\n    _moveCursorToEndOfInput() {\n        const range = this._document.createRange();\n        range.selectNodeContents(this.getNativeElement());\n        range.collapse(false);\n        const sel = window.getSelection();\n        sel.removeAllRanges();\n        sel.addRange(range);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatChipEditInput, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"19.2.6\", type: MatChipEditInput, isStandalone: true, selector: \"span[matChipEditInput]\", host: { attributes: { \"role\": \"textbox\", \"tabindex\": \"-1\", \"contenteditable\": \"true\" }, classAttribute: \"mat-chip-edit-input\" }, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatChipEditInput, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'span[matChipEditInput]',\n                    host: {\n                        'class': 'mat-chip-edit-input',\n                        'role': 'textbox',\n                        'tabindex': '-1',\n                        'contenteditable': 'true',\n                    },\n                }]\n        }], ctorParameters: () => [] });\n\n/**\n * An extension of the MatChip component used with MatChipGrid and\n * the matChipInputFor directive.\n */\nclass MatChipRow extends MatChip {\n    basicChipAttrName = 'mat-basic-chip-row';\n    /**\n     * The editing action has to be triggered in a timeout. While we're waiting on it, a blur\n     * event might occur which will interrupt the editing. This flag is used to avoid interruptions\n     * while the editing action is being initialized.\n     */\n    _editStartPending = false;\n    editable = false;\n    /** Emitted when the chip is edited. */\n    edited = new EventEmitter();\n    /** The default chip edit input that is used if none is projected into this chip row. */\n    defaultEditInput;\n    /** The projected chip edit input. */\n    contentEditInput;\n    _isEditing = false;\n    constructor() {\n        super();\n        this.role = 'row';\n        this._onBlur.pipe(takeUntil(this.destroyed)).subscribe(() => {\n            if (this._isEditing && !this._editStartPending) {\n                this._onEditFinish();\n            }\n        });\n    }\n    _hasTrailingIcon() {\n        // The trailing icon is hidden while editing.\n        return !this._isEditing && super._hasTrailingIcon();\n    }\n    /** Sends focus to the first gridcell when the user clicks anywhere inside the chip. */\n    _handleFocus() {\n        if (!this._isEditing && !this.disabled) {\n            this.focus();\n        }\n    }\n    _handleKeydown(event) {\n        if (event.keyCode === ENTER && !this.disabled) {\n            if (this._isEditing) {\n                event.preventDefault();\n                this._onEditFinish();\n            }\n            else if (this.editable) {\n                this._startEditing(event);\n            }\n        }\n        else if (this._isEditing) {\n            // Stop the event from reaching the chip set in order to avoid navigating.\n            event.stopPropagation();\n        }\n        else {\n            super._handleKeydown(event);\n        }\n    }\n    _handleDoubleclick(event) {\n        if (!this.disabled && this.editable) {\n            this._startEditing(event);\n        }\n    }\n    _startEditing(event) {\n        if (!this.primaryAction ||\n            (this.removeIcon && this._getSourceAction(event.target) === this.removeIcon)) {\n            return;\n        }\n        // The value depends on the DOM so we need to extract it before we flip the flag.\n        const value = this.value;\n        this._isEditing = this._editStartPending = true;\n        // Defer initializing the input until after it has been added to the DOM.\n        afterNextRender(() => {\n            this._getEditInput().initialize(value);\n            this._editStartPending = false;\n        }, { injector: this._injector });\n    }\n    _onEditFinish() {\n        this._isEditing = this._editStartPending = false;\n        this.edited.emit({ chip: this, value: this._getEditInput().getValue() });\n        // If the edit input is still focused or focus was returned to the body after it was destroyed,\n        // return focus to the chip contents.\n        if (this._document.activeElement === this._getEditInput().getNativeElement() ||\n            this._document.activeElement === this._document.body) {\n            this.primaryAction.focus();\n        }\n    }\n    _isRippleDisabled() {\n        return super._isRippleDisabled() || this._isEditing;\n    }\n    /**\n     * Gets the projected chip edit input, or the default input if none is projected in. One of these\n     * two values is guaranteed to be defined.\n     */\n    _getEditInput() {\n        return this.contentEditInput || this.defaultEditInput;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatChipRow, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"19.2.6\", type: MatChipRow, isStandalone: true, selector: \"mat-chip-row, [mat-chip-row], mat-basic-chip-row, [mat-basic-chip-row]\", inputs: { editable: \"editable\" }, outputs: { edited: \"edited\" }, host: { listeners: { \"focus\": \"_handleFocus()\", \"dblclick\": \"_handleDoubleclick($event)\" }, properties: { \"class.mat-mdc-chip-with-avatar\": \"leadingIcon\", \"class.mat-mdc-chip-disabled\": \"disabled\", \"class.mat-mdc-chip-editing\": \"_isEditing\", \"class.mat-mdc-chip-editable\": \"editable\", \"class.mdc-evolution-chip--disabled\": \"disabled\", \"class.mdc-evolution-chip--with-trailing-action\": \"_hasTrailingIcon()\", \"class.mdc-evolution-chip--with-primary-graphic\": \"leadingIcon\", \"class.mdc-evolution-chip--with-primary-icon\": \"leadingIcon\", \"class.mdc-evolution-chip--with-avatar\": \"leadingIcon\", \"class.mat-mdc-chip-highlighted\": \"highlighted\", \"class.mat-mdc-chip-with-trailing-icon\": \"_hasTrailingIcon()\", \"id\": \"id\", \"attr.tabindex\": \"disabled ? null : -1\", \"attr.aria-label\": \"null\", \"attr.aria-description\": \"null\", \"attr.role\": \"role\" }, classAttribute: \"mat-mdc-chip mat-mdc-chip-row mdc-evolution-chip\" }, providers: [\n            { provide: MatChip, useExisting: MatChipRow },\n            { provide: MAT_CHIP, useExisting: MatChipRow },\n        ], queries: [{ propertyName: \"contentEditInput\", first: true, predicate: MatChipEditInput, descendants: true }], viewQueries: [{ propertyName: \"defaultEditInput\", first: true, predicate: MatChipEditInput, descendants: true }], usesInheritance: true, ngImport: i0, template: \"@if (!_isEditing) {\\n  <span class=\\\"mat-mdc-chip-focus-overlay\\\"></span>\\n}\\n\\n<span class=\\\"mdc-evolution-chip__cell mdc-evolution-chip__cell--primary\\\" role=\\\"gridcell\\\"\\n    matChipAction\\n    [disabled]=\\\"disabled\\\"\\n    [attr.aria-label]=\\\"ariaLabel\\\"\\n    [attr.aria-describedby]=\\\"_ariaDescriptionId\\\">\\n  @if (leadingIcon) {\\n    <span class=\\\"mdc-evolution-chip__graphic mat-mdc-chip-graphic\\\">\\n      <ng-content select=\\\"mat-chip-avatar, [matChipAvatar]\\\"></ng-content>\\n    </span>\\n  }\\n\\n  <span class=\\\"mdc-evolution-chip__text-label mat-mdc-chip-action-label\\\">\\n    @if (_isEditing) {\\n      @if (contentEditInput) {\\n        <ng-content select=\\\"[matChipEditInput]\\\"></ng-content>\\n      } @else {\\n        <span matChipEditInput></span>\\n      }\\n    } @else {\\n      <ng-content></ng-content>\\n    }\\n\\n    <span class=\\\"mat-mdc-chip-primary-focus-indicator mat-focus-indicator\\\" aria-hidden=\\\"true\\\"></span>\\n  </span>\\n</span>\\n\\n@if (_hasTrailingIcon()) {\\n  <span\\n    class=\\\"mdc-evolution-chip__cell mdc-evolution-chip__cell--trailing\\\"\\n    role=\\\"gridcell\\\">\\n    <ng-content select=\\\"mat-chip-trailing-icon,[matChipRemove],[matChipTrailingIcon]\\\"></ng-content>\\n  </span>\\n}\\n\\n<span class=\\\"cdk-visually-hidden\\\" [id]=\\\"_ariaDescriptionId\\\">{{ariaDescription}}</span>\\n\", styles: [\".mdc-evolution-chip,.mdc-evolution-chip__cell,.mdc-evolution-chip__action{display:inline-flex;align-items:center}.mdc-evolution-chip{position:relative;max-width:100%}.mdc-evolution-chip__cell,.mdc-evolution-chip__action{height:100%}.mdc-evolution-chip__cell--primary{flex-basis:100%;overflow-x:hidden}.mdc-evolution-chip__cell--trailing{flex:1 0 auto}.mdc-evolution-chip__action{align-items:center;background:none;border:none;box-sizing:content-box;cursor:pointer;display:inline-flex;justify-content:center;outline:none;padding:0;text-decoration:none;color:inherit}.mdc-evolution-chip__action--presentational{cursor:auto}.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{pointer-events:none}@media(forced-colors: active){.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{forced-color-adjust:none}}.mdc-evolution-chip__action--primary{font:inherit;letter-spacing:inherit;white-space:inherit;overflow-x:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary::before{border-width:var(--mdc-chip-outline-width, 1px);border-radius:var(--mdc-chip-container-shape-radius, 8px);box-sizing:border-box;content:\\\"\\\";height:100%;left:0;position:absolute;pointer-events:none;top:0;width:100%;z-index:1;border-style:solid}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--primary::before{border-color:var(--mdc-chip-outline-color, var(--mat-sys-outline))}.mdc-evolution-chip__action--primary:not(.mdc-evolution-chip__action--presentational):not(.mdc-ripple-upgraded):focus::before{border-color:var(--mdc-chip-focus-outline-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--primary::before{border-color:var(--mdc-chip-disabled-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__action--primary::before{border-width:var(--mdc-chip-flat-selected-outline-width, 0)}.mat-mdc-basic-chip .mdc-evolution-chip__action--primary{font:inherit}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip__action--trailing{position:relative;overflow:visible}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--trailing{color:var(--mdc-chip-with-trailing-icon-trailing-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--trailing{color:var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip__text-label{-webkit-user-select:none;user-select:none;white-space:nowrap;text-overflow:ellipsis;overflow:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__text-label{font-family:var(--mdc-chip-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mdc-chip-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mdc-chip-label-text-size, var(--mat-sys-label-large-size));font-weight:var(--mdc-chip-label-text-weight, var(--mat-sys-label-large-weight));letter-spacing:var(--mdc-chip-label-text-tracking, var(--mat-sys-label-large-tracking))}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mdc-chip-label-text-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mdc-chip-selected-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label,.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label{color:var(--mdc-chip-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-evolution-chip__graphic{align-items:center;display:inline-flex;justify-content:center;overflow:hidden;pointer-events:none;position:relative;flex:1 0 auto}.mat-mdc-standard-chip .mdc-evolution-chip__graphic{width:var(--mdc-chip-with-avatar-avatar-size, 24px);height:var(--mdc-chip-with-avatar-avatar-size, 24px);font-size:var(--mdc-chip-with-avatar-avatar-size, 24px)}.mdc-evolution-chip--selecting .mdc-evolution-chip__graphic{transition:width 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selectable:not(.mdc-evolution-chip--selected):not(.mdc-evolution-chip--with-primary-icon) .mdc-evolution-chip__graphic{width:0}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mdc-evolution-chip__checkmark{position:absolute;opacity:0;top:50%;left:50%;height:20px;width:20px}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__checkmark{color:var(--mdc-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__checkmark{color:var(--mdc-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark{transition:transform 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1);transform:translate(-75%, -50%)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{transform:translate(-50%, -50%);opacity:1}.mdc-evolution-chip__checkmark-svg{display:block}.mdc-evolution-chip__checkmark-path{stroke-width:2px;stroke-dasharray:29.7833385;stroke-dashoffset:29.7833385;stroke:currentColor}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark-path{transition:stroke-dashoffset 150ms 45ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark-path{stroke-dashoffset:0}@media(forced-colors: active){.mdc-evolution-chip__checkmark-path{stroke:CanvasText !important}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--trailing{height:18px;width:18px;font-size:18px}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove{opacity:calc(var(--mat-chip-trailing-action-opacity, 1)*var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove:focus{opacity:calc(var(--mat-chip-trailing-action-focus-opacity, 1)*var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mat-mdc-standard-chip{border-radius:var(--mdc-chip-container-shape-radius, 8px);height:var(--mdc-chip-container-height, 32px)}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled){background-color:var(--mdc-chip-elevated-container-color, transparent)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{background-color:var(--mdc-chip-elevated-disabled-container-color)}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled){background-color:var(--mdc-chip-elevated-selected-container-color, var(--mat-sys-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled{background-color:var(--mdc-chip-flat-disabled-selected-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}@media(forced-colors: active){.mat-mdc-standard-chip{outline:solid 1px}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--primary{border-radius:var(--mdc-chip-with-avatar-avatar-shape-radius, 24px);width:var(--mdc-chip-with-icon-icon-size, 18px);height:var(--mdc-chip-with-icon-icon-size, 18px);font-size:var(--mdc-chip-with-icon-icon-size, 18px)}.mdc-evolution-chip--selected .mdc-evolution-chip__icon--primary{opacity:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__icon--primary{color:var(--mdc-chip-with-icon-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--primary{color:var(--mdc-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-highlighted{--mdc-chip-with-icon-icon-color:var(--mdc-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container));--mdc-chip-elevated-container-color:var(--mdc-chip-elevated-selected-container-color, var(--mat-sys-secondary-container));--mdc-chip-label-text-color:var(--mdc-chip-selected-label-text-color, var(--mat-sys-on-secondary-container));--mdc-chip-outline-width:var(--mdc-chip-flat-selected-outline-width, 0)}.mat-mdc-chip-focus-overlay{background:var(--mdc-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-selected .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-chip:hover .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-hover-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mdc-chip-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-focus-overlay .mat-mdc-chip-selected:hover,.mat-mdc-chip-highlighted:hover .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-hover-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mdc-chip-selected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mdc-chip-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected.cdk-focused .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mdc-chip-selected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-evolution-chip--disabled:not(.mdc-evolution-chip--selected) .mat-mdc-chip-avatar{opacity:var(--mdc-chip-with-avatar-disabled-avatar-opacity, 0.38)}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{opacity:var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38)}.mdc-evolution-chip--disabled.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{opacity:var(--mdc-chip-with-icon-disabled-icon-opacity, 0.38)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{opacity:var(--mat-chip-disabled-container-opacity, 1)}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-trailing-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-remove{opacity:var(--mat-chip-trailing-action-opacity, 1)}.mat-mdc-chip-remove:focus{opacity:var(--mat-chip-trailing-action-focus-opacity, 1)}.mat-mdc-chip-remove::after{background-color:var(--mat-chip-trailing-action-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-remove:hover::after{opacity:var(--mat-chip-trailing-action-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-remove:focus::after{opacity:var(--mat-chip-trailing-action-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected .mat-mdc-chip-remove::after,.mat-mdc-chip-highlighted .mat-mdc-chip-remove::after{background-color:var(--mat-chip-selected-trailing-action-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-standard-chip .mdc-evolution-chip__cell--primary,.mat-mdc-standard-chip .mdc-evolution-chip__action--primary,.mat-mdc-standard-chip .mat-mdc-chip-action-label{overflow:visible}.mat-mdc-standard-chip .mat-mdc-chip-graphic,.mat-mdc-standard-chip .mat-mdc-chip-trailing-icon{box-sizing:content-box}.mat-mdc-standard-chip._mat-animation-noopable,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__graphic,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark-path{transition-duration:1ms;animation-duration:1ms}.mat-mdc-chip-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;opacity:0;border-radius:inherit;transition:opacity 150ms linear}._mat-animation-noopable .mat-mdc-chip-focus-overlay{transition:none}.mat-mdc-basic-chip .mat-mdc-chip-focus-overlay{display:none}.mat-mdc-chip .mat-ripple.mat-mdc-chip-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-chip-avatar{text-align:center;line-height:1;color:var(--mdc-chip-with-icon-icon-color, currentColor)}.mat-mdc-chip{position:relative;z-index:0}.mat-mdc-chip-action-label{text-align:left;z-index:1}[dir=rtl] .mat-mdc-chip-action-label{text-align:right}.mat-mdc-chip.mdc-evolution-chip--with-trailing-action .mat-mdc-chip-action-label{position:relative}.mat-mdc-chip-action-label .mat-mdc-chip-primary-focus-indicator{position:absolute;top:0;right:0;bottom:0;left:0;pointer-events:none}.mat-mdc-chip-action-label .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-mdc-chip-remove::before{margin:calc(var(--mat-focus-indicator-border-width, 3px)*-1);left:8px;right:8px}.mat-mdc-chip-remove::after{content:\\\"\\\";display:block;opacity:0;position:absolute;top:-3px;bottom:-3px;left:5px;right:5px;border-radius:50%;box-sizing:border-box;padding:12px;margin:-12px;background-clip:content-box}.mat-mdc-chip-remove .mat-icon{width:18px;height:18px;font-size:18px;box-sizing:content-box}.mat-chip-edit-input{cursor:text;display:inline-block;color:inherit;outline:0}@media(forced-colors: active){.mat-mdc-chip-selected:not(.mat-mdc-chip-multiple){outline-width:3px}}.mat-mdc-chip-action:focus .mat-focus-indicator::before{content:\\\"\\\"}.mdc-evolution-chip__icon,.mat-mdc-chip-remove .mat-icon{min-height:fit-content}\\n\"], dependencies: [{ kind: \"directive\", type: MatChipAction, selector: \"[matChipAction]\", inputs: [\"isInteractive\", \"disabled\", \"tabIndex\", \"_allowFocusWhenDisabled\"] }, { kind: \"directive\", type: MatChipEditInput, selector: \"span[matChipEditInput]\" }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatChipRow, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-chip-row, [mat-chip-row], mat-basic-chip-row, [mat-basic-chip-row]', host: {\n                        'class': 'mat-mdc-chip mat-mdc-chip-row mdc-evolution-chip',\n                        '[class.mat-mdc-chip-with-avatar]': 'leadingIcon',\n                        '[class.mat-mdc-chip-disabled]': 'disabled',\n                        '[class.mat-mdc-chip-editing]': '_isEditing',\n                        '[class.mat-mdc-chip-editable]': 'editable',\n                        '[class.mdc-evolution-chip--disabled]': 'disabled',\n                        '[class.mdc-evolution-chip--with-trailing-action]': '_hasTrailingIcon()',\n                        '[class.mdc-evolution-chip--with-primary-graphic]': 'leadingIcon',\n                        '[class.mdc-evolution-chip--with-primary-icon]': 'leadingIcon',\n                        '[class.mdc-evolution-chip--with-avatar]': 'leadingIcon',\n                        '[class.mat-mdc-chip-highlighted]': 'highlighted',\n                        '[class.mat-mdc-chip-with-trailing-icon]': '_hasTrailingIcon()',\n                        '[id]': 'id',\n                        // Has to have a negative tabindex in order to capture\n                        // focus and redirect it to the primary action.\n                        '[attr.tabindex]': 'disabled ? null : -1',\n                        '[attr.aria-label]': 'null',\n                        '[attr.aria-description]': 'null',\n                        '[attr.role]': 'role',\n                        '(focus)': '_handleFocus()',\n                        '(dblclick)': '_handleDoubleclick($event)',\n                    }, providers: [\n                        { provide: MatChip, useExisting: MatChipRow },\n                        { provide: MAT_CHIP, useExisting: MatChipRow },\n                    ], encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, imports: [MatChipAction, MatChipEditInput], template: \"@if (!_isEditing) {\\n  <span class=\\\"mat-mdc-chip-focus-overlay\\\"></span>\\n}\\n\\n<span class=\\\"mdc-evolution-chip__cell mdc-evolution-chip__cell--primary\\\" role=\\\"gridcell\\\"\\n    matChipAction\\n    [disabled]=\\\"disabled\\\"\\n    [attr.aria-label]=\\\"ariaLabel\\\"\\n    [attr.aria-describedby]=\\\"_ariaDescriptionId\\\">\\n  @if (leadingIcon) {\\n    <span class=\\\"mdc-evolution-chip__graphic mat-mdc-chip-graphic\\\">\\n      <ng-content select=\\\"mat-chip-avatar, [matChipAvatar]\\\"></ng-content>\\n    </span>\\n  }\\n\\n  <span class=\\\"mdc-evolution-chip__text-label mat-mdc-chip-action-label\\\">\\n    @if (_isEditing) {\\n      @if (contentEditInput) {\\n        <ng-content select=\\\"[matChipEditInput]\\\"></ng-content>\\n      } @else {\\n        <span matChipEditInput></span>\\n      }\\n    } @else {\\n      <ng-content></ng-content>\\n    }\\n\\n    <span class=\\\"mat-mdc-chip-primary-focus-indicator mat-focus-indicator\\\" aria-hidden=\\\"true\\\"></span>\\n  </span>\\n</span>\\n\\n@if (_hasTrailingIcon()) {\\n  <span\\n    class=\\\"mdc-evolution-chip__cell mdc-evolution-chip__cell--trailing\\\"\\n    role=\\\"gridcell\\\">\\n    <ng-content select=\\\"mat-chip-trailing-icon,[matChipRemove],[matChipTrailingIcon]\\\"></ng-content>\\n  </span>\\n}\\n\\n<span class=\\\"cdk-visually-hidden\\\" [id]=\\\"_ariaDescriptionId\\\">{{ariaDescription}}</span>\\n\", styles: [\".mdc-evolution-chip,.mdc-evolution-chip__cell,.mdc-evolution-chip__action{display:inline-flex;align-items:center}.mdc-evolution-chip{position:relative;max-width:100%}.mdc-evolution-chip__cell,.mdc-evolution-chip__action{height:100%}.mdc-evolution-chip__cell--primary{flex-basis:100%;overflow-x:hidden}.mdc-evolution-chip__cell--trailing{flex:1 0 auto}.mdc-evolution-chip__action{align-items:center;background:none;border:none;box-sizing:content-box;cursor:pointer;display:inline-flex;justify-content:center;outline:none;padding:0;text-decoration:none;color:inherit}.mdc-evolution-chip__action--presentational{cursor:auto}.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{pointer-events:none}@media(forced-colors: active){.mdc-evolution-chip--disabled,.mdc-evolution-chip__action:disabled{forced-color-adjust:none}}.mdc-evolution-chip__action--primary{font:inherit;letter-spacing:inherit;white-space:inherit;overflow-x:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary::before{border-width:var(--mdc-chip-outline-width, 1px);border-radius:var(--mdc-chip-container-shape-radius, 8px);box-sizing:border-box;content:\\\"\\\";height:100%;left:0;position:absolute;pointer-events:none;top:0;width:100%;z-index:1;border-style:solid}.mat-mdc-standard-chip .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--primary::before{border-color:var(--mdc-chip-outline-color, var(--mat-sys-outline))}.mdc-evolution-chip__action--primary:not(.mdc-evolution-chip__action--presentational):not(.mdc-ripple-upgraded):focus::before{border-color:var(--mdc-chip-focus-outline-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--primary::before{border-color:var(--mdc-chip-disabled-outline-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__action--primary::before{border-width:var(--mdc-chip-flat-selected-outline-width, 0)}.mat-mdc-basic-chip .mdc-evolution-chip__action--primary{font:inherit}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:0;padding-right:12px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__action--primary{padding-left:12px;padding-right:0}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--primary{padding-left:0;padding-right:0}.mdc-evolution-chip__action--trailing{position:relative;overflow:visible}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__action--trailing{color:var(--mdc-chip-with-trailing-icon-trailing-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__action--trailing{color:var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-standard-chip.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__action--trailing{padding-left:8px;padding-right:8px}.mdc-evolution-chip__text-label{-webkit-user-select:none;user-select:none;white-space:nowrap;text-overflow:ellipsis;overflow:hidden}.mat-mdc-standard-chip .mdc-evolution-chip__text-label{font-family:var(--mdc-chip-label-text-font, var(--mat-sys-label-large-font));line-height:var(--mdc-chip-label-text-line-height, var(--mat-sys-label-large-line-height));font-size:var(--mdc-chip-label-text-size, var(--mat-sys-label-large-size));font-weight:var(--mdc-chip-label-text-weight, var(--mat-sys-label-large-weight));letter-spacing:var(--mdc-chip-label-text-tracking, var(--mat-sys-label-large-tracking))}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mdc-chip-label-text-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__text-label{color:var(--mdc-chip-selected-label-text-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label,.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__text-label{color:var(--mdc-chip-disabled-label-text-color, color-mix(in srgb, var(--mat-sys-on-surface) 38%, transparent))}.mdc-evolution-chip__graphic{align-items:center;display:inline-flex;justify-content:center;overflow:hidden;pointer-events:none;position:relative;flex:1 0 auto}.mat-mdc-standard-chip .mdc-evolution-chip__graphic{width:var(--mdc-chip-with-avatar-avatar-size, 24px);height:var(--mdc-chip-with-avatar-avatar-size, 24px);font-size:var(--mdc-chip-with-avatar-avatar-size, 24px)}.mdc-evolution-chip--selecting .mdc-evolution-chip__graphic{transition:width 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selectable:not(.mdc-evolution-chip--selected):not(.mdc-evolution-chip--with-primary-icon) .mdc-evolution-chip__graphic{width:0}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mat-mdc-standard-chip.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:6px;padding-right:6px}.mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:4px;padding-right:8px}[dir=rtl] .mdc-evolution-chip--with-avatar.mdc-evolution-chip--with-primary-graphic.mdc-evolution-chip--with-trailing-action .mdc-evolution-chip__graphic{padding-left:8px;padding-right:4px}.mdc-evolution-chip__checkmark{position:absolute;opacity:0;top:50%;left:50%;height:20px;width:20px}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__checkmark{color:var(--mdc-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__checkmark{color:var(--mdc-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark{transition:transform 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1);transform:translate(-75%, -50%)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{transform:translate(-50%, -50%);opacity:1}.mdc-evolution-chip__checkmark-svg{display:block}.mdc-evolution-chip__checkmark-path{stroke-width:2px;stroke-dasharray:29.7833385;stroke-dashoffset:29.7833385;stroke:currentColor}.mdc-evolution-chip--selecting .mdc-evolution-chip__checkmark-path{transition:stroke-dashoffset 150ms 45ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark-path{stroke-dashoffset:0}@media(forced-colors: active){.mdc-evolution-chip__checkmark-path{stroke:CanvasText !important}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--trailing{height:18px;width:18px;font-size:18px}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove{opacity:calc(var(--mat-chip-trailing-action-opacity, 1)*var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing.mat-mdc-chip-remove:focus{opacity:calc(var(--mat-chip-trailing-action-focus-opacity, 1)*var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38))}.mat-mdc-standard-chip{border-radius:var(--mdc-chip-container-shape-radius, 8px);height:var(--mdc-chip-container-height, 32px)}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled){background-color:var(--mdc-chip-elevated-container-color, transparent)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{background-color:var(--mdc-chip-elevated-disabled-container-color)}.mat-mdc-standard-chip.mdc-evolution-chip--selected:not(.mdc-evolution-chip--disabled){background-color:var(--mdc-chip-elevated-selected-container-color, var(--mat-sys-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled{background-color:var(--mdc-chip-flat-disabled-selected-container-color, color-mix(in srgb, var(--mat-sys-on-surface) 12%, transparent))}@media(forced-colors: active){.mat-mdc-standard-chip{outline:solid 1px}}.mat-mdc-standard-chip .mdc-evolution-chip__icon--primary{border-radius:var(--mdc-chip-with-avatar-avatar-shape-radius, 24px);width:var(--mdc-chip-with-icon-icon-size, 18px);height:var(--mdc-chip-with-icon-icon-size, 18px);font-size:var(--mdc-chip-with-icon-icon-size, 18px)}.mdc-evolution-chip--selected .mdc-evolution-chip__icon--primary{opacity:0}.mat-mdc-standard-chip:not(.mdc-evolution-chip--disabled) .mdc-evolution-chip__icon--primary{color:var(--mdc-chip-with-icon-icon-color, var(--mat-sys-on-surface-variant))}.mat-mdc-standard-chip.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--primary{color:var(--mdc-chip-with-icon-disabled-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-highlighted{--mdc-chip-with-icon-icon-color:var(--mdc-chip-with-icon-selected-icon-color, var(--mat-sys-on-secondary-container));--mdc-chip-elevated-container-color:var(--mdc-chip-elevated-selected-container-color, var(--mat-sys-secondary-container));--mdc-chip-label-text-color:var(--mdc-chip-selected-label-text-color, var(--mat-sys-on-secondary-container));--mdc-chip-outline-width:var(--mdc-chip-flat-selected-outline-width, 0)}.mat-mdc-chip-focus-overlay{background:var(--mdc-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-selected .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-chip:hover .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-hover-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mdc-chip-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-focus-overlay .mat-mdc-chip-selected:hover,.mat-mdc-chip-highlighted:hover .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-hover-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mdc-chip-selected-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-focus-state-layer-color, var(--mat-sys-on-surface-variant));opacity:var(--mdc-chip-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected.cdk-focused .mat-mdc-chip-focus-overlay,.mat-mdc-chip-highlighted.cdk-focused .mat-mdc-chip-focus-overlay{background:var(--mdc-chip-selected-focus-state-layer-color, var(--mat-sys-on-secondary-container));opacity:var(--mdc-chip-selected-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mdc-evolution-chip--disabled:not(.mdc-evolution-chip--selected) .mat-mdc-chip-avatar{opacity:var(--mdc-chip-with-avatar-disabled-avatar-opacity, 0.38)}.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{opacity:var(--mdc-chip-with-trailing-icon-disabled-trailing-icon-opacity, 0.38)}.mdc-evolution-chip--disabled.mdc-evolution-chip--selected .mdc-evolution-chip__checkmark{opacity:var(--mdc-chip-with-icon-disabled-icon-opacity, 0.38)}.mat-mdc-standard-chip.mdc-evolution-chip--disabled{opacity:var(--mat-chip-disabled-container-opacity, 1)}.mat-mdc-standard-chip.mdc-evolution-chip--selected .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-trailing-icon-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip.mdc-evolution-chip--selected.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing,.mat-mdc-standard-chip.mat-mdc-chip-highlighted.mdc-evolution-chip--disabled .mdc-evolution-chip__icon--trailing{color:var(--mat-chip-selected-disabled-trailing-icon-color, var(--mat-sys-on-surface))}.mat-mdc-chip-remove{opacity:var(--mat-chip-trailing-action-opacity, 1)}.mat-mdc-chip-remove:focus{opacity:var(--mat-chip-trailing-action-focus-opacity, 1)}.mat-mdc-chip-remove::after{background-color:var(--mat-chip-trailing-action-state-layer-color, var(--mat-sys-on-surface-variant))}.mat-mdc-chip-remove:hover::after{opacity:var(--mat-chip-trailing-action-hover-state-layer-opacity, var(--mat-sys-hover-state-layer-opacity))}.mat-mdc-chip-remove:focus::after{opacity:var(--mat-chip-trailing-action-focus-state-layer-opacity, var(--mat-sys-focus-state-layer-opacity))}.mat-mdc-chip-selected .mat-mdc-chip-remove::after,.mat-mdc-chip-highlighted .mat-mdc-chip-remove::after{background-color:var(--mat-chip-selected-trailing-action-state-layer-color, var(--mat-sys-on-secondary-container))}.mat-mdc-standard-chip{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-standard-chip .mdc-evolution-chip__cell--primary,.mat-mdc-standard-chip .mdc-evolution-chip__action--primary,.mat-mdc-standard-chip .mat-mdc-chip-action-label{overflow:visible}.mat-mdc-standard-chip .mat-mdc-chip-graphic,.mat-mdc-standard-chip .mat-mdc-chip-trailing-icon{box-sizing:content-box}.mat-mdc-standard-chip._mat-animation-noopable,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__graphic,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark,.mat-mdc-standard-chip._mat-animation-noopable .mdc-evolution-chip__checkmark-path{transition-duration:1ms;animation-duration:1ms}.mat-mdc-chip-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;opacity:0;border-radius:inherit;transition:opacity 150ms linear}._mat-animation-noopable .mat-mdc-chip-focus-overlay{transition:none}.mat-mdc-basic-chip .mat-mdc-chip-focus-overlay{display:none}.mat-mdc-chip .mat-ripple.mat-mdc-chip-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-chip-avatar{text-align:center;line-height:1;color:var(--mdc-chip-with-icon-icon-color, currentColor)}.mat-mdc-chip{position:relative;z-index:0}.mat-mdc-chip-action-label{text-align:left;z-index:1}[dir=rtl] .mat-mdc-chip-action-label{text-align:right}.mat-mdc-chip.mdc-evolution-chip--with-trailing-action .mat-mdc-chip-action-label{position:relative}.mat-mdc-chip-action-label .mat-mdc-chip-primary-focus-indicator{position:absolute;top:0;right:0;bottom:0;left:0;pointer-events:none}.mat-mdc-chip-action-label .mat-focus-indicator::before{margin:calc(calc(var(--mat-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-mdc-chip-remove::before{margin:calc(var(--mat-focus-indicator-border-width, 3px)*-1);left:8px;right:8px}.mat-mdc-chip-remove::after{content:\\\"\\\";display:block;opacity:0;position:absolute;top:-3px;bottom:-3px;left:5px;right:5px;border-radius:50%;box-sizing:border-box;padding:12px;margin:-12px;background-clip:content-box}.mat-mdc-chip-remove .mat-icon{width:18px;height:18px;font-size:18px;box-sizing:content-box}.mat-chip-edit-input{cursor:text;display:inline-block;color:inherit;outline:0}@media(forced-colors: active){.mat-mdc-chip-selected:not(.mat-mdc-chip-multiple){outline-width:3px}}.mat-mdc-chip-action:focus .mat-focus-indicator::before{content:\\\"\\\"}.mdc-evolution-chip__icon,.mat-mdc-chip-remove .mat-icon{min-height:fit-content}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { editable: [{\n                type: Input\n            }], edited: [{\n                type: Output\n            }], defaultEditInput: [{\n                type: ViewChild,\n                args: [MatChipEditInput]\n            }], contentEditInput: [{\n                type: ContentChild,\n                args: [MatChipEditInput]\n            }] } });\n\n/**\n * Basic container component for the MatChip component.\n *\n * Extended by MatChipListbox and MatChipGrid for different interaction patterns.\n */\nclass MatChipSet {\n    _elementRef = inject(ElementRef);\n    _changeDetectorRef = inject(ChangeDetectorRef);\n    _dir = inject(Directionality, { optional: true });\n    /** Index of the last destroyed chip that had focus. */\n    _lastDestroyedFocusedChipIndex = null;\n    /** Used to manage focus within the chip list. */\n    _keyManager;\n    /** Subject that emits when the component has been destroyed. */\n    _destroyed = new Subject();\n    /** Role to use if it hasn't been overwritten by the user. */\n    _defaultRole = 'presentation';\n    /** Combined stream of all of the child chips' focus events. */\n    get chipFocusChanges() {\n        return this._getChipStream(chip => chip._onFocus);\n    }\n    /** Combined stream of all of the child chips' destroy events. */\n    get chipDestroyedChanges() {\n        return this._getChipStream(chip => chip.destroyed);\n    }\n    /** Combined stream of all of the child chips' remove events. */\n    get chipRemovedChanges() {\n        return this._getChipStream(chip => chip.removed);\n    }\n    /** Whether the chip set is disabled. */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(value) {\n        this._disabled = value;\n        this._syncChipsState();\n    }\n    _disabled = false;\n    /** Whether the chip list contains chips or not. */\n    get empty() {\n        return !this._chips || this._chips.length === 0;\n    }\n    /** The ARIA role applied to the chip set. */\n    get role() {\n        if (this._explicitRole) {\n            return this._explicitRole;\n        }\n        return this.empty ? null : this._defaultRole;\n    }\n    /** Tabindex of the chip set. */\n    tabIndex = 0;\n    set role(value) {\n        this._explicitRole = value;\n    }\n    _explicitRole = null;\n    /** Whether any of the chips inside of this chip-set has focus. */\n    get focused() {\n        return this._hasFocusedChip();\n    }\n    /** The chips that are part of this chip set. */\n    _chips;\n    /** Flat list of all the actions contained within the chips. */\n    _chipActions = new QueryList();\n    constructor() { }\n    ngAfterViewInit() {\n        this._setUpFocusManagement();\n        this._trackChipSetChanges();\n        this._trackDestroyedFocusedChip();\n    }\n    ngOnDestroy() {\n        this._keyManager?.destroy();\n        this._chipActions.destroy();\n        this._destroyed.next();\n        this._destroyed.complete();\n    }\n    /** Checks whether any of the chips is focused. */\n    _hasFocusedChip() {\n        return this._chips && this._chips.some(chip => chip._hasFocus());\n    }\n    /** Syncs the chip-set's state with the individual chips. */\n    _syncChipsState() {\n        this._chips?.forEach(chip => {\n            chip._chipListDisabled = this._disabled;\n            chip._changeDetectorRef.markForCheck();\n        });\n    }\n    /** Dummy method for subclasses to override. Base chip set cannot be focused. */\n    focus() { }\n    /** Handles keyboard events on the chip set. */\n    _handleKeydown(event) {\n        if (this._originatesFromChip(event)) {\n            this._keyManager.onKeydown(event);\n        }\n    }\n    /**\n     * Utility to ensure all indexes are valid.\n     *\n     * @param index The index to be checked.\n     * @returns True if the index is valid for our list of chips.\n     */\n    _isValidIndex(index) {\n        return index >= 0 && index < this._chips.length;\n    }\n    /**\n     * Removes the `tabindex` from the chip set and resets it back afterwards, allowing the\n     * user to tab out of it. This prevents the set from capturing focus and redirecting\n     * it back to the first chip, creating a focus trap, if it user tries to tab away.\n     */\n    _allowFocusEscape() {\n        const previous = this._elementRef.nativeElement.tabIndex;\n        if (previous !== -1) {\n            // Set the tabindex directly on the element, instead of going through\n            // the data binding, because we aren't guaranteed that change detection\n            // will run quickly enough to allow focus to escape.\n            this._elementRef.nativeElement.tabIndex = -1;\n            // Note that this needs to be a `setTimeout`, because a `Promise.resolve`\n            // doesn't allow enough time for the focus to escape.\n            setTimeout(() => (this._elementRef.nativeElement.tabIndex = previous));\n        }\n    }\n    /**\n     * Gets a stream of events from all the chips within the set.\n     * The stream will automatically incorporate any newly-added chips.\n     */\n    _getChipStream(mappingFunction) {\n        return this._chips.changes.pipe(startWith(null), switchMap(() => merge(...this._chips.map(mappingFunction))));\n    }\n    /** Checks whether an event comes from inside a chip element. */\n    _originatesFromChip(event) {\n        let currentElement = event.target;\n        while (currentElement && currentElement !== this._elementRef.nativeElement) {\n            if (currentElement.classList.contains('mat-mdc-chip')) {\n                return true;\n            }\n            currentElement = currentElement.parentElement;\n        }\n        return false;\n    }\n    /** Sets up the chip set's focus management logic. */\n    _setUpFocusManagement() {\n        // Create a flat `QueryList` containing the actions of all of the chips.\n        // This allows us to navigate both within the chip and move to the next/previous\n        // one using the existing `ListKeyManager`.\n        this._chips.changes.pipe(startWith(this._chips)).subscribe((chips) => {\n            const actions = [];\n            chips.forEach(chip => chip._getActions().forEach(action => actions.push(action)));\n            this._chipActions.reset(actions);\n            this._chipActions.notifyOnChanges();\n        });\n        this._keyManager = new FocusKeyManager(this._chipActions)\n            .withVerticalOrientation()\n            .withHorizontalOrientation(this._dir ? this._dir.value : 'ltr')\n            .withHomeAndEnd()\n            .skipPredicate(action => this._skipPredicate(action));\n        // Keep the manager active index in sync so that navigation picks\n        // up from the current chip if the user clicks into the list directly.\n        this.chipFocusChanges.pipe(takeUntil(this._destroyed)).subscribe(({ chip }) => {\n            const action = chip._getSourceAction(document.activeElement);\n            if (action) {\n                this._keyManager.updateActiveItem(action);\n            }\n        });\n        this._dir?.change\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(direction => this._keyManager.withHorizontalOrientation(direction));\n    }\n    /**\n     * Determines if key manager should avoid putting a given chip action in the tab index. Skip\n     * non-interactive and disabled actions since the user can't do anything with them.\n     */\n    _skipPredicate(action) {\n        // Skip chips that the user cannot interact with. `mat-chip-set` does not permit focusing disabled\n        // chips.\n        return !action.isInteractive || action.disabled;\n    }\n    /** Listens to changes in the chip set and syncs up the state of the individual chips. */\n    _trackChipSetChanges() {\n        this._chips.changes.pipe(startWith(null), takeUntil(this._destroyed)).subscribe(() => {\n            if (this.disabled) {\n                // Since this happens after the content has been\n                // checked, we need to defer it to the next tick.\n                Promise.resolve().then(() => this._syncChipsState());\n            }\n            this._redirectDestroyedChipFocus();\n        });\n    }\n    /** Starts tracking the destroyed chips in order to capture the focused one. */\n    _trackDestroyedFocusedChip() {\n        this.chipDestroyedChanges.pipe(takeUntil(this._destroyed)).subscribe((event) => {\n            const chipArray = this._chips.toArray();\n            const chipIndex = chipArray.indexOf(event.chip);\n            // If the focused chip is destroyed, save its index so that we can move focus to the next\n            // chip. We only save the index here, rather than move the focus immediately, because we want\n            // to wait until the chip is removed from the chip list before focusing the next one. This\n            // allows us to keep focus on the same index if the chip gets swapped out.\n            if (this._isValidIndex(chipIndex) && event.chip._hasFocus()) {\n                this._lastDestroyedFocusedChipIndex = chipIndex;\n            }\n        });\n    }\n    /**\n     * Finds the next appropriate chip to move focus to,\n     * if the currently-focused chip is destroyed.\n     */\n    _redirectDestroyedChipFocus() {\n        if (this._lastDestroyedFocusedChipIndex == null) {\n            return;\n        }\n        if (this._chips.length) {\n            const newIndex = Math.min(this._lastDestroyedFocusedChipIndex, this._chips.length - 1);\n            const chipToFocus = this._chips.toArray()[newIndex];\n            if (chipToFocus.disabled) {\n                // If we're down to one disabled chip, move focus back to the set.\n                if (this._chips.length === 1) {\n                    this.focus();\n                }\n                else {\n                    this._keyManager.setPreviousItemActive();\n                }\n            }\n            else {\n                chipToFocus.focus();\n            }\n        }\n        else {\n            this.focus();\n        }\n        this._lastDestroyedFocusedChipIndex = null;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatChipSet, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"19.2.6\", type: MatChipSet, isStandalone: true, selector: \"mat-chip-set\", inputs: { disabled: [\"disabled\", \"disabled\", booleanAttribute], role: \"role\", tabIndex: [\"tabIndex\", \"tabIndex\", (value) => (value == null ? 0 : numberAttribute(value))] }, host: { listeners: { \"keydown\": \"_handleKeydown($event)\" }, properties: { \"attr.role\": \"role\" }, classAttribute: \"mat-mdc-chip-set mdc-evolution-chip-set\" }, queries: [{ propertyName: \"_chips\", predicate: MatChip, descendants: true }], ngImport: i0, template: `\n    <div class=\"mdc-evolution-chip-set__chips\" role=\"presentation\">\n      <ng-content></ng-content>\n    </div>\n  `, isInline: true, styles: [\".mat-mdc-chip-set{display:flex}.mat-mdc-chip-set:focus{outline:none}.mat-mdc-chip-set .mdc-evolution-chip-set__chips{min-width:100%;margin-left:-8px;margin-right:0}.mat-mdc-chip-set .mdc-evolution-chip{margin:4px 0 4px 8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip-set__chips{margin-left:0;margin-right:-8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip{margin-left:0;margin-right:8px}.mdc-evolution-chip-set__chips{display:flex;flex-flow:wrap;min-width:0}.mat-mdc-chip-set-stacked{flex-direction:column;align-items:flex-start}.mat-mdc-chip-set-stacked .mat-mdc-chip{width:100%}.mat-mdc-chip-set-stacked .mdc-evolution-chip__graphic{flex-grow:0}.mat-mdc-chip-set-stacked .mdc-evolution-chip__action--primary{flex-basis:100%;justify-content:start}input.mat-mdc-chip-input{flex:1 0 150px;margin-left:8px}[dir=rtl] input.mat-mdc-chip-input{margin-left:0;margin-right:8px}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatChipSet, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-chip-set', template: `\n    <div class=\"mdc-evolution-chip-set__chips\" role=\"presentation\">\n      <ng-content></ng-content>\n    </div>\n  `, host: {\n                        'class': 'mat-mdc-chip-set mdc-evolution-chip-set',\n                        '(keydown)': '_handleKeydown($event)',\n                        '[attr.role]': 'role',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, styles: [\".mat-mdc-chip-set{display:flex}.mat-mdc-chip-set:focus{outline:none}.mat-mdc-chip-set .mdc-evolution-chip-set__chips{min-width:100%;margin-left:-8px;margin-right:0}.mat-mdc-chip-set .mdc-evolution-chip{margin:4px 0 4px 8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip-set__chips{margin-left:0;margin-right:-8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip{margin-left:0;margin-right:8px}.mdc-evolution-chip-set__chips{display:flex;flex-flow:wrap;min-width:0}.mat-mdc-chip-set-stacked{flex-direction:column;align-items:flex-start}.mat-mdc-chip-set-stacked .mat-mdc-chip{width:100%}.mat-mdc-chip-set-stacked .mdc-evolution-chip__graphic{flex-grow:0}.mat-mdc-chip-set-stacked .mdc-evolution-chip__action--primary{flex-basis:100%;justify-content:start}input.mat-mdc-chip-input{flex:1 0 150px;margin-left:8px}[dir=rtl] input.mat-mdc-chip-input{margin-left:0;margin-right:8px}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], role: [{\n                type: Input\n            }], tabIndex: [{\n                type: Input,\n                args: [{\n                        transform: (value) => (value == null ? 0 : numberAttribute(value)),\n                    }]\n            }], _chips: [{\n                type: ContentChildren,\n                args: [MatChip, {\n                        // We need to use `descendants: true`, because Ivy will no longer match\n                        // indirect descendants if it's left as false.\n                        descendants: true,\n                    }]\n            }] } });\n\n/** Change event object that is emitted when the chip listbox value has changed. */\nclass MatChipListboxChange {\n    source;\n    value;\n    constructor(\n    /** Chip listbox that emitted the event. */\n    source, \n    /** Value of the chip listbox when the event was emitted. */\n    value) {\n        this.source = source;\n        this.value = value;\n    }\n}\n/**\n * Provider Expression that allows mat-chip-listbox to register as a ControlValueAccessor.\n * This allows it to support [(ngModel)].\n * @docs-private\n */\nconst MAT_CHIP_LISTBOX_CONTROL_VALUE_ACCESSOR = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => MatChipListbox),\n    multi: true,\n};\n/**\n * An extension of the MatChipSet component that supports chip selection.\n * Used with MatChipOption chips.\n */\nclass MatChipListbox extends MatChipSet {\n    /**\n     * Function when touched. Set as part of ControlValueAccessor implementation.\n     * @docs-private\n     */\n    _onTouched = () => { };\n    /**\n     * Function when changed. Set as part of ControlValueAccessor implementation.\n     * @docs-private\n     */\n    _onChange = () => { };\n    // TODO: MDC uses `grid` here\n    _defaultRole = 'listbox';\n    /** Default chip options. */\n    _defaultOptions = inject(MAT_CHIPS_DEFAULT_OPTIONS, { optional: true });\n    /** Whether the user should be allowed to select multiple chips. */\n    get multiple() {\n        return this._multiple;\n    }\n    set multiple(value) {\n        this._multiple = value;\n        this._syncListboxProperties();\n    }\n    _multiple = false;\n    /** The array of selected chips inside the chip listbox. */\n    get selected() {\n        const selectedChips = this._chips.toArray().filter(chip => chip.selected);\n        return this.multiple ? selectedChips : selectedChips[0];\n    }\n    /** Orientation of the chip list. */\n    ariaOrientation = 'horizontal';\n    /**\n     * Whether or not this chip listbox is selectable.\n     *\n     * When a chip listbox is not selectable, the selected states for all\n     * the chips inside the chip listbox are always ignored.\n     */\n    get selectable() {\n        return this._selectable;\n    }\n    set selectable(value) {\n        this._selectable = value;\n        this._syncListboxProperties();\n    }\n    _selectable = true;\n    /**\n     * A function to compare the option values with the selected values. The first argument\n     * is a value from an option. The second is a value from the selection. A boolean\n     * should be returned.\n     */\n    compareWith = (o1, o2) => o1 === o2;\n    /** Whether this chip listbox is required. */\n    required = false;\n    /** Whether checkmark indicator for single-selection options is hidden. */\n    get hideSingleSelectionIndicator() {\n        return this._hideSingleSelectionIndicator;\n    }\n    set hideSingleSelectionIndicator(value) {\n        this._hideSingleSelectionIndicator = value;\n        this._syncListboxProperties();\n    }\n    _hideSingleSelectionIndicator = this._defaultOptions?.hideSingleSelectionIndicator ?? false;\n    /** Combined stream of all of the child chips' selection change events. */\n    get chipSelectionChanges() {\n        return this._getChipStream(chip => chip.selectionChange);\n    }\n    /** Combined stream of all of the child chips' blur events. */\n    get chipBlurChanges() {\n        return this._getChipStream(chip => chip._onBlur);\n    }\n    /** The value of the listbox, which is the combined value of the selected chips. */\n    get value() {\n        return this._value;\n    }\n    set value(value) {\n        if (this._chips && this._chips.length) {\n            this._setSelectionByValue(value, false);\n        }\n        this._value = value;\n    }\n    _value;\n    /** Event emitted when the selected chip listbox value has been changed by the user. */\n    change = new EventEmitter();\n    _chips = undefined;\n    ngAfterContentInit() {\n        this._chips.changes.pipe(startWith(null), takeUntil(this._destroyed)).subscribe(() => {\n            if (this.value !== undefined) {\n                Promise.resolve().then(() => {\n                    this._setSelectionByValue(this.value, false);\n                });\n            }\n            // Update listbox selectable/multiple properties on chips\n            this._syncListboxProperties();\n        });\n        this.chipBlurChanges.pipe(takeUntil(this._destroyed)).subscribe(() => this._blur());\n        this.chipSelectionChanges.pipe(takeUntil(this._destroyed)).subscribe(event => {\n            if (!this.multiple) {\n                this._chips.forEach(chip => {\n                    if (chip !== event.source) {\n                        chip._setSelectedState(false, false, false);\n                    }\n                });\n            }\n            if (event.isUserInput) {\n                this._propagateChanges();\n            }\n        });\n    }\n    /**\n     * Focuses the first selected chip in this chip listbox, or the first non-disabled chip when there\n     * are no selected chips.\n     */\n    focus() {\n        if (this.disabled) {\n            return;\n        }\n        const firstSelectedChip = this._getFirstSelectedChip();\n        if (firstSelectedChip && !firstSelectedChip.disabled) {\n            firstSelectedChip.focus();\n        }\n        else if (this._chips.length > 0) {\n            this._keyManager.setFirstItemActive();\n        }\n        else {\n            this._elementRef.nativeElement.focus();\n        }\n    }\n    /**\n     * Implemented as part of ControlValueAccessor.\n     * @docs-private\n     */\n    writeValue(value) {\n        if (value != null) {\n            this.value = value;\n        }\n        else {\n            this.value = undefined;\n        }\n    }\n    /**\n     * Implemented as part of ControlValueAccessor.\n     * @docs-private\n     */\n    registerOnChange(fn) {\n        this._onChange = fn;\n    }\n    /**\n     * Implemented as part of ControlValueAccessor.\n     * @docs-private\n     */\n    registerOnTouched(fn) {\n        this._onTouched = fn;\n    }\n    /**\n     * Implemented as part of ControlValueAccessor.\n     * @docs-private\n     */\n    setDisabledState(isDisabled) {\n        this.disabled = isDisabled;\n    }\n    /** Selects all chips with value. */\n    _setSelectionByValue(value, isUserInput = true) {\n        this._clearSelection();\n        if (Array.isArray(value)) {\n            value.forEach(currentValue => this._selectValue(currentValue, isUserInput));\n        }\n        else {\n            this._selectValue(value, isUserInput);\n        }\n    }\n    /** When blurred, marks the field as touched when focus moved outside the chip listbox. */\n    _blur() {\n        if (!this.disabled) {\n            // Wait to see if focus moves to an individual chip.\n            setTimeout(() => {\n                if (!this.focused) {\n                    this._markAsTouched();\n                }\n            });\n        }\n    }\n    _keydown(event) {\n        if (event.keyCode === TAB) {\n            super._allowFocusEscape();\n        }\n    }\n    /** Marks the field as touched */\n    _markAsTouched() {\n        this._onTouched();\n        this._changeDetectorRef.markForCheck();\n    }\n    /** Emits change event to set the model value. */\n    _propagateChanges() {\n        let valueToEmit = null;\n        if (Array.isArray(this.selected)) {\n            valueToEmit = this.selected.map(chip => chip.value);\n        }\n        else {\n            valueToEmit = this.selected ? this.selected.value : undefined;\n        }\n        this._value = valueToEmit;\n        this.change.emit(new MatChipListboxChange(this, valueToEmit));\n        this._onChange(valueToEmit);\n        this._changeDetectorRef.markForCheck();\n    }\n    /**\n     * Deselects every chip in the listbox.\n     * @param skip Chip that should not be deselected.\n     */\n    _clearSelection(skip) {\n        this._chips.forEach(chip => {\n            if (chip !== skip) {\n                chip.deselect();\n            }\n        });\n    }\n    /**\n     * Finds and selects the chip based on its value.\n     * @returns Chip that has the corresponding value.\n     */\n    _selectValue(value, isUserInput) {\n        const correspondingChip = this._chips.find(chip => {\n            return chip.value != null && this.compareWith(chip.value, value);\n        });\n        if (correspondingChip) {\n            isUserInput ? correspondingChip.selectViaInteraction() : correspondingChip.select();\n        }\n        return correspondingChip;\n    }\n    /** Syncs the chip-listbox selection state with the individual chips. */\n    _syncListboxProperties() {\n        if (this._chips) {\n            // Defer setting the value in order to avoid the \"Expression\n            // has changed after it was checked\" errors from Angular.\n            Promise.resolve().then(() => {\n                this._chips.forEach(chip => {\n                    chip._chipListMultiple = this.multiple;\n                    chip.chipListSelectable = this._selectable;\n                    chip._chipListHideSingleSelectionIndicator = this.hideSingleSelectionIndicator;\n                    chip._changeDetectorRef.markForCheck();\n                });\n            });\n        }\n    }\n    /** Returns the first selected chip in this listbox, or undefined if no chips are selected. */\n    _getFirstSelectedChip() {\n        if (Array.isArray(this.selected)) {\n            return this.selected.length ? this.selected[0] : undefined;\n        }\n        else {\n            return this.selected;\n        }\n    }\n    /**\n     * Determines if key manager should avoid putting a given chip action in the tab index. Skip\n     * non-interactive actions since the user can't do anything with them.\n     */\n    _skipPredicate(action) {\n        // Override the skip predicate in the base class to avoid skipping disabled chips. Allow\n        // disabled chip options to receive focus to align with WAI ARIA recommendation. Normally WAI\n        // ARIA's instructions are to exclude disabled items from the tab order, but it makes a few\n        // exceptions for compound widgets.\n        //\n        // From [Developing a Keyboard Interface](\n        // https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/):\n        //   \"For the following composite widget elements, keep them focusable when disabled: Options in a\n        //   Listbox...\"\n        return !action.isInteractive;\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatChipListbox, deps: null, target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"19.2.6\", type: MatChipListbox, isStandalone: true, selector: \"mat-chip-listbox\", inputs: { multiple: [\"multiple\", \"multiple\", booleanAttribute], ariaOrientation: [\"aria-orientation\", \"ariaOrientation\"], selectable: [\"selectable\", \"selectable\", booleanAttribute], compareWith: \"compareWith\", required: [\"required\", \"required\", booleanAttribute], hideSingleSelectionIndicator: [\"hideSingleSelectionIndicator\", \"hideSingleSelectionIndicator\", booleanAttribute], value: \"value\" }, outputs: { change: \"change\" }, host: { listeners: { \"focus\": \"focus()\", \"blur\": \"_blur()\", \"keydown\": \"_keydown($event)\" }, properties: { \"attr.role\": \"role\", \"tabIndex\": \"(disabled || empty) ? -1 : tabIndex\", \"attr.aria-required\": \"role ? required : null\", \"attr.aria-disabled\": \"disabled.toString()\", \"attr.aria-multiselectable\": \"multiple\", \"attr.aria-orientation\": \"ariaOrientation\", \"class.mat-mdc-chip-list-disabled\": \"disabled\", \"class.mat-mdc-chip-list-required\": \"required\" }, classAttribute: \"mdc-evolution-chip-set mat-mdc-chip-listbox\" }, providers: [MAT_CHIP_LISTBOX_CONTROL_VALUE_ACCESSOR], queries: [{ propertyName: \"_chips\", predicate: MatChipOption, descendants: true }], usesInheritance: true, ngImport: i0, template: `\n    <div class=\"mdc-evolution-chip-set__chips\" role=\"presentation\">\n      <ng-content></ng-content>\n    </div>\n  `, isInline: true, styles: [\".mat-mdc-chip-set{display:flex}.mat-mdc-chip-set:focus{outline:none}.mat-mdc-chip-set .mdc-evolution-chip-set__chips{min-width:100%;margin-left:-8px;margin-right:0}.mat-mdc-chip-set .mdc-evolution-chip{margin:4px 0 4px 8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip-set__chips{margin-left:0;margin-right:-8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip{margin-left:0;margin-right:8px}.mdc-evolution-chip-set__chips{display:flex;flex-flow:wrap;min-width:0}.mat-mdc-chip-set-stacked{flex-direction:column;align-items:flex-start}.mat-mdc-chip-set-stacked .mat-mdc-chip{width:100%}.mat-mdc-chip-set-stacked .mdc-evolution-chip__graphic{flex-grow:0}.mat-mdc-chip-set-stacked .mdc-evolution-chip__action--primary{flex-basis:100%;justify-content:start}input.mat-mdc-chip-input{flex:1 0 150px;margin-left:8px}[dir=rtl] input.mat-mdc-chip-input{margin-left:0;margin-right:8px}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatChipListbox, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-chip-listbox', template: `\n    <div class=\"mdc-evolution-chip-set__chips\" role=\"presentation\">\n      <ng-content></ng-content>\n    </div>\n  `, host: {\n                        'class': 'mdc-evolution-chip-set mat-mdc-chip-listbox',\n                        '[attr.role]': 'role',\n                        '[tabIndex]': '(disabled || empty) ? -1 : tabIndex',\n                        '[attr.aria-required]': 'role ? required : null',\n                        '[attr.aria-disabled]': 'disabled.toString()',\n                        '[attr.aria-multiselectable]': 'multiple',\n                        '[attr.aria-orientation]': 'ariaOrientation',\n                        '[class.mat-mdc-chip-list-disabled]': 'disabled',\n                        '[class.mat-mdc-chip-list-required]': 'required',\n                        '(focus)': 'focus()',\n                        '(blur)': '_blur()',\n                        '(keydown)': '_keydown($event)',\n                    }, providers: [MAT_CHIP_LISTBOX_CONTROL_VALUE_ACCESSOR], encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, styles: [\".mat-mdc-chip-set{display:flex}.mat-mdc-chip-set:focus{outline:none}.mat-mdc-chip-set .mdc-evolution-chip-set__chips{min-width:100%;margin-left:-8px;margin-right:0}.mat-mdc-chip-set .mdc-evolution-chip{margin:4px 0 4px 8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip-set__chips{margin-left:0;margin-right:-8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip{margin-left:0;margin-right:8px}.mdc-evolution-chip-set__chips{display:flex;flex-flow:wrap;min-width:0}.mat-mdc-chip-set-stacked{flex-direction:column;align-items:flex-start}.mat-mdc-chip-set-stacked .mat-mdc-chip{width:100%}.mat-mdc-chip-set-stacked .mdc-evolution-chip__graphic{flex-grow:0}.mat-mdc-chip-set-stacked .mdc-evolution-chip__action--primary{flex-basis:100%;justify-content:start}input.mat-mdc-chip-input{flex:1 0 150px;margin-left:8px}[dir=rtl] input.mat-mdc-chip-input{margin-left:0;margin-right:8px}\\n\"] }]\n        }], propDecorators: { multiple: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], ariaOrientation: [{\n                type: Input,\n                args: ['aria-orientation']\n            }], selectable: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], compareWith: [{\n                type: Input\n            }], required: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], hideSingleSelectionIndicator: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], value: [{\n                type: Input\n            }], change: [{\n                type: Output\n            }], _chips: [{\n                type: ContentChildren,\n                args: [MatChipOption, {\n                        // We need to use `descendants: true`, because Ivy will no longer match\n                        // indirect descendants if it's left as false.\n                        descendants: true,\n                    }]\n            }] } });\n\n/** Change event object that is emitted when the chip grid value has changed. */\nclass MatChipGridChange {\n    source;\n    value;\n    constructor(\n    /** Chip grid that emitted the event. */\n    source, \n    /** Value of the chip grid when the event was emitted. */\n    value) {\n        this.source = source;\n        this.value = value;\n    }\n}\n/**\n * An extension of the MatChipSet component used with MatChipRow chips and\n * the matChipInputFor directive.\n */\nclass MatChipGrid extends MatChipSet {\n    ngControl = inject(NgControl, { optional: true, self: true });\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    controlType = 'mat-chip-grid';\n    /** The chip input to add more chips */\n    _chipInput;\n    _defaultRole = 'grid';\n    _errorStateTracker;\n    /**\n     * List of element ids to propagate to the chipInput's aria-describedby attribute.\n     */\n    _ariaDescribedbyIds = [];\n    /**\n     * Function when touched. Set as part of ControlValueAccessor implementation.\n     * @docs-private\n     */\n    _onTouched = () => { };\n    /**\n     * Function when changed. Set as part of ControlValueAccessor implementation.\n     * @docs-private\n     */\n    _onChange = () => { };\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get disabled() {\n        return this.ngControl ? !!this.ngControl.disabled : this._disabled;\n    }\n    set disabled(value) {\n        this._disabled = value;\n        this._syncChipsState();\n        this.stateChanges.next();\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get id() {\n        return this._chipInput.id;\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get empty() {\n        return ((!this._chipInput || this._chipInput.empty) && (!this._chips || this._chips.length === 0));\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get placeholder() {\n        return this._chipInput ? this._chipInput.placeholder : this._placeholder;\n    }\n    set placeholder(value) {\n        this._placeholder = value;\n        this.stateChanges.next();\n    }\n    _placeholder;\n    /** Whether any chips or the matChipInput inside of this chip-grid has focus. */\n    get focused() {\n        return this._chipInput.focused || this._hasFocusedChip();\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get required() {\n        return this._required ?? this.ngControl?.control?.hasValidator(Validators.required) ?? false;\n    }\n    set required(value) {\n        this._required = value;\n        this.stateChanges.next();\n    }\n    _required;\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get shouldLabelFloat() {\n        return !this.empty || this.focused;\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get value() {\n        return this._value;\n    }\n    set value(value) {\n        this._value = value;\n    }\n    _value = [];\n    /** An object used to control when error messages are shown. */\n    get errorStateMatcher() {\n        return this._errorStateTracker.matcher;\n    }\n    set errorStateMatcher(value) {\n        this._errorStateTracker.matcher = value;\n    }\n    /** Combined stream of all of the child chips' blur events. */\n    get chipBlurChanges() {\n        return this._getChipStream(chip => chip._onBlur);\n    }\n    /** Emits when the chip grid value has been changed by the user. */\n    change = new EventEmitter();\n    /**\n     * Emits whenever the raw value of the chip-grid changes. This is here primarily\n     * to facilitate the two-way binding for the `value` input.\n     * @docs-private\n     */\n    valueChange = new EventEmitter();\n    _chips = undefined;\n    /**\n     * Emits whenever the component state changes and should cause the parent\n     * form-field to update. Implemented as part of `MatFormFieldControl`.\n     * @docs-private\n     */\n    stateChanges = new Subject();\n    /** Whether the chip grid is in an error state. */\n    get errorState() {\n        return this._errorStateTracker.errorState;\n    }\n    set errorState(value) {\n        this._errorStateTracker.errorState = value;\n    }\n    constructor() {\n        super();\n        const parentForm = inject(NgForm, { optional: true });\n        const parentFormGroup = inject(FormGroupDirective, { optional: true });\n        const defaultErrorStateMatcher = inject(ErrorStateMatcher);\n        if (this.ngControl) {\n            this.ngControl.valueAccessor = this;\n        }\n        this._errorStateTracker = new _ErrorStateTracker(defaultErrorStateMatcher, this.ngControl, parentFormGroup, parentForm, this.stateChanges);\n    }\n    ngAfterContentInit() {\n        this.chipBlurChanges.pipe(takeUntil(this._destroyed)).subscribe(() => {\n            this._blur();\n            this.stateChanges.next();\n        });\n        merge(this.chipFocusChanges, this._chips.changes)\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(() => this.stateChanges.next());\n    }\n    ngAfterViewInit() {\n        super.ngAfterViewInit();\n        if (!this._chipInput && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('mat-chip-grid must be used in combination with matChipInputFor.');\n        }\n    }\n    ngDoCheck() {\n        if (this.ngControl) {\n            // We need to re-evaluate this on every change detection cycle, because there are some\n            // error triggers that we can't subscribe to (e.g. parent form submissions). This means\n            // that whatever logic is in here has to be super lean or we risk destroying the performance.\n            this.updateErrorState();\n        }\n    }\n    ngOnDestroy() {\n        super.ngOnDestroy();\n        this.stateChanges.complete();\n    }\n    /** Associates an HTML input element with this chip grid. */\n    registerInput(inputElement) {\n        this._chipInput = inputElement;\n        this._chipInput.setDescribedByIds(this._ariaDescribedbyIds);\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    onContainerClick(event) {\n        if (!this.disabled && !this._originatesFromChip(event)) {\n            this.focus();\n        }\n    }\n    /**\n     * Focuses the first chip in this chip grid, or the associated input when there\n     * are no eligible chips.\n     */\n    focus() {\n        if (this.disabled || this._chipInput.focused) {\n            return;\n        }\n        if (!this._chips.length || this._chips.first.disabled) {\n            // Delay until the next tick, because this can cause a \"changed after checked\"\n            // error if the input does something on focus (e.g. opens an autocomplete).\n            Promise.resolve().then(() => this._chipInput.focus());\n        }\n        else {\n            const activeItem = this._keyManager.activeItem;\n            if (activeItem) {\n                activeItem.focus();\n            }\n            else {\n                this._keyManager.setFirstItemActive();\n            }\n        }\n        this.stateChanges.next();\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    setDescribedByIds(ids) {\n        // We must keep this up to date to handle the case where ids are set\n        // before the chip input is registered.\n        this._ariaDescribedbyIds = ids;\n        this._chipInput?.setDescribedByIds(ids);\n    }\n    /**\n     * Implemented as part of ControlValueAccessor.\n     * @docs-private\n     */\n    writeValue(value) {\n        // The user is responsible for creating the child chips, so we just store the value.\n        this._value = value;\n    }\n    /**\n     * Implemented as part of ControlValueAccessor.\n     * @docs-private\n     */\n    registerOnChange(fn) {\n        this._onChange = fn;\n    }\n    /**\n     * Implemented as part of ControlValueAccessor.\n     * @docs-private\n     */\n    registerOnTouched(fn) {\n        this._onTouched = fn;\n    }\n    /**\n     * Implemented as part of ControlValueAccessor.\n     * @docs-private\n     */\n    setDisabledState(isDisabled) {\n        this.disabled = isDisabled;\n        this.stateChanges.next();\n    }\n    /** Refreshes the error state of the chip grid. */\n    updateErrorState() {\n        this._errorStateTracker.updateErrorState();\n    }\n    /** When blurred, mark the field as touched when focus moved outside the chip grid. */\n    _blur() {\n        if (!this.disabled) {\n            // Check whether the focus moved to chip input.\n            // If the focus is not moved to chip input, mark the field as touched. If the focus moved\n            // to chip input, do nothing.\n            // Timeout is needed to wait for the focus() event trigger on chip input.\n            setTimeout(() => {\n                if (!this.focused) {\n                    this._propagateChanges();\n                    this._markAsTouched();\n                }\n            });\n        }\n    }\n    /**\n     * Removes the `tabindex` from the chip grid and resets it back afterwards, allowing the\n     * user to tab out of it. This prevents the grid from capturing focus and redirecting\n     * it back to the first chip, creating a focus trap, if it user tries to tab away.\n     */\n    _allowFocusEscape() {\n        if (!this._chipInput.focused) {\n            super._allowFocusEscape();\n        }\n    }\n    /** Handles custom keyboard events. */\n    _handleKeydown(event) {\n        const keyCode = event.keyCode;\n        const activeItem = this._keyManager.activeItem;\n        if (keyCode === TAB) {\n            if (this._chipInput.focused &&\n                hasModifierKey(event, 'shiftKey') &&\n                this._chips.length &&\n                !this._chips.last.disabled) {\n                event.preventDefault();\n                if (activeItem) {\n                    this._keyManager.setActiveItem(activeItem);\n                }\n                else {\n                    this._focusLastChip();\n                }\n            }\n            else {\n                // Use the super method here since it doesn't check for the input\n                // focused state. This allows focus to escape if there's only one\n                // disabled chip left in the list.\n                super._allowFocusEscape();\n            }\n        }\n        else if (!this._chipInput.focused) {\n            // The up and down arrows are supposed to navigate between the individual rows in the grid.\n            // We do this by filtering the actions down to the ones that have the same `_isPrimary`\n            // flag as the active action and moving focus between them ourseles instead of delegating\n            // to the key manager. For more information, see #29359 and:\n            // https://www.w3.org/WAI/ARIA/apg/patterns/grid/examples/layout-grids/#ex2_label\n            if ((keyCode === UP_ARROW || keyCode === DOWN_ARROW) && activeItem) {\n                const eligibleActions = this._chipActions.filter(action => action._isPrimary === activeItem._isPrimary && !this._skipPredicate(action));\n                const currentIndex = eligibleActions.indexOf(activeItem);\n                const delta = event.keyCode === UP_ARROW ? -1 : 1;\n                event.preventDefault();\n                if (currentIndex > -1 && this._isValidIndex(currentIndex + delta)) {\n                    this._keyManager.setActiveItem(eligibleActions[currentIndex + delta]);\n                }\n            }\n            else {\n                super._handleKeydown(event);\n            }\n        }\n        this.stateChanges.next();\n    }\n    _focusLastChip() {\n        if (this._chips.length) {\n            this._chips.last.focus();\n        }\n    }\n    /** Emits change event to set the model value. */\n    _propagateChanges() {\n        const valueToEmit = this._chips.length ? this._chips.toArray().map(chip => chip.value) : [];\n        this._value = valueToEmit;\n        this.change.emit(new MatChipGridChange(this, valueToEmit));\n        this.valueChange.emit(valueToEmit);\n        this._onChange(valueToEmit);\n        this._changeDetectorRef.markForCheck();\n    }\n    /** Mark the field as touched */\n    _markAsTouched() {\n        this._onTouched();\n        this._changeDetectorRef.markForCheck();\n        this.stateChanges.next();\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatChipGrid, deps: [], target: i0.ɵɵFactoryTarget.Component });\n    static ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"19.2.6\", type: MatChipGrid, isStandalone: true, selector: \"mat-chip-grid\", inputs: { disabled: [\"disabled\", \"disabled\", booleanAttribute], placeholder: \"placeholder\", required: [\"required\", \"required\", booleanAttribute], value: \"value\", errorStateMatcher: \"errorStateMatcher\" }, outputs: { change: \"change\", valueChange: \"valueChange\" }, host: { listeners: { \"focus\": \"focus()\", \"blur\": \"_blur()\" }, properties: { \"attr.role\": \"role\", \"attr.tabindex\": \"(disabled || (_chips && _chips.length === 0)) ? -1 : tabIndex\", \"attr.aria-disabled\": \"disabled.toString()\", \"attr.aria-invalid\": \"errorState\", \"class.mat-mdc-chip-list-disabled\": \"disabled\", \"class.mat-mdc-chip-list-invalid\": \"errorState\", \"class.mat-mdc-chip-list-required\": \"required\" }, classAttribute: \"mat-mdc-chip-set mat-mdc-chip-grid mdc-evolution-chip-set\" }, providers: [{ provide: MatFormFieldControl, useExisting: MatChipGrid }], queries: [{ propertyName: \"_chips\", predicate: MatChipRow, descendants: true }], usesInheritance: true, ngImport: i0, template: `\n    <div class=\"mdc-evolution-chip-set__chips\" role=\"presentation\">\n      <ng-content></ng-content>\n    </div>\n  `, isInline: true, styles: [\".mat-mdc-chip-set{display:flex}.mat-mdc-chip-set:focus{outline:none}.mat-mdc-chip-set .mdc-evolution-chip-set__chips{min-width:100%;margin-left:-8px;margin-right:0}.mat-mdc-chip-set .mdc-evolution-chip{margin:4px 0 4px 8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip-set__chips{margin-left:0;margin-right:-8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip{margin-left:0;margin-right:8px}.mdc-evolution-chip-set__chips{display:flex;flex-flow:wrap;min-width:0}.mat-mdc-chip-set-stacked{flex-direction:column;align-items:flex-start}.mat-mdc-chip-set-stacked .mat-mdc-chip{width:100%}.mat-mdc-chip-set-stacked .mdc-evolution-chip__graphic{flex-grow:0}.mat-mdc-chip-set-stacked .mdc-evolution-chip__action--primary{flex-basis:100%;justify-content:start}input.mat-mdc-chip-input{flex:1 0 150px;margin-left:8px}[dir=rtl] input.mat-mdc-chip-input{margin-left:0;margin-right:8px}\\n\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatChipGrid, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-chip-grid', template: `\n    <div class=\"mdc-evolution-chip-set__chips\" role=\"presentation\">\n      <ng-content></ng-content>\n    </div>\n  `, host: {\n                        'class': 'mat-mdc-chip-set mat-mdc-chip-grid mdc-evolution-chip-set',\n                        '[attr.role]': 'role',\n                        '[attr.tabindex]': '(disabled || (_chips && _chips.length === 0)) ? -1 : tabIndex',\n                        '[attr.aria-disabled]': 'disabled.toString()',\n                        '[attr.aria-invalid]': 'errorState',\n                        '[class.mat-mdc-chip-list-disabled]': 'disabled',\n                        '[class.mat-mdc-chip-list-invalid]': 'errorState',\n                        '[class.mat-mdc-chip-list-required]': 'required',\n                        '(focus)': 'focus()',\n                        '(blur)': '_blur()',\n                    }, providers: [{ provide: MatFormFieldControl, useExisting: MatChipGrid }], encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, styles: [\".mat-mdc-chip-set{display:flex}.mat-mdc-chip-set:focus{outline:none}.mat-mdc-chip-set .mdc-evolution-chip-set__chips{min-width:100%;margin-left:-8px;margin-right:0}.mat-mdc-chip-set .mdc-evolution-chip{margin:4px 0 4px 8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip-set__chips{margin-left:0;margin-right:-8px}[dir=rtl] .mat-mdc-chip-set .mdc-evolution-chip{margin-left:0;margin-right:8px}.mdc-evolution-chip-set__chips{display:flex;flex-flow:wrap;min-width:0}.mat-mdc-chip-set-stacked{flex-direction:column;align-items:flex-start}.mat-mdc-chip-set-stacked .mat-mdc-chip{width:100%}.mat-mdc-chip-set-stacked .mdc-evolution-chip__graphic{flex-grow:0}.mat-mdc-chip-set-stacked .mdc-evolution-chip__action--primary{flex-basis:100%;justify-content:start}input.mat-mdc-chip-input{flex:1 0 150px;margin-left:8px}[dir=rtl] input.mat-mdc-chip-input{margin-left:0;margin-right:8px}\\n\"] }]\n        }], ctorParameters: () => [], propDecorators: { disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], placeholder: [{\n                type: Input\n            }], required: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], value: [{\n                type: Input\n            }], errorStateMatcher: [{\n                type: Input\n            }], change: [{\n                type: Output\n            }], valueChange: [{\n                type: Output\n            }], _chips: [{\n                type: ContentChildren,\n                args: [MatChipRow, {\n                        // We need to use `descendants: true`, because Ivy will no longer match\n                        // indirect descendants if it's left as false.\n                        descendants: true,\n                    }]\n            }] } });\n\n/**\n * Directive that adds chip-specific behaviors to an input element inside `<mat-form-field>`.\n * May be placed inside or outside of a `<mat-chip-grid>`.\n */\nclass MatChipInput {\n    _elementRef = inject(ElementRef);\n    /** Whether the control is focused. */\n    focused = false;\n    /** Register input for chip list */\n    get chipGrid() {\n        return this._chipGrid;\n    }\n    set chipGrid(value) {\n        if (value) {\n            this._chipGrid = value;\n            this._chipGrid.registerInput(this);\n        }\n    }\n    _chipGrid;\n    /**\n     * Whether or not the chipEnd event will be emitted when the input is blurred.\n     */\n    addOnBlur = false;\n    /**\n     * The list of key codes that will trigger a chipEnd event.\n     *\n     * Defaults to `[ENTER]`.\n     */\n    separatorKeyCodes;\n    /** Emitted when a chip is to be added. */\n    chipEnd = new EventEmitter();\n    /** The input's placeholder text. */\n    placeholder = '';\n    /** Unique id for the input. */\n    id = inject(_IdGenerator).getId('mat-mdc-chip-list-input-');\n    /** Whether the input is disabled. */\n    get disabled() {\n        return this._disabled || (this._chipGrid && this._chipGrid.disabled);\n    }\n    set disabled(value) {\n        this._disabled = value;\n    }\n    _disabled = false;\n    /** Whether the input is empty. */\n    get empty() {\n        return !this.inputElement.value;\n    }\n    /** The native input element to which this directive is attached. */\n    inputElement;\n    constructor() {\n        const defaultOptions = inject(MAT_CHIPS_DEFAULT_OPTIONS);\n        const formField = inject(MAT_FORM_FIELD, { optional: true });\n        this.inputElement = this._elementRef.nativeElement;\n        this.separatorKeyCodes = defaultOptions.separatorKeyCodes;\n        if (formField) {\n            this.inputElement.classList.add('mat-mdc-form-field-input-control');\n        }\n    }\n    ngOnChanges() {\n        this._chipGrid.stateChanges.next();\n    }\n    ngOnDestroy() {\n        this.chipEnd.complete();\n    }\n    /** Utility method to make host definition/tests more clear. */\n    _keydown(event) {\n        if (this.empty && event.keyCode === BACKSPACE) {\n            // Ignore events where the user is holding down backspace\n            // so that we don't accidentally remove too many chips.\n            if (!event.repeat) {\n                this._chipGrid._focusLastChip();\n            }\n            event.preventDefault();\n        }\n        else {\n            this._emitChipEnd(event);\n        }\n    }\n    /** Checks to see if the blur should emit the (chipEnd) event. */\n    _blur() {\n        if (this.addOnBlur) {\n            this._emitChipEnd();\n        }\n        this.focused = false;\n        // Blur the chip list if it is not focused\n        if (!this._chipGrid.focused) {\n            this._chipGrid._blur();\n        }\n        this._chipGrid.stateChanges.next();\n    }\n    _focus() {\n        this.focused = true;\n        this._chipGrid.stateChanges.next();\n    }\n    /** Checks to see if the (chipEnd) event needs to be emitted. */\n    _emitChipEnd(event) {\n        if (!event || (this._isSeparatorKey(event) && !event.repeat)) {\n            this.chipEnd.emit({\n                input: this.inputElement,\n                value: this.inputElement.value,\n                chipInput: this,\n            });\n            event?.preventDefault();\n        }\n    }\n    _onInput() {\n        // Let chip list know whenever the value changes.\n        this._chipGrid.stateChanges.next();\n    }\n    /** Focuses the input. */\n    focus() {\n        this.inputElement.focus();\n    }\n    /** Clears the input */\n    clear() {\n        this.inputElement.value = '';\n    }\n    setDescribedByIds(ids) {\n        const element = this._elementRef.nativeElement;\n        // Set the value directly in the DOM since this binding\n        // is prone to \"changed after checked\" errors.\n        if (ids.length) {\n            element.setAttribute('aria-describedby', ids.join(' '));\n        }\n        else {\n            element.removeAttribute('aria-describedby');\n        }\n    }\n    /** Checks whether a keycode is one of the configured separators. */\n    _isSeparatorKey(event) {\n        return !hasModifierKey(event) && new Set(this.separatorKeyCodes).has(event.keyCode);\n    }\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatChipInput, deps: [], target: i0.ɵɵFactoryTarget.Directive });\n    static ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"19.2.6\", type: MatChipInput, isStandalone: true, selector: \"input[matChipInputFor]\", inputs: { chipGrid: [\"matChipInputFor\", \"chipGrid\"], addOnBlur: [\"matChipInputAddOnBlur\", \"addOnBlur\", booleanAttribute], separatorKeyCodes: [\"matChipInputSeparatorKeyCodes\", \"separatorKeyCodes\"], placeholder: \"placeholder\", id: \"id\", disabled: [\"disabled\", \"disabled\", booleanAttribute] }, outputs: { chipEnd: \"matChipInputTokenEnd\" }, host: { listeners: { \"keydown\": \"_keydown($event)\", \"blur\": \"_blur()\", \"focus\": \"_focus()\", \"input\": \"_onInput()\" }, properties: { \"id\": \"id\", \"attr.disabled\": \"disabled || null\", \"attr.placeholder\": \"placeholder || null\", \"attr.aria-invalid\": \"_chipGrid && _chipGrid.ngControl ? _chipGrid.ngControl.invalid : null\", \"attr.aria-required\": \"_chipGrid && _chipGrid.required || null\", \"attr.required\": \"_chipGrid && _chipGrid.required || null\" }, classAttribute: \"mat-mdc-chip-input mat-mdc-input-element mdc-text-field__input mat-input-element\" }, exportAs: [\"matChipInput\", \"matChipInputFor\"], usesOnChanges: true, ngImport: i0 });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatChipInput, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'input[matChipInputFor]',\n                    exportAs: 'matChipInput, matChipInputFor',\n                    host: {\n                        // TODO: eventually we should remove `mat-input-element` from here since it comes from the\n                        // non-MDC version of the input. It's currently being kept for backwards compatibility, because\n                        // the MDC chips were landed initially with it.\n                        'class': 'mat-mdc-chip-input mat-mdc-input-element mdc-text-field__input mat-input-element',\n                        '(keydown)': '_keydown($event)',\n                        '(blur)': '_blur()',\n                        '(focus)': '_focus()',\n                        '(input)': '_onInput()',\n                        '[id]': 'id',\n                        '[attr.disabled]': 'disabled || null',\n                        '[attr.placeholder]': 'placeholder || null',\n                        '[attr.aria-invalid]': '_chipGrid && _chipGrid.ngControl ? _chipGrid.ngControl.invalid : null',\n                        '[attr.aria-required]': '_chipGrid && _chipGrid.required || null',\n                        '[attr.required]': '_chipGrid && _chipGrid.required || null',\n                    },\n                }]\n        }], ctorParameters: () => [], propDecorators: { chipGrid: [{\n                type: Input,\n                args: ['matChipInputFor']\n            }], addOnBlur: [{\n                type: Input,\n                args: [{ alias: 'matChipInputAddOnBlur', transform: booleanAttribute }]\n            }], separatorKeyCodes: [{\n                type: Input,\n                args: ['matChipInputSeparatorKeyCodes']\n            }], chipEnd: [{\n                type: Output,\n                args: ['matChipInputTokenEnd']\n            }], placeholder: [{\n                type: Input\n            }], id: [{\n                type: Input\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }] } });\n\nconst CHIP_DECLARATIONS = [\n    MatChip,\n    MatChipAvatar,\n    MatChipEditInput,\n    MatChipGrid,\n    MatChipInput,\n    MatChipListbox,\n    MatChipOption,\n    MatChipRemove,\n    MatChipRow,\n    MatChipSet,\n    MatChipTrailingIcon,\n];\nclass MatChipsModule {\n    static ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatChipsModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\n    static ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"19.2.6\", ngImport: i0, type: MatChipsModule, imports: [MatCommonModule, MatRippleModule, MatChipAction, MatChip,\n            MatChipAvatar,\n            MatChipEditInput,\n            MatChipGrid,\n            MatChipInput,\n            MatChipListbox,\n            MatChipOption,\n            MatChipRemove,\n            MatChipRow,\n            MatChipSet,\n            MatChipTrailingIcon], exports: [MatCommonModule, MatChip,\n            MatChipAvatar,\n            MatChipEditInput,\n            MatChipGrid,\n            MatChipInput,\n            MatChipListbox,\n            MatChipOption,\n            MatChipRemove,\n            MatChipRow,\n            MatChipSet,\n            MatChipTrailingIcon] });\n    static ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatChipsModule, providers: [\n            ErrorStateMatcher,\n            {\n                provide: MAT_CHIPS_DEFAULT_OPTIONS,\n                useValue: {\n                    separatorKeyCodes: [ENTER],\n                },\n            },\n        ], imports: [MatCommonModule, MatRippleModule, MatCommonModule] });\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"19.2.6\", ngImport: i0, type: MatChipsModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule, MatRippleModule, MatChipAction, CHIP_DECLARATIONS],\n                    exports: [MatCommonModule, CHIP_DECLARATIONS],\n                    providers: [\n                        ErrorStateMatcher,\n                        {\n                            provide: MAT_CHIPS_DEFAULT_OPTIONS,\n                            useValue: {\n                                separatorKeyCodes: [ENTER],\n                            },\n                        },\n                    ],\n                }]\n        }] });\n\nexport { MAT_CHIP, MAT_CHIPS_DEFAULT_OPTIONS, MAT_CHIP_AVATAR, MAT_CHIP_LISTBOX_CONTROL_VALUE_ACCESSOR, MAT_CHIP_REMOVE, MAT_CHIP_TRAILING_ICON, MatChip, MatChipAvatar, MatChipEditInput, MatChipGrid, MatChipGridChange, MatChipInput, MatChipListbox, MatChipListboxChange, MatChipOption, MatChipRemove, MatChipRow, MatChipSelectionChange, MatChipSet, MatChipTrailingIcon, MatChipsModule };\n"], "mappings": "AAAA,SAASA,YAAY,EAAEC,YAAY,EAAEC,eAAe,QAAQ,mBAAmB;AAC/E,SAASC,KAAK,EAAEC,KAAK,EAAEC,SAAS,EAAEC,MAAM,EAAEC,GAAG,EAAEC,cAAc,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,uBAAuB;AAClH,SAASC,sBAAsB,EAAEC,qBAAqB,QAAQ,sBAAsB;AACpF,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,MAAM,EAAEC,UAAU,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,SAAS,EAAEC,KAAK,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,qBAAqB,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,eAAe,EAAEC,MAAM,EAAEC,YAAY,EAAEC,SAAS,EAAEC,eAAe,EAAEC,SAAS,EAAEC,UAAU,EAAEC,QAAQ,QAAQ,eAAe;AAC5V,SAASC,OAAO,EAAEC,KAAK,QAAQ,MAAM;AACrC,SAASC,CAAC,IAAIC,uBAAuB,QAAQ,kCAAkC;AAC/E,SAASC,CAAC,IAAIC,yBAAyB,QAAQ,uBAAuB;AACtE,SAASC,CAAC,IAAIC,eAAe,QAAQ,8BAA8B;AACnE,SAASC,SAAS,EAAEC,SAAS,EAAEC,SAAS,QAAQ,gBAAgB;AAChE,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,iBAAiB,EAAEC,SAAS,EAAEC,UAAU,EAAEC,MAAM,EAAEC,kBAAkB,QAAQ,gBAAgB;AACrG,SAASC,CAAC,IAAIC,iBAAiB,QAAQ,8BAA8B;AACrE,SAAShB,CAAC,IAAIiB,kBAAkB,QAAQ,4BAA4B;AACpE,SAASC,CAAC,IAAIC,mBAAmB,EAAEC,CAAC,IAAIC,cAAc,QAAQ,2BAA2B;AACzF,SAASjB,CAAC,IAAIkB,eAAe,QAAQ,8BAA8B;AACnE,SAASlB,CAAC,IAAImB,eAAe,QAAQ,sBAAsB;AAC3D,OAAO,uBAAuB;AAC9B,OAAO,uBAAuB;AAC9B,OAAO,gCAAgC;;AAEvC;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,+BAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAgG6FrD,EAAE,CAAAuD,cAAA,aA8Wm+E,CAAC;IA9Wt+EvD,EAAE,CAAAwD,YAAA,KA8WkjF,CAAC;IA9WrjFxD,EAAE,CAAAyD,YAAA,CA8WikF,CAAC;EAAA;AAAA;AAAA,SAAAC,+BAAAL,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9WpkFrD,EAAE,CAAAuD,cAAA,aA8W+5F,CAAC;IA9Wl6FvD,EAAE,CAAAwD,YAAA,KA8WsgG,CAAC;IA9WzgGxD,EAAE,CAAAyD,YAAA,CA8WihG,CAAC;EAAA;AAAA;AAAA,SAAAE,qCAAAN,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA9WphGrD,EAAE,CAAAuD,cAAA,aAikBua,CAAC;IAjkB1avD,EAAE,CAAAwD,YAAA,KAikBsf,CAAC;IAjkBzfxD,EAAE,CAAAuD,cAAA,aAikB8iB,CAAC;IAjkBjjBvD,EAAE,CAAA4D,cAAA;IAAF5D,EAAE,CAAAuD,cAAA,YAikBguB,CAAC;IAjkBnuBvD,EAAE,CAAA6D,SAAA,cAikBm4B,CAAC;IAjkBt4B7D,EAAE,CAAAyD,YAAA,CAikBq5B,CAAC,CAAgB,CAAC,CAAc,CAAC;EAAA;AAAA;AAAA,SAAAK,qCAAAT,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAjkBx7BrD,EAAE,CAAAuD,cAAA,aAikBqxC,CAAC;IAjkBxxCvD,EAAE,CAAAwD,YAAA,KAikB43C,CAAC;IAjkB/3CxD,EAAE,CAAAyD,YAAA,CAikBu4C,CAAC;EAAA;AAAA;AAAA,MAAAM,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,kCAAAb,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAjkB14CrD,EAAE,CAAA6D,SAAA,aAgwBoQ,CAAC;EAAA;AAAA;AAAA,SAAAM,kCAAAd,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAhwBvQrD,EAAE,CAAAuD,cAAA,aAgwB+kB,CAAC;IAhwBllBvD,EAAE,CAAAwD,YAAA,EAgwB4pB,CAAC;IAhwB/pBxD,EAAE,CAAAyD,YAAA,CAgwByqB,CAAC;EAAA;AAAA;AAAA,SAAAW,gDAAAf,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAhwB5qBrD,EAAE,CAAAwD,YAAA,KAgwBs3B,CAAC;EAAA;AAAA;AAAA,SAAAa,gDAAAhB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAhwBz3BrD,EAAE,CAAA6D,SAAA,aAgwB+6B,CAAC;EAAA;AAAA;AAAA,SAAAS,kCAAAjB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAhwBl7BrD,EAAE,CAAAuE,UAAA,IAAAH,+CAAA,MAgwBqzB,CAAC,IAAAC,+CAAA,iBAAiF,CAAC;EAAA;EAAA,IAAAhB,EAAA;IAAA,MAAAmB,MAAA,GAhwB14BxE,EAAE,CAAAyE,aAAA;IAAFzE,EAAE,CAAA0E,aAAA,CAAAF,MAAA,CAAAG,gBAAA,QAgwBw7B,CAAC;EAAA;AAAA;AAAA,SAAAC,kCAAAvB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAhwB37BrD,EAAE,CAAAwD,YAAA,KAgwBw+B,CAAC;EAAA;AAAA;AAAA,SAAAqB,kCAAAxB,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAhwB3+BrD,EAAE,CAAAuD,cAAA,aAgwB0vC,CAAC;IAhwB7vCvD,EAAE,CAAAwD,YAAA,KAgwBi2C,CAAC;IAhwBp2CxD,EAAE,CAAAyD,YAAA,CAgwB42C,CAAC;EAAA;AAAA;AAAA,MAAAqB,GAAA;AAAA,MAAAC,GAAA;AA/1B58C,MAAMC,yBAAyB,GAAG,IAAI/E,cAAc,CAAC,2BAA2B,EAAE;EAC9EgF,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEA,CAAA,MAAO;IACZC,iBAAiB,EAAE,CAAC9F,KAAK;EAC7B,CAAC;AACL,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA,MAAM+F,eAAe,GAAG,IAAInF,cAAc,CAAC,eAAe,CAAC;AAC3D;AACA;AACA;AACA;AACA;AACA,MAAMoF,sBAAsB,GAAG,IAAIpF,cAAc,CAAC,qBAAqB,CAAC;AACxE;AACA;AACA;AACA;AACA;AACA,MAAMqF,eAAe,GAAG,IAAIrF,cAAc,CAAC,eAAe,CAAC;AAC3D;AACA;AACA;AACA,MAAMsF,QAAQ,GAAG,IAAItF,cAAc,CAAC,SAAS,CAAC;;AAE9C;AACA;AACA;AACA;AACA,MAAMuF,aAAa,CAAC;EAChBC,WAAW,GAAGvF,MAAM,CAACC,UAAU,CAAC;EAChCuF,WAAW,GAAGxF,MAAM,CAACqF,QAAQ,CAAC;EAC9B;EACAI,aAAa,GAAG,IAAI;EACpB;EACAC,UAAU,GAAG,IAAI;EACjB;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS,IAAI,IAAI,CAACJ,WAAW,EAAEG,QAAQ,IAAI,KAAK;EAChE;EACA,IAAIA,QAAQA,CAACE,KAAK,EAAE;IAChB,IAAI,CAACD,SAAS,GAAGC,KAAK;EAC1B;EACAD,SAAS,GAAG,KAAK;EACjB;EACAE,QAAQ,GAAG,CAAC,CAAC;EACb;AACJ;AACA;EACIC,uBAAuB,GAAG,KAAK;EAC/B;AACJ;AACA;EACIC,qBAAqBA,CAAA,EAAG;IACpB;IACA;IACA,OAAO,IAAI,CAACL,QAAQ,IAAI,CAAC,IAAI,CAACI,uBAAuB,GAAG,EAAE,GAAG,IAAI;EACrE;EACA;AACJ;AACA;EACIE,YAAYA,CAAA,EAAG;IACX,OAAQ,IAAI,CAACN,QAAQ,IAAI,CAAC,IAAI,CAACI,uBAAuB,IAAK,CAAC,IAAI,CAACN,aAAa,GACxE,IAAI,GACJ,IAAI,CAACK,QAAQ,CAACI,QAAQ,CAAC,CAAC;EAClC;EACAC,WAAWA,CAAA,EAAG;IACVnG,MAAM,CAACL,sBAAsB,CAAC,CAACyG,IAAI,CAAC3E,uBAAuB,CAAC;IAC5D,IAAI,IAAI,CAAC8D,WAAW,CAACc,aAAa,CAACC,QAAQ,KAAK,QAAQ,EAAE;MACtD,IAAI,CAACf,WAAW,CAACc,aAAa,CAACE,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC;IACjE;EACJ;EACAC,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACjB,WAAW,CAACc,aAAa,CAACG,KAAK,CAAC,CAAC;EAC1C;EACAC,YAAYA,CAACC,KAAK,EAAE;IAChB,IAAI,CAAC,IAAI,CAACf,QAAQ,IAAI,IAAI,CAACF,aAAa,IAAI,IAAI,CAACC,UAAU,EAAE;MACzDgB,KAAK,CAACC,cAAc,CAAC,CAAC;MACtB,IAAI,CAACnB,WAAW,CAACoB,+BAA+B,CAAC,CAAC;IACtD;EACJ;EACAC,cAAcA,CAACH,KAAK,EAAE;IAClB,IAAI,CAACA,KAAK,CAACI,OAAO,KAAK3H,KAAK,IAAIuH,KAAK,CAACI,OAAO,KAAK1H,KAAK,KACnD,CAAC,IAAI,CAACuG,QAAQ,IACd,IAAI,CAACF,aAAa,IAClB,IAAI,CAACC,UAAU,IACf,CAAC,IAAI,CAACF,WAAW,CAACuB,UAAU,EAAE;MAC9BL,KAAK,CAACC,cAAc,CAAC,CAAC;MACtB,IAAI,CAACnB,WAAW,CAACoB,+BAA+B,CAAC,CAAC;IACtD;EACJ;EACA,OAAOI,IAAI,YAAAC,sBAAAC,iBAAA;IAAA,YAAAA,iBAAA,IAAwF5B,aAAa;EAAA;EAChH,OAAO6B,IAAI,kBAD8ErH,EAAE,CAAAsH,iBAAA;IAAAC,IAAA,EACJ/B,aAAa;IAAAgC,SAAA;IAAAC,SAAA;IAAAC,QAAA;IAAAC,YAAA,WAAAC,2BAAAvE,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QADXrD,EAAE,CAAA6H,UAAA,mBAAAC,uCAAAC,MAAA;UAAA,OACJzE,GAAA,CAAAqD,YAAA,CAAAoB,MAAmB,CAAC;QAAA,CAAR,CAAC,qBAAAC,yCAAAD,MAAA;UAAA,OAAbzE,GAAA,CAAAyD,cAAA,CAAAgB,MAAqB,CAAC;QAAA,CAAV,CAAC;MAAA;MAAA,IAAA1E,EAAA;QADXrD,EAAE,CAAAiI,WAAA,aACJ3E,GAAA,CAAA6C,YAAA,CAAa,CAAC,cAAd7C,GAAA,CAAA4C,qBAAA,CAAsB,CAAC,mBAAA5C,GAAA,CAAAuC,QAAA;QADrB7F,EAAE,CAAAkI,WAAA,wCAAA5E,GAAA,CAAAsC,UACQ,CAAC,gDAAAtC,GAAA,CAAAqC,aAAD,CAAC,0CAAArC,GAAA,CAAAsC,UAAD,CAAC;MAAA;IAAA;IAAAuC,MAAA;MAAAxC,aAAA;MAAAE,QAAA,8BAAgIzF,gBAAgB;MAAA4F,QAAA,8BAAuCD,KAAK,IAAMA,KAAK,IAAI,IAAI,GAAG,CAAC,CAAC,GAAG1F,eAAe,CAAC0F,KAAK,CAAE;MAAAE,uBAAA;IAAA;EAAA;AACtV;AACA;EAAA,QAAAmC,SAAA,oBAAAA,SAAA,KAH6FpI,EAAE,CAAAqI,iBAAA,CAGJ7C,aAAa,EAAc,CAAC;IAC3G+B,IAAI,EAAEjH,SAAS;IACfgI,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,iBAAiB;MAC3BC,IAAI,EAAE;QACF,OAAO,EAAE,gDAAgD;QACzD,6CAA6C,EAAE,YAAY;QAC3D,oDAAoD,EAAE,gBAAgB;QACtE,8CAA8C,EAAE,aAAa;QAC7D,iBAAiB,EAAE,gBAAgB;QACnC,iBAAiB,EAAE,yBAAyB;QAC5C,sBAAsB,EAAE,UAAU;QAClC,SAAS,EAAE,sBAAsB;QACjC,WAAW,EAAE;MACjB;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAE7C,aAAa,EAAE,CAAC;MACxD4B,IAAI,EAAEhH;IACV,CAAC,CAAC;IAAEsF,QAAQ,EAAE,CAAC;MACX0B,IAAI,EAAEhH,KAAK;MACX+H,IAAI,EAAE,CAAC;QAAEG,SAAS,EAAErI;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE4F,QAAQ,EAAE,CAAC;MACXuB,IAAI,EAAEhH,KAAK;MACX+H,IAAI,EAAE,CAAC;QACCG,SAAS,EAAG1C,KAAK,IAAMA,KAAK,IAAI,IAAI,GAAG,CAAC,CAAC,GAAG1F,eAAe,CAAC0F,KAAK;MACrE,CAAC;IACT,CAAC,CAAC;IAAEE,uBAAuB,EAAE,CAAC;MAC1BsB,IAAI,EAAEhH;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,MAAMmI,aAAa,CAAC;EAChB,OAAOxB,IAAI,YAAAyB,sBAAAvB,iBAAA;IAAA,YAAAA,iBAAA,IAAwFsB,aAAa;EAAA;EAChH,OAAOrB,IAAI,kBApC8ErH,EAAE,CAAAsH,iBAAA;IAAAC,IAAA,EAoCJmB,aAAa;IAAAlB,SAAA;IAAAC,SAAA,WAAkG,KAAK;IAAAmB,QAAA,GApClH5I,EAAE,CAAA6I,kBAAA,CAoCmO,CAAC;MAAEC,OAAO,EAAE1D,eAAe;MAAE2D,WAAW,EAAEL;IAAc,CAAC,CAAC;EAAA;AAC5X;AACA;EAAA,QAAAN,SAAA,oBAAAA,SAAA,KAtC6FpI,EAAE,CAAAqI,iBAAA,CAsCJK,aAAa,EAAc,CAAC;IAC3GnB,IAAI,EAAEjH,SAAS;IACfgI,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,kCAAkC;MAC5CC,IAAI,EAAE;QACF,OAAO,EAAE,gFAAgF;QACzF,MAAM,EAAE;MACZ,CAAC;MACDQ,SAAS,EAAE,CAAC;QAAEF,OAAO,EAAE1D,eAAe;QAAE2D,WAAW,EAAEL;MAAc,CAAC;IACxE,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAMO,mBAAmB,SAASzD,aAAa,CAAC;EAC5C;AACJ;AACA;AACA;EACIG,aAAa,GAAG,KAAK;EACrBC,UAAU,GAAG,KAAK;EAClB,OAAOsB,IAAI;IAAA,IAAAgC,gCAAA;IAAA,gBAAAC,4BAAA/B,iBAAA;MAAA,QAAA8B,gCAAA,KAAAA,gCAAA,GAzD8ElJ,EAAE,CAAAoJ,qBAAA,CAyDQH,mBAAmB,IAAA7B,iBAAA,IAAnB6B,mBAAmB;IAAA;EAAA;EACtH,OAAO5B,IAAI,kBA1D8ErH,EAAE,CAAAsH,iBAAA;IAAAC,IAAA,EA0DJ0B,mBAAmB;IAAAzB,SAAA;IAAAC,SAAA,kBAAsH,MAAM;IAAAmB,QAAA,GA1D7I5I,EAAE,CAAA6I,kBAAA,CA0DsQ,CAAC;MAAEC,OAAO,EAAEzD,sBAAsB;MAAE0D,WAAW,EAAEE;IAAoB,CAAC,CAAC,GA1D/UjJ,EAAE,CAAAqJ,0BAAA;EAAA;AA2D/F;AACA;EAAA,QAAAjB,SAAA,oBAAAA,SAAA,KA5D6FpI,EAAE,CAAAqI,iBAAA,CA4DJY,mBAAmB,EAAc,CAAC;IACjH1B,IAAI,EAAEjH,SAAS;IACfgI,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,+CAA+C;MACzDC,IAAI,EAAE;QACF,OAAO,EAAE,wFAAwF;QACjG,aAAa,EAAE;MACnB,CAAC;MACDQ,SAAS,EAAE,CAAC;QAAEF,OAAO,EAAEzD,sBAAsB;QAAE0D,WAAW,EAAEE;MAAoB,CAAC;IACrF,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMK,aAAa,SAAS9D,aAAa,CAAC;EACtCI,UAAU,GAAG,KAAK;EAClBe,YAAYA,CAACC,KAAK,EAAE;IAChB,IAAI,CAAC,IAAI,CAACf,QAAQ,EAAE;MAChBe,KAAK,CAAC2C,eAAe,CAAC,CAAC;MACvB3C,KAAK,CAACC,cAAc,CAAC,CAAC;MACtB,IAAI,CAACnB,WAAW,CAAC8D,MAAM,CAAC,CAAC;IAC7B;EACJ;EACAzC,cAAcA,CAACH,KAAK,EAAE;IAClB,IAAI,CAACA,KAAK,CAACI,OAAO,KAAK3H,KAAK,IAAIuH,KAAK,CAACI,OAAO,KAAK1H,KAAK,KAAK,CAAC,IAAI,CAACuG,QAAQ,EAAE;MACxEe,KAAK,CAAC2C,eAAe,CAAC,CAAC;MACvB3C,KAAK,CAACC,cAAc,CAAC,CAAC;MACtB,IAAI,CAACnB,WAAW,CAAC8D,MAAM,CAAC,CAAC;IAC7B;EACJ;EACA,OAAOtC,IAAI;IAAA,IAAAuC,0BAAA;IAAA,gBAAAC,sBAAAtC,iBAAA;MAAA,QAAAqC,0BAAA,KAAAA,0BAAA,GAtG8EzJ,EAAE,CAAAoJ,qBAAA,CAsGQE,aAAa,IAAAlC,iBAAA,IAAbkC,aAAa;IAAA;EAAA;EAChH,OAAOjC,IAAI,kBAvG8ErH,EAAE,CAAAsH,iBAAA;IAAAC,IAAA,EAuGJ+B,aAAa;IAAA9B,SAAA;IAAAC,SAAA,WAAiF,QAAQ;IAAAC,QAAA;IAAAC,YAAA,WAAAgC,2BAAAtG,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAvGpGrD,EAAE,CAAAiI,WAAA,gBAuGJ,IAAI;MAAA;IAAA;IAAAW,QAAA,GAvGF5I,EAAE,CAAA6I,kBAAA,CAuGiT,CAAC;MAAEC,OAAO,EAAExD,eAAe;MAAEyD,WAAW,EAAEO;IAAc,CAAC,CAAC,GAvG7WtJ,EAAE,CAAAqJ,0BAAA;EAAA;AAwG/F;AACA;EAAA,QAAAjB,SAAA,oBAAAA,SAAA,KAzG6FpI,EAAE,CAAAqI,iBAAA,CAyGJiB,aAAa,EAAc,CAAC;IAC3G/B,IAAI,EAAEjH,SAAS;IACfgI,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,iBAAiB;MAC3BC,IAAI,EAAE;QACF,OAAO,EAAE,qEAAqE,GAC1E,6DAA6D;QACjE,MAAM,EAAE,QAAQ;QAChB,oBAAoB,EAAE;MAC1B,CAAC;MACDQ,SAAS,EAAE,CAAC;QAAEF,OAAO,EAAExD,eAAe;QAAEyD,WAAW,EAAEO;MAAc,CAAC;IACxE,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA,MAAMM,OAAO,CAAC;EACVC,kBAAkB,GAAG3J,MAAM,CAACM,iBAAiB,CAAC;EAC9CiF,WAAW,GAAGvF,MAAM,CAACC,UAAU,CAAC;EAChC2J,OAAO,GAAG5J,MAAM,CAACO,MAAM,CAAC;EACxBsJ,aAAa,GAAG7J,MAAM,CAAChB,YAAY,CAAC;EACpC8K,oBAAoB,GAAG9J,MAAM,CAAC2B,yBAAyB,EAAE;IACrDoI,QAAQ,EAAE;EACd,CAAC,CAAC;EACFC,SAAS,GAAGhK,MAAM,CAACH,QAAQ,CAAC;EAC5B;EACAoK,QAAQ,GAAG,IAAI3I,OAAO,CAAC,CAAC;EACxB;EACA4I,OAAO,GAAG,IAAI5I,OAAO,CAAC,CAAC;EACvB;EACA6I,YAAY;EACZ;EACAC,IAAI,GAAG,IAAI;EACX;EACAC,iBAAiB,GAAG,KAAK;EACzB;EACAC,aAAa;EACb;EACAC,cAAc;EACd;EACAC,mBAAmB;EACnB;EACAC,gBAAgB;EAChB;EACAC,iBAAiB;EACjB;EACAC,eAAe;EACfC,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAACP,iBAAiB;EACjC;EACA;EACAQ,EAAE,GAAG7K,MAAM,CAACf,YAAY,CAAC,CAAC6L,KAAK,CAAC,eAAe,CAAC;EAChD;EACA;EACA;EACA;EACAC,SAAS,GAAG,IAAI;EAChB;EACA;EACA;EACA;EACAC,eAAe,GAAG,IAAI;EACtB;EACAC,kBAAkB,GAAG,GAAG,IAAI,CAACJ,EAAE,mBAAmB;EAClD;EACAK,iBAAiB,GAAG,KAAK;EACzBC,YAAY;EACZ;AACJ;AACA;AACA;EACI,IAAItF,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACuF,MAAM,KAAKC,SAAS,GAAG,IAAI,CAACD,MAAM,GAAG,IAAI,CAACD,YAAY,CAACG,WAAW,CAACC,IAAI,CAAC,CAAC;EACzF;EACA,IAAI1F,KAAKA,CAACA,KAAK,EAAE;IACb,IAAI,CAACuF,MAAM,GAAGvF,KAAK;EACvB;EACAuF,MAAM;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACII,KAAK;EACL;AACJ;AACA;EACIC,SAAS,GAAG,IAAI;EAChB;AACJ;AACA;EACIC,WAAW,GAAG,KAAK;EACnB;EACAC,aAAa,GAAG,KAAK;EACrB;EACA,IAAIhG,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS,IAAI,IAAI,CAACsF,iBAAiB;EACnD;EACA,IAAIvF,QAAQA,CAACE,KAAK,EAAE;IAChB,IAAI,CAACD,SAAS,GAAGC,KAAK;EAC1B;EACAD,SAAS,GAAG,KAAK;EACjB;EACAgG,OAAO,GAAG,IAAIpL,YAAY,CAAC,CAAC;EAC5B;EACAqL,SAAS,GAAG,IAAIrL,YAAY,CAAC,CAAC;EAC9B;EACAsL,iBAAiB,GAAG,gBAAgB;EACpC;EACAC,WAAW;EACX;EACAC,YAAY;EACZ;EACAC,UAAU;EACV;EACAC,aAAa;EACb;AACJ;AACA;AACA;EACIC,aAAa,GAAGnM,MAAM,CAAC6B,eAAe,CAAC;EACvCuK,SAAS,GAAGpM,MAAM,CAACS,QAAQ,CAAC;EAC5B0F,WAAWA,CAAA,EAAG;IACV,MAAMkG,WAAW,GAAGrM,MAAM,CAACL,sBAAsB,CAAC;IAClD0M,WAAW,CAACjG,IAAI,CAAC3E,uBAAuB,CAAC;IACzC4K,WAAW,CAACjG,IAAI,CAACxG,qBAAqB,CAAC;IACvC,MAAM0M,aAAa,GAAGtM,MAAM,CAACU,qBAAqB,EAAE;MAAEqJ,QAAQ,EAAE;IAAK,CAAC,CAAC;IACvE,IAAI,CAACS,mBAAmB,GAAG8B,aAAa,KAAK,gBAAgB;IAC7D,IAAI,CAACC,aAAa,CAAC,CAAC;IACpB,IAAI,CAACJ,aAAa,EAAEK,eAAe,CAAC,IAAI,CAACjH,WAAW,CAACc,aAAa,EAAE;MAChEoG,SAAS,EAAE,qBAAqB;MAChC9G,QAAQ,EAAE,IAAI,CAAC+G,iBAAiB,CAAC;IACrC,CAAC,CAAC;EACN;EACAC,QAAQA,CAAA,EAAG;IACP;IACA;IACA,MAAMC,OAAO,GAAG,IAAI,CAACrH,WAAW,CAACc,aAAa;IAC9C,IAAI,CAAC8D,YAAY,GACbyC,OAAO,CAACC,YAAY,CAAC,IAAI,CAACf,iBAAiB,CAAC,IACxCc,OAAO,CAACE,OAAO,CAACC,WAAW,CAAC,CAAC,KAAK,IAAI,CAACjB,iBAAiB;EACpE;EACAkB,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC7B,YAAY,GAAG,IAAI,CAAC5F,WAAW,CAACc,aAAa,CAAC4G,aAAa,CAAC,4BAA4B,CAAC;IAC9F,IAAI,IAAI,CAAC3C,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,GAAG,KAAK;MAC1B,IAAI,CAAC9D,KAAK,CAAC,CAAC;IAChB;EACJ;EACA0G,kBAAkBA,CAAA,EAAG;IACjB;IACA;IACA,IAAI,CAAC3C,cAAc,GAAGhJ,KAAK,CAAC,IAAI,CAACkJ,gBAAgB,CAAC0C,OAAO,EAAE,IAAI,CAACzC,iBAAiB,CAACyC,OAAO,EAAE,IAAI,CAACxC,eAAe,CAACwC,OAAO,CAAC,CAACC,SAAS,CAAC,MAAM,IAAI,CAACzD,kBAAkB,CAAC0D,YAAY,CAAC,CAAC,CAAC;EACpL;EACAC,SAASA,CAAA,EAAG;IACR,IAAI,CAACnB,aAAa,CAACoB,WAAW,CAAC,IAAI,CAAChI,WAAW,CAACc,aAAa,EAAE,IAAI,CAACqG,iBAAiB,CAAC,CAAC,CAAC;EAC5F;EACAc,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC3D,aAAa,CAAC4D,cAAc,CAAC,IAAI,CAAClI,WAAW,CAAC;IACnD,IAAI,CAAC4G,aAAa,EAAEuB,aAAa,CAAC,IAAI,CAACnI,WAAW,CAACc,aAAa,CAAC;IACjE,IAAI,CAACkE,cAAc,EAAEoD,WAAW,CAAC,CAAC;IAClC,IAAI,CAAC9B,SAAS,CAAC+B,IAAI,CAAC;MAAEC,IAAI,EAAE;IAAK,CAAC,CAAC;IACnC,IAAI,CAAChC,SAAS,CAACiC,QAAQ,CAAC,CAAC;EAC7B;EACA;AACJ;AACA;AACA;AACA;EACIxE,MAAMA,CAAA,EAAG;IACL,IAAI,IAAI,CAACmC,SAAS,EAAE;MAChB,IAAI,CAACG,OAAO,CAACgC,IAAI,CAAC;QAAEC,IAAI,EAAE;MAAK,CAAC,CAAC;IACrC;EACJ;EACA;EACAnB,iBAAiBA,CAAA,EAAG;IAChB,OAAQ,IAAI,CAAC/G,QAAQ,IACjB,IAAI,CAACgG,aAAa,IAClB,IAAI,CAACnB,mBAAmB,IACxB,IAAI,CAACL,YAAY,IACjB,CAAC,CAAC,IAAI,CAACL,oBAAoB,EAAEnE,QAAQ;EAC7C;EACA;EACAoI,gBAAgBA,CAAA,EAAG;IACf,OAAO,CAAC,EAAE,IAAI,CAAC/B,YAAY,IAAI,IAAI,CAACC,UAAU,CAAC;EACnD;EACA;EACApF,cAAcA,CAACH,KAAK,EAAE;IAClB;IACA;IACA,IAAKA,KAAK,CAACI,OAAO,KAAKzH,SAAS,IAAI,CAACqH,KAAK,CAACsH,MAAM,IAAKtH,KAAK,CAACI,OAAO,KAAKxH,MAAM,EAAE;MAC5EoH,KAAK,CAACC,cAAc,CAAC,CAAC;MACtB,IAAI,CAAC2C,MAAM,CAAC,CAAC;IACjB;EACJ;EACA;EACA9C,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC,IAAI,CAACb,QAAQ,EAAE;MAChB;MACA;MACA;MACA,IAAI,IAAI,CAACuG,aAAa,EAAE;QACpB,IAAI,CAACA,aAAa,CAAC1F,KAAK,CAAC,CAAC;MAC9B,CAAC,MACI;QACD,IAAI,CAAC8D,aAAa,GAAG,IAAI;MAC7B;IACJ;EACJ;EACA;EACA2D,gBAAgBA,CAACC,MAAM,EAAE;IACrB,OAAO,IAAI,CAACC,WAAW,CAAC,CAAC,CAACC,IAAI,CAACC,MAAM,IAAI;MACrC,MAAMzB,OAAO,GAAGyB,MAAM,CAAC9I,WAAW,CAACc,aAAa;MAChD,OAAOuG,OAAO,KAAKsB,MAAM,IAAItB,OAAO,CAAC0B,QAAQ,CAACJ,MAAM,CAAC;IACzD,CAAC,CAAC;EACN;EACA;EACAC,WAAWA,CAAA,EAAG;IACV,MAAMI,MAAM,GAAG,EAAE;IACjB,IAAI,IAAI,CAACrC,aAAa,EAAE;MACpBqC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACtC,aAAa,CAAC;IACnC;IACA,IAAI,IAAI,CAACD,UAAU,EAAE;MACjBsC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACvC,UAAU,CAAC;IAChC;IACA,IAAI,IAAI,CAACD,YAAY,EAAE;MACnBuC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACxC,YAAY,CAAC;IAClC;IACA,OAAOuC,MAAM;EACjB;EACA;EACA3H,+BAA+BA,CAAA,EAAG;IAC9B;EAAA;EAEJ;EACA2F,aAAaA,CAAA,EAAG;IACZ,IAAI,CAAC1C,aAAa,CAAC4E,OAAO,CAAC,IAAI,CAAClJ,WAAW,EAAE,IAAI,CAAC,CAAC6H,SAAS,CAACsB,MAAM,IAAI;MACnE,MAAMC,QAAQ,GAAGD,MAAM,KAAK,IAAI;MAChC,IAAIC,QAAQ,KAAK,IAAI,CAACtE,iBAAiB,EAAE;QACrC,IAAI,CAACA,iBAAiB,GAAGsE,QAAQ;QACjC,IAAIA,QAAQ,EAAE;UACV,IAAI,CAAC1E,QAAQ,CAAC2E,IAAI,CAAC;YAAEf,IAAI,EAAE;UAAK,CAAC,CAAC;QACtC,CAAC,MACI;UACD;UACA;UACA;UACA;UACA,IAAI,CAAClE,kBAAkB,CAAC0D,YAAY,CAAC,CAAC;UACtCwB,UAAU,CAAC,MAAM,IAAI,CAACjF,OAAO,CAACkF,GAAG,CAAC,MAAM,IAAI,CAAC5E,OAAO,CAAC0E,IAAI,CAAC;YAAEf,IAAI,EAAE;UAAK,CAAC,CAAC,CAAC,CAAC;QAC/E;MACJ;IACJ,CAAC,CAAC;EACN;EACA,OAAO7G,IAAI,YAAA+H,gBAAA7H,iBAAA;IAAA,YAAAA,iBAAA,IAAwFwC,OAAO;EAAA;EAC1G,OAAOsF,IAAI,kBA9W8ElP,EAAE,CAAAmP,iBAAA;IAAA5H,IAAA,EA8WJqC,OAAO;IAAApC,SAAA;IAAA4H,cAAA,WAAAC,uBAAAhM,EAAA,EAAAC,GAAA,EAAAgM,QAAA;MAAA,IAAAjM,EAAA;QA9WLrD,EAAE,CAAAuP,cAAA,CAAAD,QAAA,EA8WqkDlK,eAAe;QA9WtlDpF,EAAE,CAAAuP,cAAA,CAAAD,QAAA,EA8WmqDjK,sBAAsB;QA9W3rDrF,EAAE,CAAAuP,cAAA,CAAAD,QAAA,EA8WswDhK,eAAe;QA9WvxDtF,EAAE,CAAAuP,cAAA,CAAAD,QAAA,EA8W21DlK,eAAe;QA9W52DpF,EAAE,CAAAuP,cAAA,CAAAD,QAAA,EA8Wi7DjK,sBAAsB;QA9Wz8DrF,EAAE,CAAAuP,cAAA,CAAAD,QAAA,EA8W4gEhK,eAAe;MAAA;MAAA,IAAAjC,EAAA;QAAA,IAAAmM,EAAA;QA9W7hExP,EAAE,CAAAyP,cAAA,CAAAD,EAAA,GAAFxP,EAAE,CAAA0P,WAAA,QAAApM,GAAA,CAAA2I,WAAA,GAAAuD,EAAA,CAAAG,KAAA;QAAF3P,EAAE,CAAAyP,cAAA,CAAAD,EAAA,GAAFxP,EAAE,CAAA0P,WAAA,QAAApM,GAAA,CAAA4I,YAAA,GAAAsD,EAAA,CAAAG,KAAA;QAAF3P,EAAE,CAAAyP,cAAA,CAAAD,EAAA,GAAFxP,EAAE,CAAA0P,WAAA,QAAApM,GAAA,CAAA6I,UAAA,GAAAqD,EAAA,CAAAG,KAAA;QAAF3P,EAAE,CAAAyP,cAAA,CAAAD,EAAA,GAAFxP,EAAE,CAAA0P,WAAA,QAAApM,GAAA,CAAAqH,gBAAA,GAAA6E,EAAA;QAAFxP,EAAE,CAAAyP,cAAA,CAAAD,EAAA,GAAFxP,EAAE,CAAA0P,WAAA,QAAApM,GAAA,CAAAsH,iBAAA,GAAA4E,EAAA;QAAFxP,EAAE,CAAAyP,cAAA,CAAAD,EAAA,GAAFxP,EAAE,CAAA0P,WAAA,QAAApM,GAAA,CAAAuH,eAAA,GAAA2E,EAAA;MAAA;IAAA;IAAAI,SAAA,WAAAC,cAAAxM,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFrD,EAAE,CAAA8P,WAAA,CA8W0nEtK,aAAa;MAAA;MAAA,IAAAnC,EAAA;QAAA,IAAAmM,EAAA;QA9WzoExP,EAAE,CAAAyP,cAAA,CAAAD,EAAA,GAAFxP,EAAE,CAAA0P,WAAA,QAAApM,GAAA,CAAA8I,aAAA,GAAAoD,EAAA,CAAAG,KAAA;MAAA;IAAA;IAAAlI,SAAA;IAAAC,QAAA;IAAAC,YAAA,WAAAoI,qBAAA1M,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFrD,EAAE,CAAA6H,UAAA,qBAAAmI,mCAAAjI,MAAA;UAAA,OA8WJzE,GAAA,CAAAyD,cAAA,CAAAgB,MAAqB,CAAC;QAAA,CAAhB,CAAC;MAAA;MAAA,IAAA1E,EAAA;QA9WLrD,EAAE,CAAAiQ,cAAA,OAAA3M,GAAA,CAAAyH,EA8WE,CAAC;QA9WL/K,EAAE,CAAAiI,WAAA,SAAA3E,GAAA,CAAAgH,IAAA,gBAAAhH,GAAA,CAAA2H,SAAA;QAAFjL,EAAE,CAAAkQ,UAAA,CA8WJ,MAAM,IAAA5M,GAAA,CAAAoI,KAAA,IAAa,SAAS,CAAtB,CAAC;QA9WL1L,EAAE,CAAAkI,WAAA,wBAAA5E,GAAA,CAAA+G,YA8WE,CAAC,iCAAA/G,GAAA,CAAAuC,QAAD,CAAC,6CAAPvC,GAAA,CAAA2K,gBAAA,CAAiB,CAAX,CAAC,6CAAA3K,GAAA,CAAA2I,WAAD,CAAC,0CAAA3I,GAAA,CAAA2I,WAAD,CAAC,oCAAA3I,GAAA,CAAA2I,WAAD,CAAC,6BAAA3I,GAAA,CAAA2I,WAAD,CAAC,6BAAA3I,GAAA,CAAAsI,WAAD,CAAC,0BAAAtI,GAAA,CAAAuC,QAAD,CAAC,uBAAAvC,GAAA,CAAA+G,YAAD,CAAC,2BAAA/G,GAAA,CAAA+G,YAAD,CAAC,oCAAP/G,GAAA,CAAA2K,gBAAA,CAAiB,CAAX,CAAC,4BAAA3K,GAAA,CAAAoH,mBAAD,CAAC;MAAA;IAAA;IAAAvC,MAAA;MAAAmC,IAAA;MAAAS,EAAA;MAAAE,SAAA;MAAAC,eAAA;MAAAnF,KAAA;MAAA2F,KAAA;MAAAC,SAAA,gCAAoSvL,gBAAgB;MAAAwL,WAAA,oCAA+CxL,gBAAgB;MAAAyL,aAAA,wCAAqDzL,gBAAgB;MAAAyF,QAAA,8BAAsCzF,gBAAgB;IAAA;IAAA+P,OAAA;MAAArE,OAAA;MAAAC,SAAA;IAAA;IAAAqE,QAAA;IAAAxH,QAAA,GA9Wnf5I,EAAE,CAAA6I,kBAAA,CA8Wq9C,CAAC;MAAEC,OAAO,EAAEvD,QAAQ;MAAEwD,WAAW,EAAEa;IAAQ,CAAC,CAAC;IAAAyG,kBAAA,EAAAlN,GAAA;IAAAmN,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,iBAAArN,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QA9WpgDrD,EAAE,CAAA2Q,eAAA,CAAAzN,GAAA;QAAFlD,EAAE,CAAA6D,SAAA,aA8WgwE,CAAC;QA9WnwE7D,EAAE,CAAAuD,cAAA,aA8W+0E,CAAC,aAAiD,CAAC;QA9Wp4EvD,EAAE,CAAAuE,UAAA,IAAAnB,8BAAA,iBA8W05E,CAAC;QA9W75EpD,EAAE,CAAAuD,cAAA,aA8WupF,CAAC;QA9W1pFvD,EAAE,CAAAwD,YAAA,EA8WwrF,CAAC;QA9W3rFxD,EAAE,CAAA6D,SAAA,aA8WgxF,CAAC;QA9WnxF7D,EAAE,CAAAyD,YAAA,CA8W6xF,CAAC,CAAU,CAAC,CAAQ,CAAC;QA9WpzFzD,EAAE,CAAAuE,UAAA,IAAAb,8BAAA,iBA8W+0F,CAAC;MAAA;MAAA,IAAAL,EAAA;QA9Wl1FrD,EAAE,CAAA4Q,SAAA,EA8Wg4E,CAAC;QA9Wn4E5Q,EAAE,CAAA6Q,UAAA,uBA8Wg4E,CAAC;QA9Wn4E7Q,EAAE,CAAA4Q,SAAA,CA8WwkF,CAAC;QA9W3kF5Q,EAAE,CAAA0E,aAAA,CAAApB,GAAA,CAAA2I,WAAA,SA8WwkF,CAAC;QA9W3kFjM,EAAE,CAAA4Q,SAAA,EA8WohG,CAAC;QA9WvhG5Q,EAAE,CAAA0E,aAAA,CAAApB,GAAA,CAAA2K,gBAAA,WA8WohG,CAAC;MAAA;IAAA;IAAA6C,YAAA,GAA4/hBtL,aAAa;IAAAuL,MAAA;IAAAC,aAAA;IAAAC,eAAA;EAAA;AAC7noB;AACA;EAAA,QAAA7I,SAAA,oBAAAA,SAAA,KAhX6FpI,EAAE,CAAAqI,iBAAA,CAgXJuB,OAAO,EAAc,CAAC;IACrGrC,IAAI,EAAE1G,SAAS;IACfyH,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,wDAAwD;MAAE6H,QAAQ,EAAE,SAAS;MAAE5H,IAAI,EAAE;QAC5F,OAAO,EAAE,cAAc;QACvB,SAAS,EAAE,+BAA+B;QAC1C,4BAA4B,EAAE,eAAe;QAC7C,sCAAsC,EAAE,UAAU;QAClD,kDAAkD,EAAE,oBAAoB;QACxE,kDAAkD,EAAE,aAAa;QACjE,+CAA+C,EAAE,aAAa;QAC9D,yCAAyC,EAAE,aAAa;QACxD,kCAAkC,EAAE,aAAa;QACjD,kCAAkC,EAAE,aAAa;QACjD,+BAA+B,EAAE,UAAU;QAC3C,4BAA4B,EAAE,cAAc;QAC5C,+BAA+B,EAAE,eAAe;QAChD,yCAAyC,EAAE,oBAAoB;QAC/D,iCAAiC,EAAE,qBAAqB;QACxD,MAAM,EAAE,IAAI;QACZ,aAAa,EAAE,MAAM;QACrB,mBAAmB,EAAE,WAAW;QAChC,WAAW,EAAE;MACjB,CAAC;MAAEwI,aAAa,EAAElQ,iBAAiB,CAACoQ,IAAI;MAAED,eAAe,EAAElQ,uBAAuB,CAACoQ,MAAM;MAAEnI,SAAS,EAAE,CAAC;QAAEF,OAAO,EAAEvD,QAAQ;QAAEwD,WAAW,EAAEa;MAAQ,CAAC,CAAC;MAAEwH,OAAO,EAAE,CAAC5L,aAAa,CAAC;MAAEiL,QAAQ,EAAE,00BAA00B;MAAEM,MAAM,EAAE,CAAC,i8hBAAi8hB;IAAE,CAAC;EAC99jB,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEzG,IAAI,EAAE,CAAC;MAC/C/C,IAAI,EAAEhH;IACV,CAAC,CAAC;IAAEoK,gBAAgB,EAAE,CAAC;MACnBpD,IAAI,EAAEvG,eAAe;MACrBsH,IAAI,EAAE,CAAClD,eAAe,EAAE;QAAEiM,WAAW,EAAE;MAAK,CAAC;IACjD,CAAC,CAAC;IAAEzG,iBAAiB,EAAE,CAAC;MACpBrD,IAAI,EAAEvG,eAAe;MACrBsH,IAAI,EAAE,CAACjD,sBAAsB,EAAE;QAAEgM,WAAW,EAAE;MAAK,CAAC;IACxD,CAAC,CAAC;IAAExG,eAAe,EAAE,CAAC;MAClBtD,IAAI,EAAEvG,eAAe;MACrBsH,IAAI,EAAE,CAAChD,eAAe,EAAE;QAAE+L,WAAW,EAAE;MAAK,CAAC;IACjD,CAAC,CAAC;IAAEtG,EAAE,EAAE,CAAC;MACLxD,IAAI,EAAEhH;IACV,CAAC,CAAC;IAAE0K,SAAS,EAAE,CAAC;MACZ1D,IAAI,EAAEhH,KAAK;MACX+H,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAE4C,eAAe,EAAE,CAAC;MAClB3D,IAAI,EAAEhH,KAAK;MACX+H,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC,CAAC;IAAEvC,KAAK,EAAE,CAAC;MACRwB,IAAI,EAAEhH;IACV,CAAC,CAAC;IAAEmL,KAAK,EAAE,CAAC;MACRnE,IAAI,EAAEhH;IACV,CAAC,CAAC;IAAEoL,SAAS,EAAE,CAAC;MACZpE,IAAI,EAAEhH,KAAK;MACX+H,IAAI,EAAE,CAAC;QAAEG,SAAS,EAAErI;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEwL,WAAW,EAAE,CAAC;MACdrE,IAAI,EAAEhH,KAAK;MACX+H,IAAI,EAAE,CAAC;QAAEG,SAAS,EAAErI;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEyL,aAAa,EAAE,CAAC;MAChBtE,IAAI,EAAEhH,KAAK;MACX+H,IAAI,EAAE,CAAC;QAAEG,SAAS,EAAErI;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEyF,QAAQ,EAAE,CAAC;MACX0B,IAAI,EAAEhH,KAAK;MACX+H,IAAI,EAAE,CAAC;QAAEG,SAAS,EAAErI;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE0L,OAAO,EAAE,CAAC;MACVvE,IAAI,EAAEtG;IACV,CAAC,CAAC;IAAE8K,SAAS,EAAE,CAAC;MACZxE,IAAI,EAAEtG;IACV,CAAC,CAAC;IAAEgL,WAAW,EAAE,CAAC;MACd1E,IAAI,EAAErG,YAAY;MAClBoH,IAAI,EAAE,CAAClD,eAAe;IAC1B,CAAC,CAAC;IAAE8G,YAAY,EAAE,CAAC;MACf3E,IAAI,EAAErG,YAAY;MAClBoH,IAAI,EAAE,CAACjD,sBAAsB;IACjC,CAAC,CAAC;IAAE8G,UAAU,EAAE,CAAC;MACb5E,IAAI,EAAErG,YAAY;MAClBoH,IAAI,EAAE,CAAChD,eAAe;IAC1B,CAAC,CAAC;IAAE8G,aAAa,EAAE,CAAC;MAChB7E,IAAI,EAAEpG,SAAS;MACfmH,IAAI,EAAE,CAAC9C,aAAa;IACxB,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,MAAM8L,sBAAsB,CAAC;EACzBC,MAAM;EACNC,QAAQ;EACRC,WAAW;EACXpL,WAAWA,CACX;EACAkL,MAAM,EACN;EACAC,QAAQ,EACR;EACAC,WAAW,GAAG,KAAK,EAAE;IACjB,IAAI,CAACF,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,WAAW,GAAGA,WAAW;EAClC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,SAAS9H,OAAO,CAAC;EAChC;EACA+H,eAAe,GAAGzR,MAAM,CAAC8E,yBAAyB,EAAE;IAAEiF,QAAQ,EAAE;EAAK,CAAC,CAAC;EACvE;EACA2H,kBAAkB,GAAG,IAAI;EACzB;EACAC,iBAAiB,GAAG,KAAK;EACzB;EACAC,qCAAqC,GAAG,IAAI,CAACH,eAAe,EAAEI,4BAA4B,IAAI,KAAK;EACnG;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,IAAIC,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACC,WAAW,IAAI,IAAI,CAACL,kBAAkB;EACtD;EACA,IAAII,UAAUA,CAACjM,KAAK,EAAE;IAClB,IAAI,CAACkM,WAAW,GAAGlM,KAAK;IACxB,IAAI,CAAC8D,kBAAkB,CAAC0D,YAAY,CAAC,CAAC;EAC1C;EACA0E,WAAW,GAAG,IAAI;EAClB;EACA,IAAIT,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACU,SAAS;EACzB;EACA,IAAIV,QAAQA,CAACzL,KAAK,EAAE;IAChB,IAAI,CAACoM,iBAAiB,CAACpM,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC;EAC9C;EACAmM,SAAS,GAAG,KAAK;EACjB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAIE,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACJ,UAAU,GAAG,IAAI,CAACR,QAAQ,CAACpL,QAAQ,CAAC,CAAC,GAAG,IAAI;EAC5D;EACA;EACA4F,iBAAiB,GAAG,uBAAuB;EAC3C;EACAqG,eAAe,GAAG,IAAI3R,YAAY,CAAC,CAAC;EACpCmM,QAAQA,CAAA,EAAG;IACP,KAAK,CAACA,QAAQ,CAAC,CAAC;IAChB,IAAI,CAACvC,IAAI,GAAG,cAAc;EAC9B;EACA;EACAgI,MAAMA,CAAA,EAAG;IACL,IAAI,CAACH,iBAAiB,CAAC,IAAI,EAAE,KAAK,EAAE,IAAI,CAAC;EAC7C;EACA;EACAI,QAAQA,CAAA,EAAG;IACP,IAAI,CAACJ,iBAAiB,CAAC,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC;EAC9C;EACA;EACAK,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACL,iBAAiB,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EAC5C;EACA;EACAM,cAAcA,CAAChB,WAAW,GAAG,KAAK,EAAE;IAChC,IAAI,CAACU,iBAAiB,CAAC,CAAC,IAAI,CAACX,QAAQ,EAAEC,WAAW,EAAE,IAAI,CAAC;IACzD,OAAO,IAAI,CAACD,QAAQ;EACxB;EACA1K,+BAA+BA,CAAA,EAAG;IAC9B,IAAI,CAAC,IAAI,CAACjB,QAAQ,EAAE;MAChB;MACA;MACA;MACA,IAAI,CAACa,KAAK,CAAC,CAAC;MACZ,IAAI,IAAI,CAACsL,UAAU,EAAE;QACjB,IAAI,CAACS,cAAc,CAAC,IAAI,CAAC;MAC7B;IACJ;EACJ;EACAC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAACzG,WAAW,EAAE;MAClB,OAAO,IAAI;IACf;IACA;IACA;IACA;IACA,OAAO,CAAC,IAAI,CAAC6F,qCAAqC,IAAI,IAAI,CAACD,iBAAiB;EAChF;EACAM,iBAAiBA,CAACQ,UAAU,EAAElB,WAAW,EAAEmB,SAAS,EAAE;IAClD,IAAID,UAAU,KAAK,IAAI,CAACnB,QAAQ,EAAE;MAC9B,IAAI,CAACU,SAAS,GAAGS,UAAU;MAC3B,IAAIC,SAAS,EAAE;QACX,IAAI,CAACP,eAAe,CAACvE,IAAI,CAAC;UACtByD,MAAM,EAAE,IAAI;UACZE,WAAW;UACXD,QAAQ,EAAE,IAAI,CAACA;QACnB,CAAC,CAAC;MACN;MACA,IAAI,CAAC3H,kBAAkB,CAAC0D,YAAY,CAAC,CAAC;IAC1C;EACJ;EACA,OAAOrG,IAAI;IAAA,IAAA2L,0BAAA;IAAA,gBAAAC,sBAAA1L,iBAAA;MAAA,QAAAyL,0BAAA,KAAAA,0BAAA,GA7jB8E7S,EAAE,CAAAoJ,qBAAA,CA6jBQsI,aAAa,IAAAtK,iBAAA,IAAbsK,aAAa;IAAA;EAAA;EAChH,OAAOxC,IAAI,kBA9jB8ElP,EAAE,CAAAmP,iBAAA;IAAA5H,IAAA,EA8jBJmK,aAAa;IAAAlK,SAAA;IAAAC,SAAA;IAAAC,QAAA;IAAAC,YAAA,WAAAoL,2BAAA1P,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QA9jBXrD,EAAE,CAAAiQ,cAAA,OAAA3M,GAAA,CAAAyH,EA8jBQ,CAAC;QA9jBX/K,EAAE,CAAAiI,WAAA,aA8jBJ,IAAI,gBAAJ,IAAI,sBAAJ,IAAI,UAAA3E,GAAA,CAAAgH,IAAA;QA9jBFtK,EAAE,CAAAkI,WAAA,wBAAA5E,GAAA,CAAA+G,YA8jBQ,CAAC,gCAAA/G,GAAA,CAAA+G,YAAD,CAAC,oCAAA/G,GAAA,CAAA+G,YAAD,CAAC,0BAAA/G,GAAA,CAAAkO,QAAD,CAAC,0BAAAlO,GAAA,CAAAuO,iBAAD,CAAC,0BAAAvO,GAAA,CAAAuC,QAAD,CAAC,6BAAAvC,GAAA,CAAA2I,WAAD,CAAC,iCAAA3I,GAAA,CAAAuC,QAAD,CAAC,iCAAAvC,GAAA,CAAAkO,QAAD,CAAC,mCAAAlO,GAAA,CAAAoH,mBAAD,CAAC,6CAAbpH,GAAA,CAAA2K,gBAAA,CAAiB,CAAL,CAAC,0CAAA3K,GAAA,CAAA2I,WAAD,CAAC,6CAAb3I,GAAA,CAAAoP,kBAAA,CAAmB,CAAP,CAAC,oCAAApP,GAAA,CAAA2I,WAAD,CAAC,6BAAA3I,GAAA,CAAAsI,WAAD,CAAC,oCAAbtI,GAAA,CAAA2K,gBAAA,CAAiB,CAAL,CAAC;MAAA;IAAA;IAAA9F,MAAA;MAAA6J,UAAA,kCAAyK5R,gBAAgB;MAAAoR,QAAA,8BAAsCpR,gBAAgB;IAAA;IAAA+P,OAAA;MAAAkC,eAAA;IAAA;IAAAzJ,QAAA,GA9jB1P5I,EAAE,CAAA6I,kBAAA,CA8jB82C,CACj8C;MAAEC,OAAO,EAAEc,OAAO;MAAEb,WAAW,EAAE2I;IAAc,CAAC,EAChD;MAAE5I,OAAO,EAAEvD,QAAQ;MAAEwD,WAAW,EAAE2I;IAAc,CAAC,CACpD,GAjkBoF1R,EAAE,CAAAqJ,0BAAA;IAAAgH,kBAAA,EAAAlN,GAAA;IAAAmN,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAuC,uBAAA3P,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFrD,EAAE,CAAA2Q,eAAA,CAAAzN,GAAA;QAAFlD,EAAE,CAAA6D,SAAA,aAikBa,CAAC;QAjkBhB7D,EAAE,CAAAuD,cAAA,aAikB4F,CAAC,eAA+N,CAAC;QAjkB/TvD,EAAE,CAAAuE,UAAA,IAAAZ,oCAAA,iBAikB8V,CAAC;QAjkBjW3D,EAAE,CAAAuD,cAAA,aAikB2gC,CAAC;QAjkB9gCvD,EAAE,CAAAwD,YAAA,EAikB4iC,CAAC;QAjkB/iCxD,EAAE,CAAA6D,SAAA,aAikBooC,CAAC;QAjkBvoC7D,EAAE,CAAAyD,YAAA,CAikBipC,CAAC,CAAY,CAAC,CAAQ,CAAC;QAjkB1qCzD,EAAE,CAAAuE,UAAA,IAAAT,oCAAA,iBAikBqsC,CAAC;QAjkBxsC9D,EAAE,CAAAuD,cAAA,aAikB88C,CAAC;QAjkBj9CvD,EAAE,CAAAiT,MAAA,EAikBi+C,CAAC;QAjkBp+CjT,EAAE,CAAAyD,YAAA,CAikBw+C,CAAC;MAAA;MAAA,IAAAJ,EAAA;QAjkB3+CrD,EAAE,CAAA4Q,SAAA,EAikBkK,CAAC;QAjkBrK5Q,EAAE,CAAA6Q,UAAA,gCAikBkK,CAAC;QAjkBrK7Q,EAAE,CAAAiI,WAAA,kBAAA3E,GAAA,CAAA8O,YAAA,gBAAA9O,GAAA,CAAA2H,SAAA,sBAAA3H,GAAA,CAAA6H,kBAAA;QAAFnL,EAAE,CAAA4Q,SAAA,CAikB47B,CAAC;QAjkB/7B5Q,EAAE,CAAA0E,aAAA,CAAApB,GAAA,CAAAoP,kBAAA,WAikB47B,CAAC;QAjkB/7B1S,EAAE,CAAA4Q,SAAA,EAikB04C,CAAC;QAjkB74C5Q,EAAE,CAAA0E,aAAA,CAAApB,GAAA,CAAA2K,gBAAA,WAikB04C,CAAC;QAjkB74CjO,EAAE,CAAA4Q,SAAA,CAikB68C,CAAC;QAjkBh9C5Q,EAAE,CAAA6Q,UAAA,OAAAvN,GAAA,CAAA6H,kBAikB68C,CAAC;QAjkBh9CnL,EAAE,CAAA4Q,SAAA,CAikBi+C,CAAC;QAjkBp+C5Q,EAAE,CAAAkT,iBAAA,CAAA5P,GAAA,CAAA4H,eAikBi+C,CAAC;MAAA;IAAA;IAAA4F,YAAA,GAAmgiBtL,aAAa;IAAAuL,MAAA,GAAAhN,GAAA;IAAAiN,aAAA;IAAAC,eAAA;EAAA;AACjllB;AACA;EAAA,QAAA7I,SAAA,oBAAAA,SAAA,KAnkB6FpI,EAAE,CAAAqI,iBAAA,CAmkBJqJ,aAAa,EAAc,CAAC;IAC3GnK,IAAI,EAAE1G,SAAS;IACfyH,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,oFAAoF;MAAEC,IAAI,EAAE;QACnG,OAAO,EAAE,kCAAkC;QAC3C,4BAA4B,EAAE,eAAe;QAC7C,oCAAoC,EAAE,eAAe;QACrD,wCAAwC,EAAE,eAAe;QACzD,+BAA+B,EAAE,UAAU;QAC3C,+BAA+B,EAAE,mBAAmB;QACpD,+BAA+B,EAAE,UAAU;QAC3C,kCAAkC,EAAE,aAAa;QACjD,sCAAsC,EAAE,UAAU;QAClD,sCAAsC,EAAE,UAAU;QAClD;QACA;QACA;QACA;QACA,uCAAuC,EAAE,sBAAsB;QAC/D,kDAAkD,EAAE,oBAAoB;QACxE,+CAA+C,EAAE,aAAa;QAC9D,kDAAkD,EAAE,sBAAsB;QAC1E,yCAAyC,EAAE,aAAa;QACxD,kCAAkC,EAAE,aAAa;QACjD,yCAAyC,EAAE,oBAAoB;QAC/D,iBAAiB,EAAE,MAAM;QACzB,mBAAmB,EAAE,MAAM;QAC3B,yBAAyB,EAAE,MAAM;QACjC,aAAa,EAAE,MAAM;QACrB,MAAM,EAAE;MACZ,CAAC;MAAEQ,SAAS,EAAE,CACV;QAAEF,OAAO,EAAEc,OAAO;QAAEb,WAAW,EAAE2I;MAAc,CAAC,EAChD;QAAE5I,OAAO,EAAEvD,QAAQ;QAAEwD,WAAW,EAAE2I;MAAc,CAAC,CACpD;MAAEV,aAAa,EAAElQ,iBAAiB,CAACoQ,IAAI;MAAED,eAAe,EAAElQ,uBAAuB,CAACoQ,MAAM;MAAEC,OAAO,EAAE,CAAC5L,aAAa,CAAC;MAAEiL,QAAQ,EAAE,ihDAAihD;MAAEM,MAAM,EAAE,CAAC,i8hBAAi8hB;IAAE,CAAC;EAC3mlB,CAAC,CAAC,QAAkB;IAAEiB,UAAU,EAAE,CAAC;MAC3BzK,IAAI,EAAEhH,KAAK;MACX+H,IAAI,EAAE,CAAC;QAAEG,SAAS,EAAErI;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEoR,QAAQ,EAAE,CAAC;MACXjK,IAAI,EAAEhH,KAAK;MACX+H,IAAI,EAAE,CAAC;QAAEG,SAAS,EAAErI;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEiS,eAAe,EAAE,CAAC;MAClB9K,IAAI,EAAEtG;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,MAAMkS,gBAAgB,CAAC;EACnB1N,WAAW,GAAGvF,MAAM,CAACC,UAAU,CAAC;EAChC+J,SAAS,GAAGhK,MAAM,CAACH,QAAQ,CAAC;EAC5BsG,WAAWA,CAAA,EAAG,CAAE;EAChB+M,UAAUA,CAACC,YAAY,EAAE;IACrB,IAAI,CAACC,gBAAgB,CAAC,CAAC,CAAC5M,KAAK,CAAC,CAAC;IAC/B,IAAI,CAAC6M,QAAQ,CAACF,YAAY,CAAC;EAC/B;EACAC,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAAC7N,WAAW,CAACc,aAAa;EACzC;EACAgN,QAAQA,CAACxN,KAAK,EAAE;IACZ,IAAI,CAACuN,gBAAgB,CAAC,CAAC,CAAC9H,WAAW,GAAGzF,KAAK;IAC3C,IAAI,CAACyN,uBAAuB,CAAC,CAAC;EAClC;EACAC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACH,gBAAgB,CAAC,CAAC,CAAC9H,WAAW,IAAI,EAAE;EACpD;EACAgI,uBAAuBA,CAAA,EAAG;IACtB,MAAME,KAAK,GAAG,IAAI,CAACxJ,SAAS,CAACyJ,WAAW,CAAC,CAAC;IAC1CD,KAAK,CAACE,kBAAkB,CAAC,IAAI,CAACN,gBAAgB,CAAC,CAAC,CAAC;IACjDI,KAAK,CAACG,QAAQ,CAAC,KAAK,CAAC;IACrB,MAAMC,GAAG,GAAGC,MAAM,CAACC,YAAY,CAAC,CAAC;IACjCF,GAAG,CAACG,eAAe,CAAC,CAAC;IACrBH,GAAG,CAACI,QAAQ,CAACR,KAAK,CAAC;EACvB;EACA,OAAOxM,IAAI,YAAAiN,yBAAA/M,iBAAA;IAAA,YAAAA,iBAAA,IAAwF+L,gBAAgB;EAAA;EACnH,OAAO9L,IAAI,kBA7oB8ErH,EAAE,CAAAsH,iBAAA;IAAAC,IAAA,EA6oBJ4L,gBAAgB;IAAA3L,SAAA;IAAAC,SAAA,WAAwF,SAAS,cAAc,IAAI,qBAAqB,MAAM;EAAA;AACzP;AACA;EAAA,QAAAW,SAAA,oBAAAA,SAAA,KA/oB6FpI,EAAE,CAAAqI,iBAAA,CA+oBJ8K,gBAAgB,EAAc,CAAC;IAC9G5L,IAAI,EAAEjH,SAAS;IACfgI,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,wBAAwB;MAClCC,IAAI,EAAE;QACF,OAAO,EAAE,qBAAqB;QAC9B,MAAM,EAAE,SAAS;QACjB,UAAU,EAAE,IAAI;QAChB,iBAAiB,EAAE;MACvB;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA;AACA;AACA;AACA,MAAM4L,UAAU,SAASxK,OAAO,CAAC;EAC7BoC,iBAAiB,GAAG,oBAAoB;EACxC;AACJ;AACA;AACA;AACA;EACIqI,iBAAiB,GAAG,KAAK;EACzBC,QAAQ,GAAG,KAAK;EAChB;EACAC,MAAM,GAAG,IAAI7T,YAAY,CAAC,CAAC;EAC3B;EACA8T,gBAAgB;EAChB;EACA7P,gBAAgB;EAChBsC,UAAU,GAAG,KAAK;EAClBZ,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP,IAAI,CAACiE,IAAI,GAAG,KAAK;IACjB,IAAI,CAACF,OAAO,CAACqK,IAAI,CAACzS,SAAS,CAAC,IAAI,CAAC+J,SAAS,CAAC,CAAC,CAACuB,SAAS,CAAC,MAAM;MACzD,IAAI,IAAI,CAACrG,UAAU,IAAI,CAAC,IAAI,CAACoN,iBAAiB,EAAE;QAC5C,IAAI,CAACK,aAAa,CAAC,CAAC;MACxB;IACJ,CAAC,CAAC;EACN;EACAzG,gBAAgBA,CAAA,EAAG;IACf;IACA,OAAO,CAAC,IAAI,CAAChH,UAAU,IAAI,KAAK,CAACgH,gBAAgB,CAAC,CAAC;EACvD;EACA;EACA0G,YAAYA,CAAA,EAAG;IACX,IAAI,CAAC,IAAI,CAAC1N,UAAU,IAAI,CAAC,IAAI,CAACpB,QAAQ,EAAE;MACpC,IAAI,CAACa,KAAK,CAAC,CAAC;IAChB;EACJ;EACAK,cAAcA,CAACH,KAAK,EAAE;IAClB,IAAIA,KAAK,CAACI,OAAO,KAAK3H,KAAK,IAAI,CAAC,IAAI,CAACwG,QAAQ,EAAE;MAC3C,IAAI,IAAI,CAACoB,UAAU,EAAE;QACjBL,KAAK,CAACC,cAAc,CAAC,CAAC;QACtB,IAAI,CAAC6N,aAAa,CAAC,CAAC;MACxB,CAAC,MACI,IAAI,IAAI,CAACJ,QAAQ,EAAE;QACpB,IAAI,CAACM,aAAa,CAAChO,KAAK,CAAC;MAC7B;IACJ,CAAC,MACI,IAAI,IAAI,CAACK,UAAU,EAAE;MACtB;MACAL,KAAK,CAAC2C,eAAe,CAAC,CAAC;IAC3B,CAAC,MACI;MACD,KAAK,CAACxC,cAAc,CAACH,KAAK,CAAC;IAC/B;EACJ;EACAiO,kBAAkBA,CAACjO,KAAK,EAAE;IACtB,IAAI,CAAC,IAAI,CAACf,QAAQ,IAAI,IAAI,CAACyO,QAAQ,EAAE;MACjC,IAAI,CAACM,aAAa,CAAChO,KAAK,CAAC;IAC7B;EACJ;EACAgO,aAAaA,CAAChO,KAAK,EAAE;IACjB,IAAI,CAAC,IAAI,CAACwF,aAAa,IAClB,IAAI,CAACD,UAAU,IAAI,IAAI,CAACgC,gBAAgB,CAACvH,KAAK,CAACwH,MAAM,CAAC,KAAK,IAAI,CAACjC,UAAW,EAAE;MAC9E;IACJ;IACA;IACA,MAAMpG,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,IAAI,CAACkB,UAAU,GAAG,IAAI,CAACoN,iBAAiB,GAAG,IAAI;IAC/C;IACAjT,eAAe,CAAC,MAAM;MAClB,IAAI,CAAC0T,aAAa,CAAC,CAAC,CAAC1B,UAAU,CAACrN,KAAK,CAAC;MACtC,IAAI,CAACsO,iBAAiB,GAAG,KAAK;IAClC,CAAC,EAAE;MAAEU,QAAQ,EAAE,IAAI,CAACzI;IAAU,CAAC,CAAC;EACpC;EACAoI,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACzN,UAAU,GAAG,IAAI,CAACoN,iBAAiB,GAAG,KAAK;IAChD,IAAI,CAACE,MAAM,CAACzG,IAAI,CAAC;MAAEC,IAAI,EAAE,IAAI;MAAEhI,KAAK,EAAE,IAAI,CAAC+O,aAAa,CAAC,CAAC,CAACrB,QAAQ,CAAC;IAAE,CAAC,CAAC;IACxE;IACA;IACA,IAAI,IAAI,CAACvJ,SAAS,CAAC8K,aAAa,KAAK,IAAI,CAACF,aAAa,CAAC,CAAC,CAACxB,gBAAgB,CAAC,CAAC,IACxE,IAAI,CAACpJ,SAAS,CAAC8K,aAAa,KAAK,IAAI,CAAC9K,SAAS,CAAC+K,IAAI,EAAE;MACtD,IAAI,CAAC7I,aAAa,CAAC1F,KAAK,CAAC,CAAC;IAC9B;EACJ;EACAkG,iBAAiBA,CAAA,EAAG;IAChB,OAAO,KAAK,CAACA,iBAAiB,CAAC,CAAC,IAAI,IAAI,CAAC3F,UAAU;EACvD;EACA;AACJ;AACA;AACA;EACI6N,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACnQ,gBAAgB,IAAI,IAAI,CAAC6P,gBAAgB;EACzD;EACA,OAAOtN,IAAI,YAAAgO,mBAAA9N,iBAAA;IAAA,YAAAA,iBAAA,IAAwFgN,UAAU;EAAA;EAC7G,OAAOlF,IAAI,kBA7vB8ElP,EAAE,CAAAmP,iBAAA;IAAA5H,IAAA,EA6vBJ6M,UAAU;IAAA5M,SAAA;IAAA4H,cAAA,WAAA+F,0BAAA9R,EAAA,EAAAC,GAAA,EAAAgM,QAAA;MAAA,IAAAjM,EAAA;QA7vBRrD,EAAE,CAAAuP,cAAA,CAAAD,QAAA,EAgwBd6D,gBAAgB;MAAA;MAAA,IAAA9P,EAAA;QAAA,IAAAmM,EAAA;QAhwBJxP,EAAE,CAAAyP,cAAA,CAAAD,EAAA,GAAFxP,EAAE,CAAA0P,WAAA,QAAApM,GAAA,CAAAqB,gBAAA,GAAA6K,EAAA,CAAAG,KAAA;MAAA;IAAA;IAAAC,SAAA,WAAAwF,iBAAA/R,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFrD,EAAE,CAAA8P,WAAA,CAgwBoGqD,gBAAgB;MAAA;MAAA,IAAA9P,EAAA;QAAA,IAAAmM,EAAA;QAhwBtHxP,EAAE,CAAAyP,cAAA,CAAAD,EAAA,GAAFxP,EAAE,CAAA0P,WAAA,QAAApM,GAAA,CAAAkR,gBAAA,GAAAhF,EAAA,CAAAG,KAAA;MAAA;IAAA;IAAAlI,SAAA;IAAAC,QAAA;IAAAC,YAAA,WAAA0N,wBAAAhS,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFrD,EAAE,CAAA6H,UAAA,mBAAAyN,oCAAA;UAAA,OA6vBJhS,GAAA,CAAAqR,YAAA,CAAa,CAAC;QAAA,CAAL,CAAC,sBAAAY,uCAAAxN,MAAA;UAAA,OAAVzE,GAAA,CAAAuR,kBAAA,CAAA9M,MAAyB,CAAC;QAAA,CAAjB,CAAC;MAAA;MAAA,IAAA1E,EAAA;QA7vBRrD,EAAE,CAAAiQ,cAAA,OAAA3M,GAAA,CAAAyH,EA6vBK,CAAC;QA7vBR/K,EAAE,CAAAiI,WAAA,aAAA3E,GAAA,CAAAuC,QAAA,GA6vBO,IAAI,IAAI,CAAC,gBAApB,IAAI,sBAAJ,IAAI,UAAAvC,GAAA,CAAAgH,IAAA;QA7vBFtK,EAAE,CAAAkI,WAAA,6BAAA5E,GAAA,CAAA2I,WA6vBK,CAAC,0BAAA3I,GAAA,CAAAuC,QAAD,CAAC,yBAAAvC,GAAA,CAAA2D,UAAD,CAAC,0BAAA3D,GAAA,CAAAgR,QAAD,CAAC,iCAAAhR,GAAA,CAAAuC,QAAD,CAAC,6CAAVvC,GAAA,CAAA2K,gBAAA,CAAiB,CAAR,CAAC,6CAAA3K,GAAA,CAAA2I,WAAD,CAAC,0CAAA3I,GAAA,CAAA2I,WAAD,CAAC,oCAAA3I,GAAA,CAAA2I,WAAD,CAAC,6BAAA3I,GAAA,CAAAsI,WAAD,CAAC,oCAAVtI,GAAA,CAAA2K,gBAAA,CAAiB,CAAR,CAAC;MAAA;IAAA;IAAA9F,MAAA;MAAAmM,QAAA;IAAA;IAAAnE,OAAA;MAAAoE,MAAA;IAAA;IAAA3L,QAAA,GA7vBR5I,EAAE,CAAA6I,kBAAA,CA6vBwkC,CAC3pC;MAAEC,OAAO,EAAEc,OAAO;MAAEb,WAAW,EAAEqL;IAAW,CAAC,EAC7C;MAAEtL,OAAO,EAAEvD,QAAQ;MAAEwD,WAAW,EAAEqL;IAAW,CAAC,CACjD,GAhwBoFpU,EAAE,CAAAqJ,0BAAA;IAAAgH,kBAAA,EAAApM,GAAA;IAAAqM,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAA+E,oBAAAnS,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFrD,EAAE,CAAA2Q,eAAA,CAAA3M,GAAA;QAAFhE,EAAE,CAAAuE,UAAA,IAAAL,iCAAA,iBAgwB8M,CAAC;QAhwBjNlE,EAAE,CAAAuD,cAAA,aAgwBif,CAAC;QAhwBpfvD,EAAE,CAAAuE,UAAA,IAAAJ,iCAAA,iBAgwBwgB,CAAC;QAhwB3gBnE,EAAE,CAAAuD,cAAA,aAgwB6vB,CAAC;QAhwBhwBvD,EAAE,CAAAuE,UAAA,IAAAD,iCAAA,MAgwBqxB,CAAC,IAAAM,iCAAA,MAAiL,CAAC;QAhwB18B5E,EAAE,CAAA6D,SAAA,aAgwB4lC,CAAC;QAhwB/lC7D,EAAE,CAAAyD,YAAA,CAgwBumC,CAAC,CAAQ,CAAC;QAhwBnnCzD,EAAE,CAAAuE,UAAA,IAAAM,iCAAA,iBAgwB8oC,CAAC;QAhwBjpC7E,EAAE,CAAAuD,cAAA,aAgwBm7C,CAAC;QAhwBt7CvD,EAAE,CAAAiT,MAAA,EAgwBs8C,CAAC;QAhwBz8CjT,EAAE,CAAAyD,YAAA,CAgwB68C,CAAC;MAAA;MAAA,IAAAJ,EAAA;QAhwBh9CrD,EAAE,CAAA0E,aAAA,EAAApB,GAAA,CAAA2D,UAAA,SAgwBuQ,CAAC;QAhwB1QjH,EAAE,CAAA4Q,SAAA,CAgwBuZ,CAAC;QAhwB1Z5Q,EAAE,CAAA6Q,UAAA,aAAAvN,GAAA,CAAAuC,QAgwBuZ,CAAC;QAhwB1Z7F,EAAE,CAAAiI,WAAA,eAAA3E,GAAA,CAAA2H,SAAA,sBAAA3H,GAAA,CAAA6H,kBAAA;QAAFnL,EAAE,CAAA4Q,SAAA,CAgwB8qB,CAAC;QAhwBjrB5Q,EAAE,CAAA0E,aAAA,CAAApB,GAAA,CAAA2I,WAAA,SAgwB8qB,CAAC;QAhwBjrBjM,EAAE,CAAA4Q,SAAA,EAgwB++B,CAAC;QAhwBl/B5Q,EAAE,CAAA0E,aAAA,CAAApB,GAAA,CAAA2D,UAAA,QAgwB++B,CAAC;QAhwBl/BjH,EAAE,CAAA4Q,SAAA,EAgwB+2C,CAAC;QAhwBl3C5Q,EAAE,CAAA0E,aAAA,CAAApB,GAAA,CAAA2K,gBAAA,WAgwB+2C,CAAC;QAhwBl3CjO,EAAE,CAAA4Q,SAAA,CAgwBk7C,CAAC;QAhwBr7C5Q,EAAE,CAAA6Q,UAAA,OAAAvN,GAAA,CAAA6H,kBAgwBk7C,CAAC;QAhwBr7CnL,EAAE,CAAA4Q,SAAA,CAgwBs8C,CAAC;QAhwBz8C5Q,EAAE,CAAAkT,iBAAA,CAAA5P,GAAA,CAAA4H,eAgwBs8C,CAAC;MAAA;IAAA;IAAA4F,YAAA,GAAmgiBtL,aAAa,EAA0I2N,gBAAgB;IAAApC,MAAA,GAAAhN,GAAA;IAAAiN,aAAA;IAAAC,eAAA;EAAA;AAChtlB;AACA;EAAA,QAAA7I,SAAA,oBAAAA,SAAA,KAlwB6FpI,EAAE,CAAAqI,iBAAA,CAkwBJ+L,UAAU,EAAc,CAAC;IACxG7M,IAAI,EAAE1G,SAAS;IACfyH,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,wEAAwE;MAAEC,IAAI,EAAE;QACvF,OAAO,EAAE,kDAAkD;QAC3D,kCAAkC,EAAE,aAAa;QACjD,+BAA+B,EAAE,UAAU;QAC3C,8BAA8B,EAAE,YAAY;QAC5C,+BAA+B,EAAE,UAAU;QAC3C,sCAAsC,EAAE,UAAU;QAClD,kDAAkD,EAAE,oBAAoB;QACxE,kDAAkD,EAAE,aAAa;QACjE,+CAA+C,EAAE,aAAa;QAC9D,yCAAyC,EAAE,aAAa;QACxD,kCAAkC,EAAE,aAAa;QACjD,yCAAyC,EAAE,oBAAoB;QAC/D,MAAM,EAAE,IAAI;QACZ;QACA;QACA,iBAAiB,EAAE,sBAAsB;QACzC,mBAAmB,EAAE,MAAM;QAC3B,yBAAyB,EAAE,MAAM;QACjC,aAAa,EAAE,MAAM;QACrB,SAAS,EAAE,gBAAgB;QAC3B,YAAY,EAAE;MAClB,CAAC;MAAEQ,SAAS,EAAE,CACV;QAAEF,OAAO,EAAEc,OAAO;QAAEb,WAAW,EAAEqL;MAAW,CAAC,EAC7C;QAAEtL,OAAO,EAAEvD,QAAQ;QAAEwD,WAAW,EAAEqL;MAAW,CAAC,CACjD;MAAEpD,aAAa,EAAElQ,iBAAiB,CAACoQ,IAAI;MAAED,eAAe,EAAElQ,uBAAuB,CAACoQ,MAAM;MAAEC,OAAO,EAAE,CAAC5L,aAAa,EAAE2N,gBAAgB,CAAC;MAAE1C,QAAQ,EAAE,sxCAAsxC;MAAEM,MAAM,EAAE,CAAC,i8hBAAi8hB;IAAE,CAAC;EACl4kB,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEuD,QAAQ,EAAE,CAAC;MACnD/M,IAAI,EAAEhH;IACV,CAAC,CAAC;IAAEgU,MAAM,EAAE,CAAC;MACThN,IAAI,EAAEtG;IACV,CAAC,CAAC;IAAEuT,gBAAgB,EAAE,CAAC;MACnBjN,IAAI,EAAEpG,SAAS;MACfmH,IAAI,EAAE,CAAC6K,gBAAgB;IAC3B,CAAC,CAAC;IAAExO,gBAAgB,EAAE,CAAC;MACnB4C,IAAI,EAAErG,YAAY;MAClBoH,IAAI,EAAE,CAAC6K,gBAAgB;IAC3B,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA,MAAMsC,UAAU,CAAC;EACbhQ,WAAW,GAAGvF,MAAM,CAACC,UAAU,CAAC;EAChC0J,kBAAkB,GAAG3J,MAAM,CAACM,iBAAiB,CAAC;EAC9CkV,IAAI,GAAGxV,MAAM,CAACiC,cAAc,EAAE;IAAE8H,QAAQ,EAAE;EAAK,CAAC,CAAC;EACjD;EACA0L,8BAA8B,GAAG,IAAI;EACrC;EACAC,WAAW;EACX;EACAC,UAAU,GAAG,IAAIrU,OAAO,CAAC,CAAC;EAC1B;EACAsU,YAAY,GAAG,cAAc;EAC7B;EACA,IAAIC,gBAAgBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACC,cAAc,CAACjI,IAAI,IAAIA,IAAI,CAAC5D,QAAQ,CAAC;EACrD;EACA;EACA,IAAI8L,oBAAoBA,CAAA,EAAG;IACvB,OAAO,IAAI,CAACD,cAAc,CAACjI,IAAI,IAAIA,IAAI,CAAChC,SAAS,CAAC;EACtD;EACA;EACA,IAAImK,kBAAkBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAACF,cAAc,CAACjI,IAAI,IAAIA,IAAI,CAACjC,OAAO,CAAC;EACpD;EACA;EACA,IAAIjG,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAACE,KAAK,EAAE;IAChB,IAAI,CAACD,SAAS,GAAGC,KAAK;IACtB,IAAI,CAACoQ,eAAe,CAAC,CAAC;EAC1B;EACArQ,SAAS,GAAG,KAAK;EACjB;EACA,IAAIsQ,KAAKA,CAAA,EAAG;IACR,OAAO,CAAC,IAAI,CAACC,MAAM,IAAI,IAAI,CAACA,MAAM,CAACC,MAAM,KAAK,CAAC;EACnD;EACA;EACA,IAAIhM,IAAIA,CAAA,EAAG;IACP,IAAI,IAAI,CAACiM,aAAa,EAAE;MACpB,OAAO,IAAI,CAACA,aAAa;IAC7B;IACA,OAAO,IAAI,CAACH,KAAK,GAAG,IAAI,GAAG,IAAI,CAACN,YAAY;EAChD;EACA;EACA9P,QAAQ,GAAG,CAAC;EACZ,IAAIsE,IAAIA,CAACvE,KAAK,EAAE;IACZ,IAAI,CAACwQ,aAAa,GAAGxQ,KAAK;EAC9B;EACAwQ,aAAa,GAAG,IAAI;EACpB;EACA,IAAIC,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,eAAe,CAAC,CAAC;EACjC;EACA;EACAJ,MAAM;EACN;EACAK,YAAY,GAAG,IAAIrV,SAAS,CAAC,CAAC;EAC9BgF,WAAWA,CAAA,EAAG,CAAE;EAChB6G,eAAeA,CAAA,EAAG;IACd,IAAI,CAACyJ,qBAAqB,CAAC,CAAC;IAC5B,IAAI,CAACC,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAACC,0BAA0B,CAAC,CAAC;EACrC;EACAnJ,WAAWA,CAAA,EAAG;IACV,IAAI,CAACkI,WAAW,EAAEkB,OAAO,CAAC,CAAC;IAC3B,IAAI,CAACJ,YAAY,CAACI,OAAO,CAAC,CAAC;IAC3B,IAAI,CAACjB,UAAU,CAAC/G,IAAI,CAAC,CAAC;IACtB,IAAI,CAAC+G,UAAU,CAAC7H,QAAQ,CAAC,CAAC;EAC9B;EACA;EACAyI,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAACJ,MAAM,IAAI,IAAI,CAACA,MAAM,CAACU,IAAI,CAAChJ,IAAI,IAAIA,IAAI,CAACjD,SAAS,CAAC,CAAC,CAAC;EACpE;EACA;EACAqL,eAAeA,CAAA,EAAG;IACd,IAAI,CAACE,MAAM,EAAEW,OAAO,CAACjJ,IAAI,IAAI;MACzBA,IAAI,CAAC3C,iBAAiB,GAAG,IAAI,CAACtF,SAAS;MACvCiI,IAAI,CAAClE,kBAAkB,CAAC0D,YAAY,CAAC,CAAC;IAC1C,CAAC,CAAC;EACN;EACA;EACA7G,KAAKA,CAAA,EAAG,CAAE;EACV;EACAK,cAAcA,CAACH,KAAK,EAAE;IAClB,IAAI,IAAI,CAACqQ,mBAAmB,CAACrQ,KAAK,CAAC,EAAE;MACjC,IAAI,CAACgP,WAAW,CAACsB,SAAS,CAACtQ,KAAK,CAAC;IACrC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIuQ,aAAaA,CAACC,KAAK,EAAE;IACjB,OAAOA,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAG,IAAI,CAACf,MAAM,CAACC,MAAM;EACnD;EACA;AACJ;AACA;AACA;AACA;EACIe,iBAAiBA,CAAA,EAAG;IAChB,MAAMC,QAAQ,GAAG,IAAI,CAAC7R,WAAW,CAACc,aAAa,CAACP,QAAQ;IACxD,IAAIsR,QAAQ,KAAK,CAAC,CAAC,EAAE;MACjB;MACA;MACA;MACA,IAAI,CAAC7R,WAAW,CAACc,aAAa,CAACP,QAAQ,GAAG,CAAC,CAAC;MAC5C;MACA;MACA+I,UAAU,CAAC,MAAO,IAAI,CAACtJ,WAAW,CAACc,aAAa,CAACP,QAAQ,GAAGsR,QAAS,CAAC;IAC1E;EACJ;EACA;AACJ;AACA;AACA;EACItB,cAAcA,CAACuB,eAAe,EAAE;IAC5B,OAAO,IAAI,CAAClB,MAAM,CAAChJ,OAAO,CAACoH,IAAI,CAACxS,SAAS,CAAC,IAAI,CAAC,EAAEC,SAAS,CAAC,MAAMT,KAAK,CAAC,GAAG,IAAI,CAAC4U,MAAM,CAACmB,GAAG,CAACD,eAAe,CAAC,CAAC,CAAC,CAAC;EACjH;EACA;EACAN,mBAAmBA,CAACrQ,KAAK,EAAE;IACvB,IAAI6Q,cAAc,GAAG7Q,KAAK,CAACwH,MAAM;IACjC,OAAOqJ,cAAc,IAAIA,cAAc,KAAK,IAAI,CAAChS,WAAW,CAACc,aAAa,EAAE;MACxE,IAAIkR,cAAc,CAACC,SAAS,CAAClJ,QAAQ,CAAC,cAAc,CAAC,EAAE;QACnD,OAAO,IAAI;MACf;MACAiJ,cAAc,GAAGA,cAAc,CAACE,aAAa;IACjD;IACA,OAAO,KAAK;EAChB;EACA;EACAhB,qBAAqBA,CAAA,EAAG;IACpB;IACA;IACA;IACA,IAAI,CAACN,MAAM,CAAChJ,OAAO,CAACoH,IAAI,CAACxS,SAAS,CAAC,IAAI,CAACoU,MAAM,CAAC,CAAC,CAAC/I,SAAS,CAAEsK,KAAK,IAAK;MAClE,MAAMC,OAAO,GAAG,EAAE;MAClBD,KAAK,CAACZ,OAAO,CAACjJ,IAAI,IAAIA,IAAI,CAACM,WAAW,CAAC,CAAC,CAAC2I,OAAO,CAACzI,MAAM,IAAIsJ,OAAO,CAACnJ,IAAI,CAACH,MAAM,CAAC,CAAC,CAAC;MACjF,IAAI,CAACmI,YAAY,CAACoB,KAAK,CAACD,OAAO,CAAC;MAChC,IAAI,CAACnB,YAAY,CAACqB,eAAe,CAAC,CAAC;IACvC,CAAC,CAAC;IACF,IAAI,CAACnC,WAAW,GAAG,IAAIxW,eAAe,CAAC,IAAI,CAACsX,YAAY,CAAC,CACpDsB,uBAAuB,CAAC,CAAC,CACzBC,yBAAyB,CAAC,IAAI,CAACvC,IAAI,GAAG,IAAI,CAACA,IAAI,CAAC3P,KAAK,GAAG,KAAK,CAAC,CAC9DmS,cAAc,CAAC,CAAC,CAChBC,aAAa,CAAC5J,MAAM,IAAI,IAAI,CAAC6J,cAAc,CAAC7J,MAAM,CAAC,CAAC;IACzD;IACA;IACA,IAAI,CAACwH,gBAAgB,CAACtB,IAAI,CAACzS,SAAS,CAAC,IAAI,CAAC6T,UAAU,CAAC,CAAC,CAACvI,SAAS,CAAC,CAAC;MAAES;IAAK,CAAC,KAAK;MAC3E,MAAMQ,MAAM,GAAGR,IAAI,CAACI,gBAAgB,CAACkK,QAAQ,CAACrD,aAAa,CAAC;MAC5D,IAAIzG,MAAM,EAAE;QACR,IAAI,CAACqH,WAAW,CAAC0C,gBAAgB,CAAC/J,MAAM,CAAC;MAC7C;IACJ,CAAC,CAAC;IACF,IAAI,CAACmH,IAAI,EAAE6C,MAAM,CACZ9D,IAAI,CAACzS,SAAS,CAAC,IAAI,CAAC6T,UAAU,CAAC,CAAC,CAChCvI,SAAS,CAACkL,SAAS,IAAI,IAAI,CAAC5C,WAAW,CAACqC,yBAAyB,CAACO,SAAS,CAAC,CAAC;EACtF;EACA;AACJ;AACA;AACA;EACIJ,cAAcA,CAAC7J,MAAM,EAAE;IACnB;IACA;IACA,OAAO,CAACA,MAAM,CAAC5I,aAAa,IAAI4I,MAAM,CAAC1I,QAAQ;EACnD;EACA;EACA+Q,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACP,MAAM,CAAChJ,OAAO,CAACoH,IAAI,CAACxS,SAAS,CAAC,IAAI,CAAC,EAAED,SAAS,CAAC,IAAI,CAAC6T,UAAU,CAAC,CAAC,CAACvI,SAAS,CAAC,MAAM;MAClF,IAAI,IAAI,CAACzH,QAAQ,EAAE;QACf;QACA;QACA4S,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM,IAAI,CAACxC,eAAe,CAAC,CAAC,CAAC;MACxD;MACA,IAAI,CAACyC,2BAA2B,CAAC,CAAC;IACtC,CAAC,CAAC;EACN;EACA;EACA/B,0BAA0BA,CAAA,EAAG;IACzB,IAAI,CAACZ,oBAAoB,CAACxB,IAAI,CAACzS,SAAS,CAAC,IAAI,CAAC6T,UAAU,CAAC,CAAC,CAACvI,SAAS,CAAE1G,KAAK,IAAK;MAC5E,MAAMiS,SAAS,GAAG,IAAI,CAACxC,MAAM,CAACyC,OAAO,CAAC,CAAC;MACvC,MAAMC,SAAS,GAAGF,SAAS,CAACG,OAAO,CAACpS,KAAK,CAACmH,IAAI,CAAC;MAC/C;MACA;MACA;MACA;MACA,IAAI,IAAI,CAACoJ,aAAa,CAAC4B,SAAS,CAAC,IAAInS,KAAK,CAACmH,IAAI,CAACjD,SAAS,CAAC,CAAC,EAAE;QACzD,IAAI,CAAC6K,8BAA8B,GAAGoD,SAAS;MACnD;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACIH,2BAA2BA,CAAA,EAAG;IAC1B,IAAI,IAAI,CAACjD,8BAA8B,IAAI,IAAI,EAAE;MAC7C;IACJ;IACA,IAAI,IAAI,CAACU,MAAM,CAACC,MAAM,EAAE;MACpB,MAAM2C,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAAC,IAAI,CAACxD,8BAA8B,EAAE,IAAI,CAACU,MAAM,CAACC,MAAM,GAAG,CAAC,CAAC;MACtF,MAAM8C,WAAW,GAAG,IAAI,CAAC/C,MAAM,CAACyC,OAAO,CAAC,CAAC,CAACG,QAAQ,CAAC;MACnD,IAAIG,WAAW,CAACvT,QAAQ,EAAE;QACtB;QACA,IAAI,IAAI,CAACwQ,MAAM,CAACC,MAAM,KAAK,CAAC,EAAE;UAC1B,IAAI,CAAC5P,KAAK,CAAC,CAAC;QAChB,CAAC,MACI;UACD,IAAI,CAACkP,WAAW,CAACyD,qBAAqB,CAAC,CAAC;QAC5C;MACJ,CAAC,MACI;QACDD,WAAW,CAAC1S,KAAK,CAAC,CAAC;MACvB;IACJ,CAAC,MACI;MACD,IAAI,CAACA,KAAK,CAAC,CAAC;IAChB;IACA,IAAI,CAACiP,8BAA8B,GAAG,IAAI;EAC9C;EACA,OAAOzO,IAAI,YAAAoS,mBAAAlS,iBAAA;IAAA,YAAAA,iBAAA,IAAwFqO,UAAU;EAAA;EAC7G,OAAOvG,IAAI,kBAhhC8ElP,EAAE,CAAAmP,iBAAA;IAAA5H,IAAA,EAghCJkO,UAAU;IAAAjO,SAAA;IAAA4H,cAAA,WAAAmK,0BAAAlW,EAAA,EAAAC,GAAA,EAAAgM,QAAA;MAAA,IAAAjM,EAAA;QAhhCRrD,EAAE,CAAAuP,cAAA,CAAAD,QAAA,EAghCgb1F,OAAO;MAAA;MAAA,IAAAvG,EAAA;QAAA,IAAAmM,EAAA;QAhhCzbxP,EAAE,CAAAyP,cAAA,CAAAD,EAAA,GAAFxP,EAAE,CAAA0P,WAAA,QAAApM,GAAA,CAAA+S,MAAA,GAAA7G,EAAA;MAAA;IAAA;IAAA/H,SAAA;IAAAC,QAAA;IAAAC,YAAA,WAAA6R,wBAAAnW,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFrD,EAAE,CAAA6H,UAAA,qBAAA4R,sCAAA1R,MAAA;UAAA,OAghCJzE,GAAA,CAAAyD,cAAA,CAAAgB,MAAqB,CAAC;QAAA,CAAb,CAAC;MAAA;MAAA,IAAA1E,EAAA;QAhhCRrD,EAAE,CAAAiI,WAAA,SAAA3E,GAAA,CAAAgH,IAAA;MAAA;IAAA;IAAAnC,MAAA;MAAAtC,QAAA,8BAghCmGzF,gBAAgB;MAAAkK,IAAA;MAAAtE,QAAA,8BAAqDD,KAAK,IAAMA,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG1F,eAAe,CAAC0F,KAAK,CAAE;IAAA;IAAAsK,kBAAA,EAAAvL,GAAA;IAAAwL,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAiJ,oBAAArW,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAhhChOrD,EAAE,CAAA2Q,eAAA;QAAF3Q,EAAE,CAAAuD,cAAA,YAihC7B,CAAC;QAjhC0BvD,EAAE,CAAAwD,YAAA,EAkhCjE,CAAC;QAlhC8DxD,EAAE,CAAAyD,YAAA,CAmhCtF,CAAC;MAAA;IAAA;IAAAsN,MAAA;IAAAC,aAAA;IAAAC,eAAA;EAAA;AAEV;AACA;EAAA,QAAA7I,SAAA,oBAAAA,SAAA,KAthC6FpI,EAAE,CAAAqI,iBAAA,CAshCJoN,UAAU,EAAc,CAAC;IACxGlO,IAAI,EAAE1G,SAAS;IACfyH,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,cAAc;MAAEkI,QAAQ,EAAE;AACzD;AACA;AACA;AACA,GAAG;MAAEjI,IAAI,EAAE;QACa,OAAO,EAAE,yCAAyC;QAClD,WAAW,EAAE,wBAAwB;QACrC,aAAa,EAAE;MACnB,CAAC;MAAEwI,aAAa,EAAElQ,iBAAiB,CAACoQ,IAAI;MAAED,eAAe,EAAElQ,uBAAuB,CAACoQ,MAAM;MAAEJ,MAAM,EAAE,CAAC,g3BAAg3B;IAAE,CAAC;EACn+B,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAElL,QAAQ,EAAE,CAAC;MACnD0B,IAAI,EAAEhH,KAAK;MACX+H,IAAI,EAAE,CAAC;QAAEG,SAAS,EAAErI;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEkK,IAAI,EAAE,CAAC;MACP/C,IAAI,EAAEhH;IACV,CAAC,CAAC;IAAEyF,QAAQ,EAAE,CAAC;MACXuB,IAAI,EAAEhH,KAAK;MACX+H,IAAI,EAAE,CAAC;QACCG,SAAS,EAAG1C,KAAK,IAAMA,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG1F,eAAe,CAAC0F,KAAK;MACpE,CAAC;IACT,CAAC,CAAC;IAAEsQ,MAAM,EAAE,CAAC;MACT9O,IAAI,EAAEvG,eAAe;MACrBsH,IAAI,EAAE,CAACsB,OAAO,EAAE;QACR;QACA;QACAyH,WAAW,EAAE;MACjB,CAAC;IACT,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,MAAMsI,oBAAoB,CAAC;EACvBpI,MAAM;EACNxL,KAAK;EACLM,WAAWA,CACX;EACAkL,MAAM,EACN;EACAxL,KAAK,EAAE;IACH,IAAI,CAACwL,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACxL,KAAK,GAAGA,KAAK;EACtB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,MAAM6T,uCAAuC,GAAG;EAC5C9Q,OAAO,EAAE1G,iBAAiB;EAC1B2G,WAAW,EAAEzH,UAAU,CAAC,MAAMuY,cAAc,CAAC;EAC7CC,KAAK,EAAE;AACX,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMD,cAAc,SAASpE,UAAU,CAAC;EACpC;AACJ;AACA;AACA;EACIsE,UAAU,GAAGA,CAAA,KAAM,CAAE,CAAC;EACtB;AACJ;AACA;AACA;EACIC,SAAS,GAAGA,CAAA,KAAM,CAAE,CAAC;EACrB;EACAlE,YAAY,GAAG,SAAS;EACxB;EACAnE,eAAe,GAAGzR,MAAM,CAAC8E,yBAAyB,EAAE;IAAEiF,QAAQ,EAAE;EAAK,CAAC,CAAC;EACvE;EACA,IAAIgQ,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAAClU,KAAK,EAAE;IAChB,IAAI,CAACmU,SAAS,GAAGnU,KAAK;IACtB,IAAI,CAACoU,sBAAsB,CAAC,CAAC;EACjC;EACAD,SAAS,GAAG,KAAK;EACjB;EACA,IAAI1I,QAAQA,CAAA,EAAG;IACX,MAAM4I,aAAa,GAAG,IAAI,CAAC/D,MAAM,CAACyC,OAAO,CAAC,CAAC,CAACuB,MAAM,CAACtM,IAAI,IAAIA,IAAI,CAACyD,QAAQ,CAAC;IACzE,OAAO,IAAI,CAACyI,QAAQ,GAAGG,aAAa,GAAGA,aAAa,CAAC,CAAC,CAAC;EAC3D;EACA;EACAE,eAAe,GAAG,YAAY;EAC9B;AACJ;AACA;AACA;AACA;AACA;EACI,IAAItI,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACC,WAAW;EAC3B;EACA,IAAID,UAAUA,CAACjM,KAAK,EAAE;IAClB,IAAI,CAACkM,WAAW,GAAGlM,KAAK;IACxB,IAAI,CAACoU,sBAAsB,CAAC,CAAC;EACjC;EACAlI,WAAW,GAAG,IAAI;EAClB;AACJ;AACA;AACA;AACA;EACIsI,WAAW,GAAGA,CAACC,EAAE,EAAEC,EAAE,KAAKD,EAAE,KAAKC,EAAE;EACnC;EACAC,QAAQ,GAAG,KAAK;EAChB;EACA,IAAI3I,4BAA4BA,CAAA,EAAG;IAC/B,OAAO,IAAI,CAAC4I,6BAA6B;EAC7C;EACA,IAAI5I,4BAA4BA,CAAChM,KAAK,EAAE;IACpC,IAAI,CAAC4U,6BAA6B,GAAG5U,KAAK;IAC1C,IAAI,CAACoU,sBAAsB,CAAC,CAAC;EACjC;EACAQ,6BAA6B,GAAG,IAAI,CAAChJ,eAAe,EAAEI,4BAA4B,IAAI,KAAK;EAC3F;EACA,IAAI6I,oBAAoBA,CAAA,EAAG;IACvB,OAAO,IAAI,CAAC5E,cAAc,CAACjI,IAAI,IAAIA,IAAI,CAACsE,eAAe,CAAC;EAC5D;EACA;EACA,IAAIwI,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAAC7E,cAAc,CAACjI,IAAI,IAAIA,IAAI,CAAC3D,OAAO,CAAC;EACpD;EACA;EACA,IAAIrE,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACuF,MAAM;EACtB;EACA,IAAIvF,KAAKA,CAACA,KAAK,EAAE;IACb,IAAI,IAAI,CAACsQ,MAAM,IAAI,IAAI,CAACA,MAAM,CAACC,MAAM,EAAE;MACnC,IAAI,CAACwE,oBAAoB,CAAC/U,KAAK,EAAE,KAAK,CAAC;IAC3C;IACA,IAAI,CAACuF,MAAM,GAAGvF,KAAK;EACvB;EACAuF,MAAM;EACN;EACAiN,MAAM,GAAG,IAAI7X,YAAY,CAAC,CAAC;EAC3B2V,MAAM,GAAG9K,SAAS;EAClB6B,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACiJ,MAAM,CAAChJ,OAAO,CAACoH,IAAI,CAACxS,SAAS,CAAC,IAAI,CAAC,EAAED,SAAS,CAAC,IAAI,CAAC6T,UAAU,CAAC,CAAC,CAACvI,SAAS,CAAC,MAAM;MAClF,IAAI,IAAI,CAACvH,KAAK,KAAKwF,SAAS,EAAE;QAC1BkN,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;UACzB,IAAI,CAACmC,oBAAoB,CAAC,IAAI,CAAC/U,KAAK,EAAE,KAAK,CAAC;QAChD,CAAC,CAAC;MACN;MACA;MACA,IAAI,CAACoU,sBAAsB,CAAC,CAAC;IACjC,CAAC,CAAC;IACF,IAAI,CAACU,eAAe,CAACpG,IAAI,CAACzS,SAAS,CAAC,IAAI,CAAC6T,UAAU,CAAC,CAAC,CAACvI,SAAS,CAAC,MAAM,IAAI,CAACyN,KAAK,CAAC,CAAC,CAAC;IACnF,IAAI,CAACH,oBAAoB,CAACnG,IAAI,CAACzS,SAAS,CAAC,IAAI,CAAC6T,UAAU,CAAC,CAAC,CAACvI,SAAS,CAAC1G,KAAK,IAAI;MAC1E,IAAI,CAAC,IAAI,CAACqT,QAAQ,EAAE;QAChB,IAAI,CAAC5D,MAAM,CAACW,OAAO,CAACjJ,IAAI,IAAI;UACxB,IAAIA,IAAI,KAAKnH,KAAK,CAAC2K,MAAM,EAAE;YACvBxD,IAAI,CAACoE,iBAAiB,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;UAC/C;QACJ,CAAC,CAAC;MACN;MACA,IAAIvL,KAAK,CAAC6K,WAAW,EAAE;QACnB,IAAI,CAACuJ,iBAAiB,CAAC,CAAC;MAC5B;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACItU,KAAKA,CAAA,EAAG;IACJ,IAAI,IAAI,CAACb,QAAQ,EAAE;MACf;IACJ;IACA,MAAMoV,iBAAiB,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IACtD,IAAID,iBAAiB,IAAI,CAACA,iBAAiB,CAACpV,QAAQ,EAAE;MAClDoV,iBAAiB,CAACvU,KAAK,CAAC,CAAC;IAC7B,CAAC,MACI,IAAI,IAAI,CAAC2P,MAAM,CAACC,MAAM,GAAG,CAAC,EAAE;MAC7B,IAAI,CAACV,WAAW,CAACuF,kBAAkB,CAAC,CAAC;IACzC,CAAC,MACI;MACD,IAAI,CAAC1V,WAAW,CAACc,aAAa,CAACG,KAAK,CAAC,CAAC;IAC1C;EACJ;EACA;AACJ;AACA;AACA;EACI0U,UAAUA,CAACrV,KAAK,EAAE;IACd,IAAIA,KAAK,IAAI,IAAI,EAAE;MACf,IAAI,CAACA,KAAK,GAAGA,KAAK;IACtB,CAAC,MACI;MACD,IAAI,CAACA,KAAK,GAAGwF,SAAS;IAC1B;EACJ;EACA;AACJ;AACA;AACA;EACI8P,gBAAgBA,CAACC,EAAE,EAAE;IACjB,IAAI,CAACtB,SAAS,GAAGsB,EAAE;EACvB;EACA;AACJ;AACA;AACA;EACIC,iBAAiBA,CAACD,EAAE,EAAE;IAClB,IAAI,CAACvB,UAAU,GAAGuB,EAAE;EACxB;EACA;AACJ;AACA;AACA;EACIE,gBAAgBA,CAACC,UAAU,EAAE;IACzB,IAAI,CAAC5V,QAAQ,GAAG4V,UAAU;EAC9B;EACA;EACAX,oBAAoBA,CAAC/U,KAAK,EAAE0L,WAAW,GAAG,IAAI,EAAE;IAC5C,IAAI,CAACiK,eAAe,CAAC,CAAC;IACtB,IAAIC,KAAK,CAACC,OAAO,CAAC7V,KAAK,CAAC,EAAE;MACtBA,KAAK,CAACiR,OAAO,CAAC6E,YAAY,IAAI,IAAI,CAACC,YAAY,CAACD,YAAY,EAAEpK,WAAW,CAAC,CAAC;IAC/E,CAAC,MACI;MACD,IAAI,CAACqK,YAAY,CAAC/V,KAAK,EAAE0L,WAAW,CAAC;IACzC;EACJ;EACA;EACAsJ,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC,IAAI,CAAClV,QAAQ,EAAE;MAChB;MACAkJ,UAAU,CAAC,MAAM;QACb,IAAI,CAAC,IAAI,CAACyH,OAAO,EAAE;UACf,IAAI,CAACuF,cAAc,CAAC,CAAC;QACzB;MACJ,CAAC,CAAC;IACN;EACJ;EACAC,QAAQA,CAACpV,KAAK,EAAE;IACZ,IAAIA,KAAK,CAACI,OAAO,KAAKvH,GAAG,EAAE;MACvB,KAAK,CAAC4X,iBAAiB,CAAC,CAAC;IAC7B;EACJ;EACA;EACA0E,cAAcA,CAAA,EAAG;IACb,IAAI,CAAChC,UAAU,CAAC,CAAC;IACjB,IAAI,CAAClQ,kBAAkB,CAAC0D,YAAY,CAAC,CAAC;EAC1C;EACA;EACAyN,iBAAiBA,CAAA,EAAG;IAChB,IAAIiB,WAAW,GAAG,IAAI;IACtB,IAAIN,KAAK,CAACC,OAAO,CAAC,IAAI,CAACpK,QAAQ,CAAC,EAAE;MAC9ByK,WAAW,GAAG,IAAI,CAACzK,QAAQ,CAACgG,GAAG,CAACzJ,IAAI,IAAIA,IAAI,CAAChI,KAAK,CAAC;IACvD,CAAC,MACI;MACDkW,WAAW,GAAG,IAAI,CAACzK,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACzL,KAAK,GAAGwF,SAAS;IACjE;IACA,IAAI,CAACD,MAAM,GAAG2Q,WAAW;IACzB,IAAI,CAAC1D,MAAM,CAACzK,IAAI,CAAC,IAAI6L,oBAAoB,CAAC,IAAI,EAAEsC,WAAW,CAAC,CAAC;IAC7D,IAAI,CAACjC,SAAS,CAACiC,WAAW,CAAC;IAC3B,IAAI,CAACpS,kBAAkB,CAAC0D,YAAY,CAAC,CAAC;EAC1C;EACA;AACJ;AACA;AACA;EACImO,eAAeA,CAACQ,IAAI,EAAE;IAClB,IAAI,CAAC7F,MAAM,CAACW,OAAO,CAACjJ,IAAI,IAAI;MACxB,IAAIA,IAAI,KAAKmO,IAAI,EAAE;QACfnO,IAAI,CAACwE,QAAQ,CAAC,CAAC;MACnB;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACIuJ,YAAYA,CAAC/V,KAAK,EAAE0L,WAAW,EAAE;IAC7B,MAAM0K,iBAAiB,GAAG,IAAI,CAAC9F,MAAM,CAAC/H,IAAI,CAACP,IAAI,IAAI;MAC/C,OAAOA,IAAI,CAAChI,KAAK,IAAI,IAAI,IAAI,IAAI,CAACwU,WAAW,CAACxM,IAAI,CAAChI,KAAK,EAAEA,KAAK,CAAC;IACpE,CAAC,CAAC;IACF,IAAIoW,iBAAiB,EAAE;MACnB1K,WAAW,GAAG0K,iBAAiB,CAAC3J,oBAAoB,CAAC,CAAC,GAAG2J,iBAAiB,CAAC7J,MAAM,CAAC,CAAC;IACvF;IACA,OAAO6J,iBAAiB;EAC5B;EACA;EACAhC,sBAAsBA,CAAA,EAAG;IACrB,IAAI,IAAI,CAAC9D,MAAM,EAAE;MACb;MACA;MACAoC,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;QACzB,IAAI,CAACtC,MAAM,CAACW,OAAO,CAACjJ,IAAI,IAAI;UACxBA,IAAI,CAAC8D,iBAAiB,GAAG,IAAI,CAACoI,QAAQ;UACtClM,IAAI,CAAC6D,kBAAkB,GAAG,IAAI,CAACK,WAAW;UAC1ClE,IAAI,CAAC+D,qCAAqC,GAAG,IAAI,CAACC,4BAA4B;UAC9EhE,IAAI,CAAClE,kBAAkB,CAAC0D,YAAY,CAAC,CAAC;QAC1C,CAAC,CAAC;MACN,CAAC,CAAC;IACN;EACJ;EACA;EACA2N,qBAAqBA,CAAA,EAAG;IACpB,IAAIS,KAAK,CAACC,OAAO,CAAC,IAAI,CAACpK,QAAQ,CAAC,EAAE;MAC9B,OAAO,IAAI,CAACA,QAAQ,CAAC8E,MAAM,GAAG,IAAI,CAAC9E,QAAQ,CAAC,CAAC,CAAC,GAAGjG,SAAS;IAC9D,CAAC,MACI;MACD,OAAO,IAAI,CAACiG,QAAQ;IACxB;EACJ;EACA;AACJ;AACA;AACA;EACI4G,cAAcA,CAAC7J,MAAM,EAAE;IACnB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,OAAO,CAACA,MAAM,CAAC5I,aAAa;EAChC;EACA,OAAOuB,IAAI;IAAA,IAAAkV,2BAAA;IAAA,gBAAAC,uBAAAjV,iBAAA;MAAA,QAAAgV,2BAAA,KAAAA,2BAAA,GA51C8Epc,EAAE,CAAAoJ,qBAAA,CA41CQyQ,cAAc,IAAAzS,iBAAA,IAAdyS,cAAc;IAAA;EAAA;EACjH,OAAO3K,IAAI,kBA71C8ElP,EAAE,CAAAmP,iBAAA;IAAA5H,IAAA,EA61CJsS,cAAc;IAAArS,SAAA;IAAA4H,cAAA,WAAAkN,8BAAAjZ,EAAA,EAAAC,GAAA,EAAAgM,QAAA;MAAA,IAAAjM,EAAA;QA71CZrD,EAAE,CAAAuP,cAAA,CAAAD,QAAA,EA61CslCoC,aAAa;MAAA;MAAA,IAAArO,EAAA;QAAA,IAAAmM,EAAA;QA71CrmCxP,EAAE,CAAAyP,cAAA,CAAAD,EAAA,GAAFxP,EAAE,CAAA0P,WAAA,QAAApM,GAAA,CAAA+S,MAAA,GAAA7G,EAAA;MAAA;IAAA;IAAA/H,SAAA;IAAAC,QAAA;IAAAC,YAAA,WAAA4U,4BAAAlZ,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFrD,EAAE,CAAA6H,UAAA,mBAAA2U,wCAAA;UAAA,OA61CJlZ,GAAA,CAAAoD,KAAA,CAAM,CAAC;QAAA,CAAM,CAAC,kBAAA+V,uCAAA;UAAA,OAAdnZ,GAAA,CAAAyX,KAAA,CAAM,CAAC;QAAA,CAAM,CAAC,qBAAA2B,0CAAA3U,MAAA;UAAA,OAAdzE,GAAA,CAAA0Y,QAAA,CAAAjU,MAAe,CAAC;QAAA,CAAH,CAAC;MAAA;MAAA,IAAA1E,EAAA;QA71CZrD,EAAE,CAAAiQ,cAAA,aAAA3M,GAAA,CAAAuC,QAAA,IAAAvC,GAAA,CAAA8S,KAAA,IA61CmB,CAAC,GAAA9S,GAAA,CAAA0C,QAAX,CAAC;QA71CZhG,EAAE,CAAAiI,WAAA,SAAA3E,GAAA,CAAAgH,IAAA,mBAAAhH,GAAA,CAAAgH,IAAA,GAAAhH,GAAA,CAAAoX,QAAA,GA61Cc,IAAI,mBAAtBpX,GAAA,CAAAuC,QAAA,CAAAO,QAAA,CAAkB,CAAC,0BAAA9C,GAAA,CAAA2W,QAAA,sBAAA3W,GAAA,CAAAgX,eAAA;QA71CjBta,EAAE,CAAAkI,WAAA,+BAAA5E,GAAA,CAAAuC,QA61CS,CAAC,+BAAAvC,GAAA,CAAAoX,QAAD,CAAC;MAAA;IAAA;IAAAvS,MAAA;MAAA8R,QAAA,8BAAiG7Z,gBAAgB;MAAAka,eAAA;MAAAtI,UAAA,kCAAsG5R,gBAAgB;MAAAma,WAAA;MAAAG,QAAA,8BAAkEta,gBAAgB;MAAA2R,4BAAA,sEAAkG3R,gBAAgB;MAAA2F,KAAA;IAAA;IAAAoK,OAAA;MAAAoI,MAAA;IAAA;IAAA3P,QAAA,GA71Cvb5I,EAAE,CAAA6I,kBAAA,CA61C4/B,CAAC+Q,uCAAuC,CAAC,GA71CviC5Z,EAAE,CAAAqJ,0BAAA;IAAAgH,kBAAA,EAAAvL,GAAA;IAAAwL,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAkM,wBAAAtZ,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFrD,EAAE,CAAA2Q,eAAA;QAAF3Q,EAAE,CAAAuD,cAAA,YA81C7B,CAAC;QA91C0BvD,EAAE,CAAAwD,YAAA,EA+1CjE,CAAC;QA/1C8DxD,EAAE,CAAAyD,YAAA,CAg2CtF,CAAC;MAAA;IAAA;IAAAsN,MAAA,GAAAhM,GAAA;IAAAiM,aAAA;IAAAC,eAAA;EAAA;AAEV;AACA;EAAA,QAAA7I,SAAA,oBAAAA,SAAA,KAn2C6FpI,EAAE,CAAAqI,iBAAA,CAm2CJwR,cAAc,EAAc,CAAC;IAC5GtS,IAAI,EAAE1G,SAAS;IACfyH,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,kBAAkB;MAAEkI,QAAQ,EAAE;AAC7D;AACA;AACA;AACA,GAAG;MAAEjI,IAAI,EAAE;QACa,OAAO,EAAE,6CAA6C;QACtD,aAAa,EAAE,MAAM;QACrB,YAAY,EAAE,qCAAqC;QACnD,sBAAsB,EAAE,wBAAwB;QAChD,sBAAsB,EAAE,qBAAqB;QAC7C,6BAA6B,EAAE,UAAU;QACzC,yBAAyB,EAAE,iBAAiB;QAC5C,oCAAoC,EAAE,UAAU;QAChD,oCAAoC,EAAE,UAAU;QAChD,SAAS,EAAE,SAAS;QACpB,QAAQ,EAAE,SAAS;QACnB,WAAW,EAAE;MACjB,CAAC;MAAEQ,SAAS,EAAE,CAAC4Q,uCAAuC,CAAC;MAAE5I,aAAa,EAAElQ,iBAAiB,CAACoQ,IAAI;MAAED,eAAe,EAAElQ,uBAAuB,CAACoQ,MAAM;MAAEJ,MAAM,EAAE,CAAC,g3BAAg3B;IAAE,CAAC;EACzhC,CAAC,CAAC,QAAkB;IAAEkJ,QAAQ,EAAE,CAAC;MACzB1S,IAAI,EAAEhH,KAAK;MACX+H,IAAI,EAAE,CAAC;QAAEG,SAAS,EAAErI;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEka,eAAe,EAAE,CAAC;MAClB/S,IAAI,EAAEhH,KAAK;MACX+H,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC,CAAC;IAAE0J,UAAU,EAAE,CAAC;MACbzK,IAAI,EAAEhH,KAAK;MACX+H,IAAI,EAAE,CAAC;QAAEG,SAAS,EAAErI;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEma,WAAW,EAAE,CAAC;MACdhT,IAAI,EAAEhH;IACV,CAAC,CAAC;IAAEma,QAAQ,EAAE,CAAC;MACXnT,IAAI,EAAEhH,KAAK;MACX+H,IAAI,EAAE,CAAC;QAAEG,SAAS,EAAErI;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE2R,4BAA4B,EAAE,CAAC;MAC/BxK,IAAI,EAAEhH,KAAK;MACX+H,IAAI,EAAE,CAAC;QAAEG,SAAS,EAAErI;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE2F,KAAK,EAAE,CAAC;MACRwB,IAAI,EAAEhH;IACV,CAAC,CAAC;IAAEgY,MAAM,EAAE,CAAC;MACThR,IAAI,EAAEtG;IACV,CAAC,CAAC;IAAEoV,MAAM,EAAE,CAAC;MACT9O,IAAI,EAAEvG,eAAe;MACrBsH,IAAI,EAAE,CAACoJ,aAAa,EAAE;QACd;QACA;QACAL,WAAW,EAAE;MACjB,CAAC;IACT,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,MAAMuL,iBAAiB,CAAC;EACpBrL,MAAM;EACNxL,KAAK;EACLM,WAAWA,CACX;EACAkL,MAAM,EACN;EACAxL,KAAK,EAAE;IACH,IAAI,CAACwL,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACxL,KAAK,GAAGA,KAAK;EACtB;AACJ;AACA;AACA;AACA;AACA;AACA,MAAM8W,WAAW,SAASpH,UAAU,CAAC;EACjCqH,SAAS,GAAG5c,MAAM,CAACmC,SAAS,EAAE;IAAE4H,QAAQ,EAAE,IAAI;IAAE8S,IAAI,EAAE;EAAK,CAAC,CAAC;EAC7D;AACJ;AACA;AACA;EACIC,WAAW,GAAG,eAAe;EAC7B;EACAC,UAAU;EACVnH,YAAY,GAAG,MAAM;EACrBoH,kBAAkB;EAClB;AACJ;AACA;EACIC,mBAAmB,GAAG,EAAE;EACxB;AACJ;AACA;AACA;EACIpD,UAAU,GAAGA,CAAA,KAAM,CAAE,CAAC;EACtB;AACJ;AACA;AACA;EACIC,SAAS,GAAGA,CAAA,KAAM,CAAE,CAAC;EACrB;AACJ;AACA;AACA;EACI,IAAInU,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACiX,SAAS,GAAG,CAAC,CAAC,IAAI,CAACA,SAAS,CAACjX,QAAQ,GAAG,IAAI,CAACC,SAAS;EACtE;EACA,IAAID,QAAQA,CAACE,KAAK,EAAE;IAChB,IAAI,CAACD,SAAS,GAAGC,KAAK;IACtB,IAAI,CAACoQ,eAAe,CAAC,CAAC;IACtB,IAAI,CAACiH,YAAY,CAACtO,IAAI,CAAC,CAAC;EAC5B;EACA;AACJ;AACA;AACA;EACI,IAAI/D,EAAEA,CAAA,EAAG;IACL,OAAO,IAAI,CAACkS,UAAU,CAAClS,EAAE;EAC7B;EACA;AACJ;AACA;AACA;EACI,IAAIqL,KAAKA,CAAA,EAAG;IACR,OAAQ,CAAC,CAAC,IAAI,CAAC6G,UAAU,IAAI,IAAI,CAACA,UAAU,CAAC7G,KAAK,MAAM,CAAC,IAAI,CAACC,MAAM,IAAI,IAAI,CAACA,MAAM,CAACC,MAAM,KAAK,CAAC,CAAC;EACrG;EACA;AACJ;AACA;AACA;EACI,IAAI+G,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACJ,UAAU,GAAG,IAAI,CAACA,UAAU,CAACI,WAAW,GAAG,IAAI,CAACC,YAAY;EAC5E;EACA,IAAID,WAAWA,CAACtX,KAAK,EAAE;IACnB,IAAI,CAACuX,YAAY,GAAGvX,KAAK;IACzB,IAAI,CAACqX,YAAY,CAACtO,IAAI,CAAC,CAAC;EAC5B;EACAwO,YAAY;EACZ;EACA,IAAI9G,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACyG,UAAU,CAACzG,OAAO,IAAI,IAAI,CAACC,eAAe,CAAC,CAAC;EAC5D;EACA;AACJ;AACA;AACA;EACI,IAAIiE,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC6C,SAAS,IAAI,IAAI,CAACT,SAAS,EAAEU,OAAO,EAAEC,YAAY,CAACnb,UAAU,CAACoY,QAAQ,CAAC,IAAI,KAAK;EAChG;EACA,IAAIA,QAAQA,CAAC3U,KAAK,EAAE;IAChB,IAAI,CAACwX,SAAS,GAAGxX,KAAK;IACtB,IAAI,CAACqX,YAAY,CAACtO,IAAI,CAAC,CAAC;EAC5B;EACAyO,SAAS;EACT;AACJ;AACA;AACA;EACI,IAAIG,gBAAgBA,CAAA,EAAG;IACnB,OAAO,CAAC,IAAI,CAACtH,KAAK,IAAI,IAAI,CAACI,OAAO;EACtC;EACA;AACJ;AACA;AACA;EACI,IAAIzQ,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACuF,MAAM;EACtB;EACA,IAAIvF,KAAKA,CAACA,KAAK,EAAE;IACb,IAAI,CAACuF,MAAM,GAAGvF,KAAK;EACvB;EACAuF,MAAM,GAAG,EAAE;EACX;EACA,IAAIqS,iBAAiBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACT,kBAAkB,CAACU,OAAO;EAC1C;EACA,IAAID,iBAAiBA,CAAC5X,KAAK,EAAE;IACzB,IAAI,CAACmX,kBAAkB,CAACU,OAAO,GAAG7X,KAAK;EAC3C;EACA;EACA,IAAI8U,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAAC7E,cAAc,CAACjI,IAAI,IAAIA,IAAI,CAAC3D,OAAO,CAAC;EACpD;EACA;EACAmO,MAAM,GAAG,IAAI7X,YAAY,CAAC,CAAC;EAC3B;AACJ;AACA;AACA;AACA;EACImd,WAAW,GAAG,IAAInd,YAAY,CAAC,CAAC;EAChC2V,MAAM,GAAG9K,SAAS;EAClB;AACJ;AACA;AACA;AACA;EACI6R,YAAY,GAAG,IAAI5b,OAAO,CAAC,CAAC;EAC5B;EACA,IAAIsc,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACZ,kBAAkB,CAACY,UAAU;EAC7C;EACA,IAAIA,UAAUA,CAAC/X,KAAK,EAAE;IAClB,IAAI,CAACmX,kBAAkB,CAACY,UAAU,GAAG/X,KAAK;EAC9C;EACAM,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,CAAC;IACP,MAAM0X,UAAU,GAAG7d,MAAM,CAACqC,MAAM,EAAE;MAAE0H,QAAQ,EAAE;IAAK,CAAC,CAAC;IACrD,MAAM+T,eAAe,GAAG9d,MAAM,CAACsC,kBAAkB,EAAE;MAAEyH,QAAQ,EAAE;IAAK,CAAC,CAAC;IACtE,MAAMgU,wBAAwB,GAAG/d,MAAM,CAACwC,iBAAiB,CAAC;IAC1D,IAAI,IAAI,CAACoa,SAAS,EAAE;MAChB,IAAI,CAACA,SAAS,CAACoB,aAAa,GAAG,IAAI;IACvC;IACA,IAAI,CAAChB,kBAAkB,GAAG,IAAIva,kBAAkB,CAACsb,wBAAwB,EAAE,IAAI,CAACnB,SAAS,EAAEkB,eAAe,EAAED,UAAU,EAAE,IAAI,CAACX,YAAY,CAAC;EAC9I;EACAhQ,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACyN,eAAe,CAACpG,IAAI,CAACzS,SAAS,CAAC,IAAI,CAAC6T,UAAU,CAAC,CAAC,CAACvI,SAAS,CAAC,MAAM;MAClE,IAAI,CAACyN,KAAK,CAAC,CAAC;MACZ,IAAI,CAACqC,YAAY,CAACtO,IAAI,CAAC,CAAC;IAC5B,CAAC,CAAC;IACFrN,KAAK,CAAC,IAAI,CAACsU,gBAAgB,EAAE,IAAI,CAACM,MAAM,CAAChJ,OAAO,CAAC,CAC5CoH,IAAI,CAACzS,SAAS,CAAC,IAAI,CAAC6T,UAAU,CAAC,CAAC,CAChCvI,SAAS,CAAC,MAAM,IAAI,CAAC8P,YAAY,CAACtO,IAAI,CAAC,CAAC,CAAC;EAClD;EACA5B,eAAeA,CAAA,EAAG;IACd,KAAK,CAACA,eAAe,CAAC,CAAC;IACvB,IAAI,CAAC,IAAI,CAAC+P,UAAU,KAAK,OAAO7U,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACrE,MAAM+V,KAAK,CAAC,iEAAiE,CAAC;IAClF;EACJ;EACA3Q,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAACsP,SAAS,EAAE;MAChB;MACA;MACA;MACA,IAAI,CAACsB,gBAAgB,CAAC,CAAC;IAC3B;EACJ;EACA1Q,WAAWA,CAAA,EAAG;IACV,KAAK,CAACA,WAAW,CAAC,CAAC;IACnB,IAAI,CAAC0P,YAAY,CAACpP,QAAQ,CAAC,CAAC;EAChC;EACA;EACAqQ,aAAaA,CAACC,YAAY,EAAE;IACxB,IAAI,CAACrB,UAAU,GAAGqB,YAAY;IAC9B,IAAI,CAACrB,UAAU,CAACsB,iBAAiB,CAAC,IAAI,CAACpB,mBAAmB,CAAC;EAC/D;EACA;AACJ;AACA;AACA;EACIqB,gBAAgBA,CAAC5X,KAAK,EAAE;IACpB,IAAI,CAAC,IAAI,CAACf,QAAQ,IAAI,CAAC,IAAI,CAACoR,mBAAmB,CAACrQ,KAAK,CAAC,EAAE;MACpD,IAAI,CAACF,KAAK,CAAC,CAAC;IAChB;EACJ;EACA;AACJ;AACA;AACA;EACIA,KAAKA,CAAA,EAAG;IACJ,IAAI,IAAI,CAACb,QAAQ,IAAI,IAAI,CAACoX,UAAU,CAACzG,OAAO,EAAE;MAC1C;IACJ;IACA,IAAI,CAAC,IAAI,CAACH,MAAM,CAACC,MAAM,IAAI,IAAI,CAACD,MAAM,CAAC1G,KAAK,CAAC9J,QAAQ,EAAE;MACnD;MACA;MACA4S,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM,IAAI,CAACsE,UAAU,CAACvW,KAAK,CAAC,CAAC,CAAC;IACzD,CAAC,MACI;MACD,MAAM+X,UAAU,GAAG,IAAI,CAAC7I,WAAW,CAAC6I,UAAU;MAC9C,IAAIA,UAAU,EAAE;QACZA,UAAU,CAAC/X,KAAK,CAAC,CAAC;MACtB,CAAC,MACI;QACD,IAAI,CAACkP,WAAW,CAACuF,kBAAkB,CAAC,CAAC;MACzC;IACJ;IACA,IAAI,CAACiC,YAAY,CAACtO,IAAI,CAAC,CAAC;EAC5B;EACA;AACJ;AACA;AACA;EACIyP,iBAAiBA,CAACG,GAAG,EAAE;IACnB;IACA;IACA,IAAI,CAACvB,mBAAmB,GAAGuB,GAAG;IAC9B,IAAI,CAACzB,UAAU,EAAEsB,iBAAiB,CAACG,GAAG,CAAC;EAC3C;EACA;AACJ;AACA;AACA;EACItD,UAAUA,CAACrV,KAAK,EAAE;IACd;IACA,IAAI,CAACuF,MAAM,GAAGvF,KAAK;EACvB;EACA;AACJ;AACA;AACA;EACIsV,gBAAgBA,CAACC,EAAE,EAAE;IACjB,IAAI,CAACtB,SAAS,GAAGsB,EAAE;EACvB;EACA;AACJ;AACA;AACA;EACIC,iBAAiBA,CAACD,EAAE,EAAE;IAClB,IAAI,CAACvB,UAAU,GAAGuB,EAAE;EACxB;EACA;AACJ;AACA;AACA;EACIE,gBAAgBA,CAACC,UAAU,EAAE;IACzB,IAAI,CAAC5V,QAAQ,GAAG4V,UAAU;IAC1B,IAAI,CAAC2B,YAAY,CAACtO,IAAI,CAAC,CAAC;EAC5B;EACA;EACAsP,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAAClB,kBAAkB,CAACkB,gBAAgB,CAAC,CAAC;EAC9C;EACA;EACArD,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC,IAAI,CAAClV,QAAQ,EAAE;MAChB;MACA;MACA;MACA;MACAkJ,UAAU,CAAC,MAAM;QACb,IAAI,CAAC,IAAI,CAACyH,OAAO,EAAE;UACf,IAAI,CAACwE,iBAAiB,CAAC,CAAC;UACxB,IAAI,CAACe,cAAc,CAAC,CAAC;QACzB;MACJ,CAAC,CAAC;IACN;EACJ;EACA;AACJ;AACA;AACA;AACA;EACI1E,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC,IAAI,CAAC4F,UAAU,CAACzG,OAAO,EAAE;MAC1B,KAAK,CAACa,iBAAiB,CAAC,CAAC;IAC7B;EACJ;EACA;EACAtQ,cAAcA,CAACH,KAAK,EAAE;IAClB,MAAMI,OAAO,GAAGJ,KAAK,CAACI,OAAO;IAC7B,MAAMyX,UAAU,GAAG,IAAI,CAAC7I,WAAW,CAAC6I,UAAU;IAC9C,IAAIzX,OAAO,KAAKvH,GAAG,EAAE;MACjB,IAAI,IAAI,CAACwd,UAAU,CAACzG,OAAO,IACvB9W,cAAc,CAACkH,KAAK,EAAE,UAAU,CAAC,IACjC,IAAI,CAACyP,MAAM,CAACC,MAAM,IAClB,CAAC,IAAI,CAACD,MAAM,CAACsI,IAAI,CAAC9Y,QAAQ,EAAE;QAC5Be,KAAK,CAACC,cAAc,CAAC,CAAC;QACtB,IAAI4X,UAAU,EAAE;UACZ,IAAI,CAAC7I,WAAW,CAACgJ,aAAa,CAACH,UAAU,CAAC;QAC9C,CAAC,MACI;UACD,IAAI,CAACI,cAAc,CAAC,CAAC;QACzB;MACJ,CAAC,MACI;QACD;QACA;QACA;QACA,KAAK,CAACxH,iBAAiB,CAAC,CAAC;MAC7B;IACJ,CAAC,MACI,IAAI,CAAC,IAAI,CAAC4F,UAAU,CAACzG,OAAO,EAAE;MAC/B;MACA;MACA;MACA;MACA;MACA,IAAI,CAACxP,OAAO,KAAKrH,QAAQ,IAAIqH,OAAO,KAAKpH,UAAU,KAAK6e,UAAU,EAAE;QAChE,MAAMK,eAAe,GAAG,IAAI,CAACpI,YAAY,CAAC2D,MAAM,CAAC9L,MAAM,IAAIA,MAAM,CAAC3I,UAAU,KAAK6Y,UAAU,CAAC7Y,UAAU,IAAI,CAAC,IAAI,CAACwS,cAAc,CAAC7J,MAAM,CAAC,CAAC;QACvI,MAAMwQ,YAAY,GAAGD,eAAe,CAAC9F,OAAO,CAACyF,UAAU,CAAC;QACxD,MAAMO,KAAK,GAAGpY,KAAK,CAACI,OAAO,KAAKrH,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC;QACjDiH,KAAK,CAACC,cAAc,CAAC,CAAC;QACtB,IAAIkY,YAAY,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC5H,aAAa,CAAC4H,YAAY,GAAGC,KAAK,CAAC,EAAE;UAC/D,IAAI,CAACpJ,WAAW,CAACgJ,aAAa,CAACE,eAAe,CAACC,YAAY,GAAGC,KAAK,CAAC,CAAC;QACzE;MACJ,CAAC,MACI;QACD,KAAK,CAACjY,cAAc,CAACH,KAAK,CAAC;MAC/B;IACJ;IACA,IAAI,CAACwW,YAAY,CAACtO,IAAI,CAAC,CAAC;EAC5B;EACA+P,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAACxI,MAAM,CAACC,MAAM,EAAE;MACpB,IAAI,CAACD,MAAM,CAACsI,IAAI,CAACjY,KAAK,CAAC,CAAC;IAC5B;EACJ;EACA;EACAsU,iBAAiBA,CAAA,EAAG;IAChB,MAAMiB,WAAW,GAAG,IAAI,CAAC5F,MAAM,CAACC,MAAM,GAAG,IAAI,CAACD,MAAM,CAACyC,OAAO,CAAC,CAAC,CAACtB,GAAG,CAACzJ,IAAI,IAAIA,IAAI,CAAChI,KAAK,CAAC,GAAG,EAAE;IAC3F,IAAI,CAACuF,MAAM,GAAG2Q,WAAW;IACzB,IAAI,CAAC1D,MAAM,CAACzK,IAAI,CAAC,IAAI8O,iBAAiB,CAAC,IAAI,EAAEX,WAAW,CAAC,CAAC;IAC1D,IAAI,CAAC4B,WAAW,CAAC/P,IAAI,CAACmO,WAAW,CAAC;IAClC,IAAI,CAACjC,SAAS,CAACiC,WAAW,CAAC;IAC3B,IAAI,CAACpS,kBAAkB,CAAC0D,YAAY,CAAC,CAAC;EAC1C;EACA;EACAwO,cAAcA,CAAA,EAAG;IACb,IAAI,CAAChC,UAAU,CAAC,CAAC;IACjB,IAAI,CAAClQ,kBAAkB,CAAC0D,YAAY,CAAC,CAAC;IACtC,IAAI,CAAC6P,YAAY,CAACtO,IAAI,CAAC,CAAC;EAC5B;EACA,OAAO5H,IAAI,YAAA+X,oBAAA7X,iBAAA;IAAA,YAAAA,iBAAA,IAAwFyV,WAAW;EAAA;EAC9G,OAAO3N,IAAI,kBA1vD8ElP,EAAE,CAAAmP,iBAAA;IAAA5H,IAAA,EA0vDJsV,WAAW;IAAArV,SAAA;IAAA4H,cAAA,WAAA8P,2BAAA7b,EAAA,EAAAC,GAAA,EAAAgM,QAAA;MAAA,IAAAjM,EAAA;QA1vDTrD,EAAE,CAAAuP,cAAA,CAAAD,QAAA,EA0vD45B8E,UAAU;MAAA;MAAA,IAAA/Q,EAAA;QAAA,IAAAmM,EAAA;QA1vDx6BxP,EAAE,CAAAyP,cAAA,CAAAD,EAAA,GAAFxP,EAAE,CAAA0P,WAAA,QAAApM,GAAA,CAAA+S,MAAA,GAAA7G,EAAA;MAAA;IAAA;IAAA/H,SAAA;IAAAC,QAAA;IAAAC,YAAA,WAAAwX,yBAAA9b,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFrD,EAAE,CAAA6H,UAAA,mBAAAuX,qCAAA;UAAA,OA0vDJ9b,GAAA,CAAAoD,KAAA,CAAM,CAAC;QAAA,CAAG,CAAC,kBAAA2Y,oCAAA;UAAA,OAAX/b,GAAA,CAAAyX,KAAA,CAAM,CAAC;QAAA,CAAG,CAAC;MAAA;MAAA,IAAA1X,EAAA;QA1vDTrD,EAAE,CAAAiI,WAAA,SAAA3E,GAAA,CAAAgH,IAAA,cAAAhH,GAAA,CAAAuC,QAAA,IAAAvC,GAAA,CAAA+S,MAAA,IAAA/S,GAAA,CAAA+S,MAAA,CAAAC,MAAA,KA0vDsC,CAAC,IAAM,CAAC,GAAAhT,GAAA,CAAA0C,QAAA,mBAAlD1C,GAAA,CAAAuC,QAAA,CAAAO,QAAA,CAAkB,CAAC,kBAAA9C,GAAA,CAAAwa,UAAA;QA1vDjB9d,EAAE,CAAAkI,WAAA,+BAAA5E,GAAA,CAAAuC,QA0vDM,CAAC,8BAAAvC,GAAA,CAAAwa,UAAD,CAAC,+BAAAxa,GAAA,CAAAoX,QAAD,CAAC;MAAA;IAAA;IAAAvS,MAAA;MAAAtC,QAAA,8BAA8FzF,gBAAgB;MAAAid,WAAA;MAAA3C,QAAA,8BAAkEta,gBAAgB;MAAA2F,KAAA;MAAA4X,iBAAA;IAAA;IAAAxN,OAAA;MAAAoI,MAAA;MAAAsF,WAAA;IAAA;IAAAjV,QAAA,GA1vDzM5I,EAAE,CAAA6I,kBAAA,CA0vD+yB,CAAC;MAAEC,OAAO,EAAEjG,mBAAmB;MAAEkG,WAAW,EAAE8T;IAAY,CAAC,CAAC,GA1vD72B7c,EAAE,CAAAqJ,0BAAA;IAAAgH,kBAAA,EAAAvL,GAAA;IAAAwL,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAA6O,qBAAAjc,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAAFrD,EAAE,CAAA2Q,eAAA;QAAF3Q,EAAE,CAAAuD,cAAA,YA2vD7B,CAAC;QA3vD0BvD,EAAE,CAAAwD,YAAA,EA4vDjE,CAAC;QA5vD8DxD,EAAE,CAAAyD,YAAA,CA6vDtF,CAAC;MAAA;IAAA;IAAAsN,MAAA,GAAAhM,GAAA;IAAAiM,aAAA;IAAAC,eAAA;EAAA;AAEV;AACA;EAAA,QAAA7I,SAAA,oBAAAA,SAAA,KAhwD6FpI,EAAE,CAAAqI,iBAAA,CAgwDJwU,WAAW,EAAc,CAAC;IACzGtV,IAAI,EAAE1G,SAAS;IACfyH,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,eAAe;MAAEkI,QAAQ,EAAE;AAC1D;AACA;AACA;AACA,GAAG;MAAEjI,IAAI,EAAE;QACa,OAAO,EAAE,2DAA2D;QACpE,aAAa,EAAE,MAAM;QACrB,iBAAiB,EAAE,+DAA+D;QAClF,sBAAsB,EAAE,qBAAqB;QAC7C,qBAAqB,EAAE,YAAY;QACnC,oCAAoC,EAAE,UAAU;QAChD,mCAAmC,EAAE,YAAY;QACjD,oCAAoC,EAAE,UAAU;QAChD,SAAS,EAAE,SAAS;QACpB,QAAQ,EAAE;MACd,CAAC;MAAEQ,SAAS,EAAE,CAAC;QAAEF,OAAO,EAAEjG,mBAAmB;QAAEkG,WAAW,EAAE8T;MAAY,CAAC,CAAC;MAAE7L,aAAa,EAAElQ,iBAAiB,CAACoQ,IAAI;MAAED,eAAe,EAAElQ,uBAAuB,CAACoQ,MAAM;MAAEJ,MAAM,EAAE,CAAC,g3BAAg3B;IAAE,CAAC;EAC5iC,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAElL,QAAQ,EAAE,CAAC;MACnD0B,IAAI,EAAEhH,KAAK;MACX+H,IAAI,EAAE,CAAC;QAAEG,SAAS,EAAErI;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEid,WAAW,EAAE,CAAC;MACd9V,IAAI,EAAEhH;IACV,CAAC,CAAC;IAAEma,QAAQ,EAAE,CAAC;MACXnT,IAAI,EAAEhH,KAAK;MACX+H,IAAI,EAAE,CAAC;QAAEG,SAAS,EAAErI;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE2F,KAAK,EAAE,CAAC;MACRwB,IAAI,EAAEhH;IACV,CAAC,CAAC;IAAEod,iBAAiB,EAAE,CAAC;MACpBpW,IAAI,EAAEhH;IACV,CAAC,CAAC;IAAEgY,MAAM,EAAE,CAAC;MACThR,IAAI,EAAEtG;IACV,CAAC,CAAC;IAAE4c,WAAW,EAAE,CAAC;MACdtW,IAAI,EAAEtG;IACV,CAAC,CAAC;IAAEoV,MAAM,EAAE,CAAC;MACT9O,IAAI,EAAEvG,eAAe;MACrBsH,IAAI,EAAE,CAAC8L,UAAU,EAAE;QACX;QACA;QACA/C,WAAW,EAAE;MACjB,CAAC;IACT,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,MAAMkO,YAAY,CAAC;EACf9Z,WAAW,GAAGvF,MAAM,CAACC,UAAU,CAAC;EAChC;EACAqW,OAAO,GAAG,KAAK;EACf;EACA,IAAIgJ,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAACzZ,KAAK,EAAE;IAChB,IAAIA,KAAK,EAAE;MACP,IAAI,CAAC0Z,SAAS,GAAG1Z,KAAK;MACtB,IAAI,CAAC0Z,SAAS,CAACpB,aAAa,CAAC,IAAI,CAAC;IACtC;EACJ;EACAoB,SAAS;EACT;AACJ;AACA;EACIC,SAAS,GAAG,KAAK;EACjB;AACJ;AACA;AACA;AACA;EACIva,iBAAiB;EACjB;EACAwa,OAAO,GAAG,IAAIjf,YAAY,CAAC,CAAC;EAC5B;EACA2c,WAAW,GAAG,EAAE;EAChB;EACAtS,EAAE,GAAG7K,MAAM,CAACf,YAAY,CAAC,CAAC6L,KAAK,CAAC,0BAA0B,CAAC;EAC3D;EACA,IAAInF,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS,IAAK,IAAI,CAAC2Z,SAAS,IAAI,IAAI,CAACA,SAAS,CAAC5Z,QAAS;EACxE;EACA,IAAIA,QAAQA,CAACE,KAAK,EAAE;IAChB,IAAI,CAACD,SAAS,GAAGC,KAAK;EAC1B;EACAD,SAAS,GAAG,KAAK;EACjB;EACA,IAAIsQ,KAAKA,CAAA,EAAG;IACR,OAAO,CAAC,IAAI,CAACkI,YAAY,CAACvY,KAAK;EACnC;EACA;EACAuY,YAAY;EACZjY,WAAWA,CAAA,EAAG;IACV,MAAMuZ,cAAc,GAAG1f,MAAM,CAAC8E,yBAAyB,CAAC;IACxD,MAAM6a,SAAS,GAAG3f,MAAM,CAAC6C,cAAc,EAAE;MAAEkH,QAAQ,EAAE;IAAK,CAAC,CAAC;IAC5D,IAAI,CAACqU,YAAY,GAAG,IAAI,CAAC7Y,WAAW,CAACc,aAAa;IAClD,IAAI,CAACpB,iBAAiB,GAAGya,cAAc,CAACza,iBAAiB;IACzD,IAAI0a,SAAS,EAAE;MACX,IAAI,CAACvB,YAAY,CAAC5G,SAAS,CAACoI,GAAG,CAAC,kCAAkC,CAAC;IACvE;EACJ;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACN,SAAS,CAACrC,YAAY,CAACtO,IAAI,CAAC,CAAC;EACtC;EACApB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACiS,OAAO,CAAC3R,QAAQ,CAAC,CAAC;EAC3B;EACA;EACAgO,QAAQA,CAACpV,KAAK,EAAE;IACZ,IAAI,IAAI,CAACwP,KAAK,IAAIxP,KAAK,CAACI,OAAO,KAAKzH,SAAS,EAAE;MAC3C;MACA;MACA,IAAI,CAACqH,KAAK,CAACsH,MAAM,EAAE;QACf,IAAI,CAACuR,SAAS,CAACZ,cAAc,CAAC,CAAC;MACnC;MACAjY,KAAK,CAACC,cAAc,CAAC,CAAC;IAC1B,CAAC,MACI;MACD,IAAI,CAACmZ,YAAY,CAACpZ,KAAK,CAAC;IAC5B;EACJ;EACA;EACAmU,KAAKA,CAAA,EAAG;IACJ,IAAI,IAAI,CAAC2E,SAAS,EAAE;MAChB,IAAI,CAACM,YAAY,CAAC,CAAC;IACvB;IACA,IAAI,CAACxJ,OAAO,GAAG,KAAK;IACpB;IACA,IAAI,CAAC,IAAI,CAACiJ,SAAS,CAACjJ,OAAO,EAAE;MACzB,IAAI,CAACiJ,SAAS,CAAC1E,KAAK,CAAC,CAAC;IAC1B;IACA,IAAI,CAAC0E,SAAS,CAACrC,YAAY,CAACtO,IAAI,CAAC,CAAC;EACtC;EACAmR,MAAMA,CAAA,EAAG;IACL,IAAI,CAACzJ,OAAO,GAAG,IAAI;IACnB,IAAI,CAACiJ,SAAS,CAACrC,YAAY,CAACtO,IAAI,CAAC,CAAC;EACtC;EACA;EACAkR,YAAYA,CAACpZ,KAAK,EAAE;IAChB,IAAI,CAACA,KAAK,IAAK,IAAI,CAACsZ,eAAe,CAACtZ,KAAK,CAAC,IAAI,CAACA,KAAK,CAACsH,MAAO,EAAE;MAC1D,IAAI,CAACyR,OAAO,CAAC7R,IAAI,CAAC;QACdqS,KAAK,EAAE,IAAI,CAAC7B,YAAY;QACxBvY,KAAK,EAAE,IAAI,CAACuY,YAAY,CAACvY,KAAK;QAC9Bqa,SAAS,EAAE;MACf,CAAC,CAAC;MACFxZ,KAAK,EAAEC,cAAc,CAAC,CAAC;IAC3B;EACJ;EACAwZ,QAAQA,CAAA,EAAG;IACP;IACA,IAAI,CAACZ,SAAS,CAACrC,YAAY,CAACtO,IAAI,CAAC,CAAC;EACtC;EACA;EACApI,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC4X,YAAY,CAAC5X,KAAK,CAAC,CAAC;EAC7B;EACA;EACA4Z,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAChC,YAAY,CAACvY,KAAK,GAAG,EAAE;EAChC;EACAwY,iBAAiBA,CAACG,GAAG,EAAE;IACnB,MAAM5R,OAAO,GAAG,IAAI,CAACrH,WAAW,CAACc,aAAa;IAC9C;IACA;IACA,IAAImY,GAAG,CAACpI,MAAM,EAAE;MACZxJ,OAAO,CAACrG,YAAY,CAAC,kBAAkB,EAAEiY,GAAG,CAAC6B,IAAI,CAAC,GAAG,CAAC,CAAC;IAC3D,CAAC,MACI;MACDzT,OAAO,CAAC0T,eAAe,CAAC,kBAAkB,CAAC;IAC/C;EACJ;EACA;EACAN,eAAeA,CAACtZ,KAAK,EAAE;IACnB,OAAO,CAAClH,cAAc,CAACkH,KAAK,CAAC,IAAI,IAAI6Z,GAAG,CAAC,IAAI,CAACtb,iBAAiB,CAAC,CAACub,GAAG,CAAC9Z,KAAK,CAACI,OAAO,CAAC;EACvF;EACA,OAAOE,IAAI,YAAAyZ,qBAAAvZ,iBAAA;IAAA,YAAAA,iBAAA,IAAwFmY,YAAY;EAAA;EAC/G,OAAOlY,IAAI,kBAh7D8ErH,EAAE,CAAAsH,iBAAA;IAAAC,IAAA,EAg7DJgY,YAAY;IAAA/X,SAAA;IAAAC,SAAA;IAAAC,QAAA;IAAAC,YAAA,WAAAiZ,0BAAAvd,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QAh7DVrD,EAAE,CAAA6H,UAAA,qBAAAgZ,wCAAA9Y,MAAA;UAAA,OAg7DJzE,GAAA,CAAA0Y,QAAA,CAAAjU,MAAe,CAAC;QAAA,CAAL,CAAC,kBAAA+Y,qCAAA;UAAA,OAAZxd,GAAA,CAAAyX,KAAA,CAAM,CAAC;QAAA,CAAI,CAAC,mBAAAgG,sCAAA;UAAA,OAAZzd,GAAA,CAAA2c,MAAA,CAAO,CAAC;QAAA,CAAG,CAAC,mBAAAe,sCAAA;UAAA,OAAZ1d,GAAA,CAAA+c,QAAA,CAAS,CAAC;QAAA,CAAC,CAAC;MAAA;MAAA,IAAAhd,EAAA;QAh7DVrD,EAAE,CAAAiQ,cAAA,OAAA3M,GAAA,CAAAyH,EAg7DO,CAAC;QAh7DV/K,EAAE,CAAAiI,WAAA,aAAA3E,GAAA,CAAAuC,QAAA,IAg7DQ,IAAI,iBAAAvC,GAAA,CAAA+Z,WAAA,IAAD,IAAI,kBAAA/Z,GAAA,CAAAmc,SAAA,IAAAnc,GAAA,CAAAmc,SAAA,CAAA3C,SAAA,GAAAxZ,GAAA,CAAAmc,SAAA,CAAA3C,SAAA,CAAAmE,OAAA,GAA8C,IAAI,mBAAA3d,GAAA,CAAAmc,SAAA,IAAAnc,GAAA,CAAAmc,SAAA,CAAA/E,QAAA,IAAlC,IAAI,cAAApX,GAAA,CAAAmc,SAAA,IAAAnc,GAAA,CAAAmc,SAAA,CAAA/E,QAAA,IAAJ,IAAI;MAAA;IAAA;IAAAvS,MAAA;MAAAqX,QAAA;MAAAE,SAAA,4CAAsItf,gBAAgB;MAAA+E,iBAAA;MAAAkY,WAAA;MAAAtS,EAAA;MAAAlF,QAAA,8BAAuJzF,gBAAgB;IAAA;IAAA+P,OAAA;MAAAwP,OAAA;IAAA;IAAAvP,QAAA;IAAAxH,QAAA,GAh7DlW5I,EAAE,CAAAkhB,oBAAA;EAAA;AAi7D/F;AACA;EAAA,QAAA9Y,SAAA,oBAAAA,SAAA,KAl7D6FpI,EAAE,CAAAqI,iBAAA,CAk7DJkX,YAAY,EAAc,CAAC;IAC1GhY,IAAI,EAAEjH,SAAS;IACfgI,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,wBAAwB;MAClC6H,QAAQ,EAAE,+BAA+B;MACzC5H,IAAI,EAAE;QACF;QACA;QACA;QACA,OAAO,EAAE,kFAAkF;QAC3F,WAAW,EAAE,kBAAkB;QAC/B,QAAQ,EAAE,SAAS;QACnB,SAAS,EAAE,UAAU;QACrB,SAAS,EAAE,YAAY;QACvB,MAAM,EAAE,IAAI;QACZ,iBAAiB,EAAE,kBAAkB;QACrC,oBAAoB,EAAE,qBAAqB;QAC3C,qBAAqB,EAAE,uEAAuE;QAC9F,sBAAsB,EAAE,yCAAyC;QACjE,iBAAiB,EAAE;MACvB;IACJ,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE,EAAkB;IAAEgX,QAAQ,EAAE,CAAC;MACnDjY,IAAI,EAAEhH,KAAK;MACX+H,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAEoX,SAAS,EAAE,CAAC;MACZnY,IAAI,EAAEhH,KAAK;MACX+H,IAAI,EAAE,CAAC;QAAE6Y,KAAK,EAAE,uBAAuB;QAAE1Y,SAAS,EAAErI;MAAiB,CAAC;IAC1E,CAAC,CAAC;IAAE+E,iBAAiB,EAAE,CAAC;MACpBoC,IAAI,EAAEhH,KAAK;MACX+H,IAAI,EAAE,CAAC,+BAA+B;IAC1C,CAAC,CAAC;IAAEqX,OAAO,EAAE,CAAC;MACVpY,IAAI,EAAEtG,MAAM;MACZqH,IAAI,EAAE,CAAC,sBAAsB;IACjC,CAAC,CAAC;IAAE+U,WAAW,EAAE,CAAC;MACd9V,IAAI,EAAEhH;IACV,CAAC,CAAC;IAAEwK,EAAE,EAAE,CAAC;MACLxD,IAAI,EAAEhH;IACV,CAAC,CAAC;IAAEsF,QAAQ,EAAE,CAAC;MACX0B,IAAI,EAAEhH,KAAK;MACX+H,IAAI,EAAE,CAAC;QAAEG,SAAS,EAAErI;MAAiB,CAAC;IAC1C,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMghB,iBAAiB,GAAG,CACtBxX,OAAO,EACPlB,aAAa,EACbyK,gBAAgB,EAChB0J,WAAW,EACX0C,YAAY,EACZ1F,cAAc,EACdnI,aAAa,EACbpI,aAAa,EACb8K,UAAU,EACVqB,UAAU,EACVxM,mBAAmB,CACtB;AACD,MAAMoY,cAAc,CAAC;EACjB,OAAOna,IAAI,YAAAoa,uBAAAla,iBAAA;IAAA,YAAAA,iBAAA,IAAwFia,cAAc;EAAA;EACjH,OAAOE,IAAI,kBA5+D8EvhB,EAAE,CAAAwhB,gBAAA;IAAAja,IAAA,EA4+DS8Z;EAAc;EAqBlH,OAAOI,IAAI,kBAjgE8EzhB,EAAE,CAAA0hB,gBAAA;IAAA1Y,SAAA,EAigEoC,CACvHtG,iBAAiB,EACjB;MACIoG,OAAO,EAAE9D,yBAAyB;MAClC2c,QAAQ,EAAE;QACNxc,iBAAiB,EAAE,CAAC9F,KAAK;MAC7B;IACJ,CAAC,CACJ;IAAA+R,OAAA,GAAYpO,eAAe,EAAEC,eAAe,EAAED,eAAe;EAAA;AACtE;AACA;EAAA,QAAAoF,SAAA,oBAAAA,SAAA,KA3gE6FpI,EAAE,CAAAqI,iBAAA,CA2gEJgZ,cAAc,EAAc,CAAC;IAC5G9Z,IAAI,EAAEhG,QAAQ;IACd+G,IAAI,EAAE,CAAC;MACC8I,OAAO,EAAE,CAACpO,eAAe,EAAEC,eAAe,EAAEuC,aAAa,EAAE4b,iBAAiB,CAAC;MAC7EQ,OAAO,EAAE,CAAC5e,eAAe,EAAEoe,iBAAiB,CAAC;MAC7CpY,SAAS,EAAE,CACPtG,iBAAiB,EACjB;QACIoG,OAAO,EAAE9D,yBAAyB;QAClC2c,QAAQ,EAAE;UACNxc,iBAAiB,EAAE,CAAC9F,KAAK;QAC7B;MACJ,CAAC;IAET,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,SAASkG,QAAQ,EAAEP,yBAAyB,EAAEI,eAAe,EAAEwU,uCAAuC,EAAEtU,eAAe,EAAED,sBAAsB,EAAEuE,OAAO,EAAElB,aAAa,EAAEyK,gBAAgB,EAAE0J,WAAW,EAAED,iBAAiB,EAAE2C,YAAY,EAAE1F,cAAc,EAAEF,oBAAoB,EAAEjI,aAAa,EAAEpI,aAAa,EAAE8K,UAAU,EAAE9C,sBAAsB,EAAEmE,UAAU,EAAExM,mBAAmB,EAAEoY,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}