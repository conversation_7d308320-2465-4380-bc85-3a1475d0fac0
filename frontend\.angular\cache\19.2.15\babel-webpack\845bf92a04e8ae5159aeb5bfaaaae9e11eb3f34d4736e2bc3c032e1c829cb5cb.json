{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/material/tabs\";\nimport * as i4 from \"@angular/material/card\";\nimport * as i5 from \"@angular/material/icon\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/expansion\";\nfunction GettingStartedComponent_mat_card_16_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"strong\");\n    i0.ɵɵtext(2, \"Installation:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"code\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const req_r1 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(req_r1.installCommand);\n  }\n}\nfunction GettingStartedComponent_mat_card_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 26)(1, \"mat-card-header\")(2, \"mat-icon\", 13);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-card-title\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-card-subtitle\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"mat-card-content\")(9, \"p\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, GettingStartedComponent_mat_card_16_div_11_Template, 5, 1, \"div\", 27);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const req_r1 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"background-color\", req_r1.color);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", req_r1.icon, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(req_r1.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(req_r1.version);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(req_r1.description);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", req_r1.installCommand);\n  }\n}\nfunction GettingStartedComponent_mat_expansion_panel_22_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"div\", 15)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"terminal\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"Commands\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"pre\")(7, \"code\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const step_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(step_r2.commands);\n  }\n}\nfunction GettingStartedComponent_mat_expansion_panel_22_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const step_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(step_r2.notes);\n  }\n}\nfunction GettingStartedComponent_mat_expansion_panel_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-expansion-panel\", 29)(1, \"mat-expansion-panel-header\")(2, \"mat-panel-title\")(3, \"span\", 30);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-panel-description\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 31)(9, \"p\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, GettingStartedComponent_mat_expansion_panel_22_div_11_Template, 9, 1, \"div\", 32)(12, GettingStartedComponent_mat_expansion_panel_22_div_12_Template, 5, 1, \"div\", 33);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const step_r2 = ctx.$implicit;\n    const i_r3 = ctx.index;\n    i0.ɵɵproperty(\"expanded\", i_r3 === 0);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(i_r3 + 1);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", step_r2.title, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", step_r2.description, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(step_r2.details);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", step_r2.commands);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", step_r2.notes);\n  }\n}\nfunction GettingStartedComponent_div_72_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"pre\")(2, \"code\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const step_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(step_r4.command);\n  }\n}\nfunction GettingStartedComponent_div_72_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"arrow_downward\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction GettingStartedComponent_div_72_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"div\", 36)(2, \"div\", 37);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h3\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, GettingStartedComponent_div_72_div_8_Template, 4, 1, \"div\", 32)(9, GettingStartedComponent_div_72_div_9_Template, 3, 0, \"div\", 38);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const step_r4 = ctx.$implicit;\n    const i_r5 = ctx.index;\n    const ctx_r5 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i_r5 + 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(step_r4.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(step_r4.description);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", step_r4.command);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r5 < ctx_r5.quickStartFlow.length - 1);\n  }\n}\nexport class GettingStartedComponent {\n  constructor() {\n    this.prerequisites = [{\n      name: 'Go',\n      version: '1.21+',\n      description: 'Required for the backend services and CLI tool',\n      icon: 'code',\n      color: '#00ADD8',\n      installCommand: 'https://golang.org/doc/install'\n    }, {\n      name: 'Node.js',\n      version: '18+',\n      description: 'Required for the frontend dashboard and development tools',\n      icon: 'javascript',\n      color: '#339933',\n      installCommand: 'https://nodejs.org/'\n    }, {\n      name: 'Angular CLI',\n      version: 'Latest',\n      description: 'Required for frontend development and building',\n      icon: 'web',\n      color: '#DD0031',\n      installCommand: 'npm install -g @angular/cli'\n    }, {\n      name: 'Git',\n      version: 'Latest',\n      description: 'Required for version control and repository management',\n      icon: 'source',\n      color: '#F05032',\n      installCommand: 'https://git-scm.com/downloads'\n    }];\n    this.installationSteps = [{\n      title: 'Clone Repository',\n      description: 'Get the SPT source code',\n      details: 'Clone the SPT repository from GitHub to your local development environment.',\n      commands: 'git clone https://github.com/blockchain-spt/spt.git\\ncd spt',\n      notes: 'Make sure you have Git installed and configured'\n    }, {\n      title: 'Backend Setup',\n      description: 'Install Go dependencies and start backend',\n      details: 'Navigate to the backend directory, install dependencies, and start the Go server.',\n      commands: 'cd backend\\ngo mod tidy\\ngo run cmd/main.go',\n      notes: 'Backend will start on port 8080 by default'\n    }, {\n      title: 'Frontend Setup',\n      description: 'Install Node.js dependencies and start frontend',\n      details: 'Navigate to the frontend directory, install npm packages, and start the Angular development server.',\n      commands: 'cd frontend\\nnpm install\\nnpm start',\n      notes: 'Frontend will start on port 4200 by default'\n    }, {\n      title: 'CLI Tool (Optional)',\n      description: 'Build the command-line interface',\n      details: 'Build the SPT CLI tool for command-line security scanning.',\n      commands: 'make cli\\n# or\\ngo build -o spt cmd/main.go',\n      notes: 'CLI tool will be available as ./spt'\n    }];\n    this.quickStartFlow = [{\n      title: 'Clone & Navigate',\n      description: 'Get the source code and navigate to the project directory',\n      command: 'git clone https://github.com/blockchain-spt/spt.git\\ncd spt'\n    }, {\n      title: 'Start Backend',\n      description: 'Launch the Go backend server with all APIs',\n      command: 'cd backend && go run cmd/main.go'\n    }, {\n      title: 'Start Frontend',\n      description: 'Launch the Angular development server in a new terminal',\n      command: 'cd frontend && npm install && npm start'\n    }, {\n      title: 'Access Application',\n      description: 'Open your browser and start using SPT',\n      command: 'Open http://localhost:4200 in your browser'\n    }];\n    this.backendConfig = `{\n  \"environment\": \"development\",\n  \"server\": {\n    \"port\": 8080,\n    \"host\": \"localhost\"\n  },\n  \"database\": {\n    \"type\": \"sqlite\",\n    \"database\": \"spt.db\"\n  },\n  \"security\": {\n    \"jwt_secret\": \"your-secret-key\",\n    \"cors_enabled\": true\n  },\n  \"scanning\": {\n    \"max_concurrent_scans\": 5,\n    \"timeout_seconds\": 300\n  }\n}`;\n    this.frontendConfig = `# Frontend Environment Variables\nAPI_URL=http://localhost:8080\nWS_URL=ws://localhost:8080/ws\nENVIRONMENT=development\nDEBUG=true`;\n  }\n  static {\n    this.ɵfac = function GettingStartedComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || GettingStartedComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: GettingStartedComponent,\n      selectors: [[\"app-getting-started\"]],\n      decls: 109,\n      vars: 5,\n      consts: [[1, \"getting-started-container\"], [1, \"page-header\"], [1, \"page-subtitle\"], [\"animationDuration\", \"300ms\", 1, \"content-tabs\"], [\"label\", \"Prerequisites\"], [1, \"tab-content\"], [1, \"requirements-grid\"], [\"class\", \"requirement-card\", 4, \"ngFor\", \"ngForOf\"], [\"label\", \"Installation\"], [1, \"installation-steps\"], [3, \"expanded\", 4, \"ngFor\", \"ngForOf\"], [\"label\", \"Configuration\"], [1, \"config-card\"], [\"mat-card-avatar\", \"\"], [1, \"code-block\"], [1, \"code-header\"], [\"label\", \"Quick Start\"], [1, \"quick-start-flow\"], [\"class\", \"flow-step\", 4, \"ngFor\", \"ngForOf\"], [1, \"success-card\"], [\"mat-card-avatar\", \"\", 2, \"background-color\", \"#4caf50\"], [\"href\", \"http://localhost:4200\", \"target\", \"_blank\"], [\"href\", \"http://localhost:8080\", \"target\", \"_blank\"], [\"href\", \"http://localhost:8080/health\", \"target\", \"_blank\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/doc/api-reference\"], [\"mat-stroked-button\", \"\", \"routerLink\", \"/doc/security-practices\"], [1, \"requirement-card\"], [\"class\", \"install-command\", 4, \"ngIf\"], [1, \"install-command\"], [3, \"expanded\"], [1, \"step-number\"], [1, \"step-content\"], [\"class\", \"code-block\", 4, \"ngIf\"], [\"class\", \"step-notes\", 4, \"ngIf\"], [1, \"step-notes\"], [1, \"flow-step\"], [1, \"flow-step-header\"], [1, \"flow-step-number\"], [\"class\", \"flow-arrow\", 4, \"ngIf\"], [1, \"flow-arrow\"]],\n      template: function GettingStartedComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\")(3, \"mat-icon\");\n          i0.ɵɵtext(4, \"play_arrow\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(5, \" Getting Started \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p\", 2);\n          i0.ɵɵtext(7, \" Get SPT up and running in your development environment \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"mat-tab-group\", 3)(9, \"mat-tab\", 4)(10, \"div\", 5)(11, \"h2\");\n          i0.ɵɵtext(12, \"System Requirements\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"p\");\n          i0.ɵɵtext(14, \"Before installing SPT, ensure you have the following prerequisites:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"div\", 6);\n          i0.ɵɵtemplate(16, GettingStartedComponent_mat_card_16_Template, 12, 7, \"mat-card\", 7);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(17, \"mat-tab\", 8)(18, \"div\", 5)(19, \"h2\");\n          i0.ɵɵtext(20, \"Installation Steps\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"mat-accordion\", 9);\n          i0.ɵɵtemplate(22, GettingStartedComponent_mat_expansion_panel_22_Template, 13, 7, \"mat-expansion-panel\", 10);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(23, \"mat-tab\", 11)(24, \"div\", 5)(25, \"h2\");\n          i0.ɵɵtext(26, \"Configuration\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"p\");\n          i0.ɵɵtext(28, \"Configure SPT for your development environment:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(29, \"mat-card\", 12)(30, \"mat-card-header\")(31, \"mat-icon\", 13);\n          i0.ɵɵtext(32, \"settings\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"mat-card-title\");\n          i0.ɵɵtext(34, \"Backend Configuration\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"mat-card-subtitle\");\n          i0.ɵɵtext(36, \"spt.config.json\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(37, \"mat-card-content\")(38, \"div\", 14)(39, \"div\", 15)(40, \"mat-icon\");\n          i0.ɵɵtext(41, \"code\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"span\");\n          i0.ɵɵtext(43, \"Configuration File\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(44, \"pre\")(45, \"code\");\n          i0.ɵɵtext(46);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(47, \"mat-card\", 12)(48, \"mat-card-header\")(49, \"mat-icon\", 13);\n          i0.ɵɵtext(50, \"web\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"mat-card-title\");\n          i0.ɵɵtext(52, \"Frontend Configuration\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(53, \"mat-card-subtitle\");\n          i0.ɵɵtext(54, \"Environment Variables\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(55, \"mat-card-content\")(56, \"div\", 14)(57, \"div\", 15)(58, \"mat-icon\");\n          i0.ɵɵtext(59, \"code\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(60, \"span\");\n          i0.ɵɵtext(61, \"Environment File\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(62, \"pre\")(63, \"code\");\n          i0.ɵɵtext(64);\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵelementStart(65, \"mat-tab\", 16)(66, \"div\", 5)(67, \"h2\");\n          i0.ɵɵtext(68, \"Quick Start Guide\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"p\");\n          i0.ɵɵtext(70, \"Get SPT running with these simple commands:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"div\", 17);\n          i0.ɵɵtemplate(72, GettingStartedComponent_div_72_Template, 10, 5, \"div\", 18);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(73, \"mat-card\", 19)(74, \"mat-card-header\")(75, \"mat-icon\", 20);\n          i0.ɵɵtext(76, \"check_circle\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(77, \"mat-card-title\");\n          i0.ɵɵtext(78, \"Success!\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(79, \"mat-card-subtitle\");\n          i0.ɵɵtext(80, \"SPT is now running\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(81, \"mat-card-content\")(82, \"p\");\n          i0.ɵɵtext(83, \"You can now access:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(84, \"ul\")(85, \"li\")(86, \"strong\");\n          i0.ɵɵtext(87, \"Web Dashboard:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(88, \"a\", 21);\n          i0.ɵɵtext(89, \"http://localhost:4200\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(90, \"li\")(91, \"strong\");\n          i0.ɵɵtext(92, \"API Server:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(93, \"a\", 22);\n          i0.ɵɵtext(94, \"http://localhost:8080\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(95, \"li\")(96, \"strong\");\n          i0.ɵɵtext(97, \"Health Check:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(98, \"a\", 23);\n          i0.ɵɵtext(99, \"http://localhost:8080/health\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(100, \"mat-card-actions\")(101, \"button\", 24)(102, \"mat-icon\");\n          i0.ɵɵtext(103, \"api\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(104, \" Explore API \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(105, \"button\", 25)(106, \"mat-icon\");\n          i0.ɵɵtext(107, \"security\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(108, \" Security Guide \");\n          i0.ɵɵelementEnd()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(16);\n          i0.ɵɵproperty(\"ngForOf\", ctx.prerequisites);\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngForOf\", ctx.installationSteps);\n          i0.ɵɵadvance(24);\n          i0.ɵɵtextInterpolate(ctx.backendConfig);\n          i0.ɵɵadvance(18);\n          i0.ɵɵtextInterpolate(ctx.frontendConfig);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngForOf\", ctx.quickStartFlow);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.NgIf, RouterModule, i2.RouterLink, MatTabsModule, i3.MatTab, i3.MatTabGroup, MatCardModule, i4.MatCard, i4.MatCardActions, i4.MatCardAvatar, i4.MatCardContent, i4.MatCardHeader, i4.MatCardSubtitle, i4.MatCardTitle, MatIconModule, i5.MatIcon, MatButtonModule, i6.MatButton, MatExpansionModule, i7.MatAccordion, i7.MatExpansionPanel, i7.MatExpansionPanelHeader, i7.MatExpansionPanelTitle, i7.MatExpansionPanelDescription],\n      styles: [\".getting-started-container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  padding: 48px;\\n  background: #ffffff;\\n}\\n\\n.page-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 48px;\\n  padding: 32px 0;\\n}\\n\\n.page-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 16px;\\n  color: #1a202c;\\n  margin: 0 0 12px 0;\\n  font-size: 2.5em;\\n  font-weight: 800;\\n  letter-spacing: -1px;\\n}\\n\\n.page-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 48px;\\n  width: 48px;\\n  height: 48px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: #667eea;\\n}\\n\\n.page-subtitle[_ngcontent-%COMP%] {\\n  color: #64748b;\\n  font-size: 1.25em;\\n  margin: 0;\\n  font-weight: 500;\\n}\\n\\n.content-tabs[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n\\n.tab-content[_ngcontent-%COMP%] {\\n  padding: 24px 0;\\n}\\n\\n.requirements-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));\\n  gap: 32px;\\n  margin-top: 32px;\\n}\\n\\n.install-command[_ngcontent-%COMP%] {\\n  margin-top: 12px;\\n  padding: 8px;\\n  background: #f5f5f5;\\n  border-radius: 4px;\\n}\\n\\n.installation-steps[_ngcontent-%COMP%] {\\n  margin-top: 24px;\\n}\\n\\n.step-number[_ngcontent-%COMP%] {\\n  background: #1976d2;\\n  color: white;\\n  border-radius: 50%;\\n  width: 24px;\\n  height: 24px;\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 0.8em;\\n  font-weight: bold;\\n  margin-right: 12px;\\n}\\n\\n.step-content[_ngcontent-%COMP%] {\\n  padding: 16px 0;\\n}\\n\\n.code-block[_ngcontent-%COMP%] {\\n  margin: 16px 0;\\n  border: 1px solid #e0e0e0;\\n  border-radius: 8px;\\n  overflow: hidden;\\n}\\n\\n.code-header[_ngcontent-%COMP%] {\\n  background: #f5f5f5;\\n  padding: 8px 16px;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-weight: 500;\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n\\n.code-block[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding: 16px;\\n  background: #fafafa;\\n  overflow-x: auto;\\n}\\n\\n.code-block[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  font-family: \\\"Courier New\\\", monospace;\\n  font-size: 0.9em;\\n}\\n\\n.step-notes[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-top: 12px;\\n  padding: 8px 12px;\\n  background: #e3f2fd;\\n  border-radius: 4px;\\n  color: #1976d2;\\n}\\n\\n.config-card[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n\\n.quick-start-flow[_ngcontent-%COMP%] {\\n  margin: 24px 0;\\n}\\n\\n.flow-step[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n  position: relative;\\n}\\n\\n.flow-step-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n  margin-bottom: 12px;\\n}\\n\\n.flow-step-number[_ngcontent-%COMP%] {\\n  background: #1976d2;\\n  color: white;\\n  border-radius: 50%;\\n  width: 40px;\\n  height: 40px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-weight: bold;\\n  font-size: 1.1em;\\n}\\n\\n.flow-step[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #1976d2;\\n}\\n\\n.flow-arrow[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin: 16px 0;\\n  color: #1976d2;\\n}\\n\\n.success-card[_ngcontent-%COMP%] {\\n  margin-top: 32px;\\n  border: 2px solid #4caf50;\\n}\\n\\n.success-card[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  margin: 16px 0;\\n}\\n\\n.success-card[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n  text-decoration: none;\\n}\\n\\n.success-card[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n\\n\\nmat-icon[_ngcontent-%COMP%] {\\n  display: inline-flex !important;\\n  align-items: center !important;\\n  justify-content: center !important;\\n  vertical-align: middle !important;\\n  line-height: 1 !important;\\n}\\n\\n.code-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.code-header[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n}\\n\\n.step-notes[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 8px;\\n  margin-top: 12px;\\n  padding: 12px;\\n  background: #f0f9ff;\\n  border-radius: 8px;\\n  border-left: 4px solid #0ea5e9;\\n}\\n\\n.step-notes[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #0ea5e9;\\n  font-size: 18px;\\n  width: 18px;\\n  height: 18px;\\n  margin-top: 2px;\\n  flex-shrink: 0;\\n}\\n\\n.flow-arrow[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin: 16px 0;\\n}\\n\\n.flow-arrow[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #667eea;\\n  font-size: 24px;\\n  width: 24px;\\n  height: 24px;\\n}\\n\\nmat-card-header[_ngcontent-%COMP%]   .mat-mdc-card-avatar[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 20px !important;\\n  width: 20px !important;\\n  height: 20px !important;\\n  line-height: 1 !important;\\n}\\n\\n@media (max-width: 768px) {\\n  .requirements-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .flow-step-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 8px;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbW9kdWxlcy9kb2N1bWVudGF0aW9uL2NvbXBvbmVudHMvZ2V0dGluZy1zdGFydGVkL2dldHRpbmctc3RhcnRlZC5jb21wb25lbnQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQ0k7RUFDRSxpQkFBQTtFQUNBLGNBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7QUFBTjs7QUFHSTtFQUNFLGtCQUFBO0VBQ0EsbUJBQUE7RUFDQSxlQUFBO0FBQU47O0FBR0k7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLFNBQUE7RUFDQSxjQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtFQUNBLGdCQUFBO0VBQ0Esb0JBQUE7QUFBTjs7QUFHSTtFQUNFLGVBQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLHVCQUFBO0VBQ0EsY0FBQTtBQUFOOztBQUdJO0VBQ0UsY0FBQTtFQUNBLGlCQUFBO0VBQ0EsU0FBQTtFQUNBLGdCQUFBO0FBQU47O0FBR0k7RUFDRSxtQkFBQTtBQUFOOztBQUdJO0VBQ0UsZUFBQTtBQUFOOztBQUdJO0VBQ0UsYUFBQTtFQUNBLDJEQUFBO0VBQ0EsU0FBQTtFQUNBLGdCQUFBO0FBQU47O0FBR0k7RUFDRSxnQkFBQTtFQUNBLFlBQUE7RUFDQSxtQkFBQTtFQUNBLGtCQUFBO0FBQU47O0FBR0k7RUFDRSxnQkFBQTtBQUFOOztBQUdJO0VBQ0UsbUJBQUE7RUFDQSxZQUFBO0VBQ0Esa0JBQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtFQUNBLG9CQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLGdCQUFBO0VBQ0EsaUJBQUE7RUFDQSxrQkFBQTtBQUFOOztBQUdJO0VBQ0UsZUFBQTtBQUFOOztBQUdJO0VBQ0UsY0FBQTtFQUNBLHlCQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtBQUFOOztBQUdJO0VBQ0UsbUJBQUE7RUFDQSxpQkFBQTtFQUNBLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFFBQUE7RUFDQSxnQkFBQTtFQUNBLGdDQUFBO0FBQU47O0FBR0k7RUFDRSxTQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0EsZ0JBQUE7QUFBTjs7QUFHSTtFQUNFLHFDQUFBO0VBQ0EsZ0JBQUE7QUFBTjs7QUFHSTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFFBQUE7RUFDQSxnQkFBQTtFQUNBLGlCQUFBO0VBQ0EsbUJBQUE7RUFDQSxrQkFBQTtFQUNBLGNBQUE7QUFBTjs7QUFHSTtFQUNFLG1CQUFBO0FBQU47O0FBR0k7RUFDRSxjQUFBO0FBQU47O0FBR0k7RUFDRSxtQkFBQTtFQUNBLGtCQUFBO0FBQU47O0FBR0k7RUFDRSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSxTQUFBO0VBQ0EsbUJBQUE7QUFBTjs7QUFHSTtFQUNFLG1CQUFBO0VBQ0EsWUFBQTtFQUNBLGtCQUFBO0VBQ0EsV0FBQTtFQUNBLFlBQUE7RUFDQSxhQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLGlCQUFBO0VBQ0EsZ0JBQUE7QUFBTjs7QUFHSTtFQUNFLFNBQUE7RUFDQSxjQUFBO0FBQU47O0FBR0k7RUFDRSxrQkFBQTtFQUNBLGNBQUE7RUFDQSxjQUFBO0FBQU47O0FBR0k7RUFDRSxnQkFBQTtFQUNBLHlCQUFBO0FBQU47O0FBR0k7RUFDRSxjQUFBO0FBQU47O0FBR0k7RUFDRSxjQUFBO0VBQ0EscUJBQUE7QUFBTjs7QUFHSTtFQUNFLDBCQUFBO0FBQU47O0FBR0ksa0NBQUE7QUFDQTtFQUNFLCtCQUFBO0VBQ0EsOEJBQUE7RUFDQSxrQ0FBQTtFQUNBLGlDQUFBO0VBQ0EseUJBQUE7QUFBTjs7QUFHSTtFQUNFLGFBQUE7RUFDQSxtQkFBQTtFQUNBLFFBQUE7QUFBTjs7QUFHSTtFQUNFLGVBQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtBQUFOOztBQUdJO0VBQ0UsYUFBQTtFQUNBLHVCQUFBO0VBQ0EsUUFBQTtFQUNBLGdCQUFBO0VBQ0EsYUFBQTtFQUNBLG1CQUFBO0VBQ0Esa0JBQUE7RUFDQSw4QkFBQTtBQUFOOztBQUdJO0VBQ0UsY0FBQTtFQUNBLGVBQUE7RUFDQSxXQUFBO0VBQ0EsWUFBQTtFQUNBLGVBQUE7RUFDQSxjQUFBO0FBQU47O0FBR0k7RUFDRSxrQkFBQTtFQUNBLGNBQUE7QUFBTjs7QUFHSTtFQUNFLGNBQUE7RUFDQSxlQUFBO0VBQ0EsV0FBQTtFQUNBLFlBQUE7QUFBTjs7QUFHSTtFQUNFLDBCQUFBO0VBQ0Esc0JBQUE7RUFDQSx1QkFBQTtFQUNBLHlCQUFBO0FBQU47O0FBR0k7RUFDRTtJQUNFLDBCQUFBO0VBQU47RUFHSTtJQUNFLHNCQUFBO0lBQ0EsdUJBQUE7SUFDQSxRQUFBO0VBRE47QUFDRiIsInNvdXJjZXNDb250ZW50IjpbIlxuICAgIC5nZXR0aW5nLXN0YXJ0ZWQtY29udGFpbmVyIHtcbiAgICAgIG1heC13aWR0aDogMTIwMHB4O1xuICAgICAgbWFyZ2luOiAwIGF1dG87XG4gICAgICBwYWRkaW5nOiA0OHB4O1xuICAgICAgYmFja2dyb3VuZDogI2ZmZmZmZjtcbiAgICB9XG5cbiAgICAucGFnZS1oZWFkZXIge1xuICAgICAgdGV4dC1hbGlnbjogY2VudGVyO1xuICAgICAgbWFyZ2luLWJvdHRvbTogNDhweDtcbiAgICAgIHBhZGRpbmc6IDMycHggMDtcbiAgICB9XG5cbiAgICAucGFnZS1oZWFkZXIgaDEge1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgICAgIGdhcDogMTZweDtcbiAgICAgIGNvbG9yOiAjMWEyMDJjO1xuICAgICAgbWFyZ2luOiAwIDAgMTJweCAwO1xuICAgICAgZm9udC1zaXplOiAyLjVlbTtcbiAgICAgIGZvbnQtd2VpZ2h0OiA4MDA7XG4gICAgICBsZXR0ZXItc3BhY2luZzogLTFweDtcbiAgICB9XG5cbiAgICAucGFnZS1oZWFkZXIgaDEgbWF0LWljb24ge1xuICAgICAgZm9udC1zaXplOiA0OHB4O1xuICAgICAgd2lkdGg6IDQ4cHg7XG4gICAgICBoZWlnaHQ6IDQ4cHg7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICAgICAgY29sb3I6ICM2NjdlZWE7XG4gICAgfVxuXG4gICAgLnBhZ2Utc3VidGl0bGUge1xuICAgICAgY29sb3I6ICM2NDc0OGI7XG4gICAgICBmb250LXNpemU6IDEuMjVlbTtcbiAgICAgIG1hcmdpbjogMDtcbiAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7XG4gICAgfVxuXG4gICAgLmNvbnRlbnQtdGFicyB7XG4gICAgICBtYXJnaW4tYm90dG9tOiAzMnB4O1xuICAgIH1cblxuICAgIC50YWItY29udGVudCB7XG4gICAgICBwYWRkaW5nOiAyNHB4IDA7XG4gICAgfVxuXG4gICAgLnJlcXVpcmVtZW50cy1ncmlkIHtcbiAgICAgIGRpc3BsYXk6IGdyaWQ7XG4gICAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdChhdXRvLWZpdCwgbWlubWF4KDMyMHB4LCAxZnIpKTtcbiAgICAgIGdhcDogMzJweDtcbiAgICAgIG1hcmdpbi10b3A6IDMycHg7XG4gICAgfVxuXG4gICAgLmluc3RhbGwtY29tbWFuZCB7XG4gICAgICBtYXJnaW4tdG9wOiAxMnB4O1xuICAgICAgcGFkZGluZzogOHB4O1xuICAgICAgYmFja2dyb3VuZDogI2Y1ZjVmNTtcbiAgICAgIGJvcmRlci1yYWRpdXM6IDRweDtcbiAgICB9XG5cbiAgICAuaW5zdGFsbGF0aW9uLXN0ZXBzIHtcbiAgICAgIG1hcmdpbi10b3A6IDI0cHg7XG4gICAgfVxuXG4gICAgLnN0ZXAtbnVtYmVyIHtcbiAgICAgIGJhY2tncm91bmQ6ICMxOTc2ZDI7XG4gICAgICBjb2xvcjogd2hpdGU7XG4gICAgICBib3JkZXItcmFkaXVzOiA1MCU7XG4gICAgICB3aWR0aDogMjRweDtcbiAgICAgIGhlaWdodDogMjRweDtcbiAgICAgIGRpc3BsYXk6IGlubGluZS1mbGV4O1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICAgICAgZm9udC1zaXplOiAwLjhlbTtcbiAgICAgIGZvbnQtd2VpZ2h0OiBib2xkO1xuICAgICAgbWFyZ2luLXJpZ2h0OiAxMnB4O1xuICAgIH1cblxuICAgIC5zdGVwLWNvbnRlbnQge1xuICAgICAgcGFkZGluZzogMTZweCAwO1xuICAgIH1cblxuICAgIC5jb2RlLWJsb2NrIHtcbiAgICAgIG1hcmdpbjogMTZweCAwO1xuICAgICAgYm9yZGVyOiAxcHggc29saWQgI2UwZTBlMDtcbiAgICAgIGJvcmRlci1yYWRpdXM6IDhweDtcbiAgICAgIG92ZXJmbG93OiBoaWRkZW47XG4gICAgfVxuXG4gICAgLmNvZGUtaGVhZGVyIHtcbiAgICAgIGJhY2tncm91bmQ6ICNmNWY1ZjU7XG4gICAgICBwYWRkaW5nOiA4cHggMTZweDtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgZ2FwOiA4cHg7XG4gICAgICBmb250LXdlaWdodDogNTAwO1xuICAgICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNlMGUwZTA7XG4gICAgfVxuXG4gICAgLmNvZGUtYmxvY2sgcHJlIHtcbiAgICAgIG1hcmdpbjogMDtcbiAgICAgIHBhZGRpbmc6IDE2cHg7XG4gICAgICBiYWNrZ3JvdW5kOiAjZmFmYWZhO1xuICAgICAgb3ZlcmZsb3cteDogYXV0bztcbiAgICB9XG5cbiAgICAuY29kZS1ibG9jayBjb2RlIHtcbiAgICAgIGZvbnQtZmFtaWx5OiAnQ291cmllciBOZXcnLCBtb25vc3BhY2U7XG4gICAgICBmb250LXNpemU6IDAuOWVtO1xuICAgIH1cblxuICAgIC5zdGVwLW5vdGVzIHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgZ2FwOiA4cHg7XG4gICAgICBtYXJnaW4tdG9wOiAxMnB4O1xuICAgICAgcGFkZGluZzogOHB4IDEycHg7XG4gICAgICBiYWNrZ3JvdW5kOiAjZTNmMmZkO1xuICAgICAgYm9yZGVyLXJhZGl1czogNHB4O1xuICAgICAgY29sb3I6ICMxOTc2ZDI7XG4gICAgfVxuXG4gICAgLmNvbmZpZy1jYXJkIHtcbiAgICAgIG1hcmdpbi1ib3R0b206IDI0cHg7XG4gICAgfVxuXG4gICAgLnF1aWNrLXN0YXJ0LWZsb3cge1xuICAgICAgbWFyZ2luOiAyNHB4IDA7XG4gICAgfVxuXG4gICAgLmZsb3ctc3RlcCB7XG4gICAgICBtYXJnaW4tYm90dG9tOiAzMnB4O1xuICAgICAgcG9zaXRpb246IHJlbGF0aXZlO1xuICAgIH1cblxuICAgIC5mbG93LXN0ZXAtaGVhZGVyIHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgZ2FwOiAxNnB4O1xuICAgICAgbWFyZ2luLWJvdHRvbTogMTJweDtcbiAgICB9XG5cbiAgICAuZmxvdy1zdGVwLW51bWJlciB7XG4gICAgICBiYWNrZ3JvdW5kOiAjMTk3NmQyO1xuICAgICAgY29sb3I6IHdoaXRlO1xuICAgICAgYm9yZGVyLXJhZGl1czogNTAlO1xuICAgICAgd2lkdGg6IDQwcHg7XG4gICAgICBoZWlnaHQ6IDQwcHg7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7XG4gICAgICBmb250LXNpemU6IDEuMWVtO1xuICAgIH1cblxuICAgIC5mbG93LXN0ZXAgaDMge1xuICAgICAgbWFyZ2luOiAwO1xuICAgICAgY29sb3I6ICMxOTc2ZDI7XG4gICAgfVxuXG4gICAgLmZsb3ctYXJyb3cge1xuICAgICAgdGV4dC1hbGlnbjogY2VudGVyO1xuICAgICAgbWFyZ2luOiAxNnB4IDA7XG4gICAgICBjb2xvcjogIzE5NzZkMjtcbiAgICB9XG5cbiAgICAuc3VjY2Vzcy1jYXJkIHtcbiAgICAgIG1hcmdpbi10b3A6IDMycHg7XG4gICAgICBib3JkZXI6IDJweCBzb2xpZCAjNGNhZjUwO1xuICAgIH1cblxuICAgIC5zdWNjZXNzLWNhcmQgdWwge1xuICAgICAgbWFyZ2luOiAxNnB4IDA7XG4gICAgfVxuXG4gICAgLnN1Y2Nlc3MtY2FyZCBhIHtcbiAgICAgIGNvbG9yOiAjMTk3NmQyO1xuICAgICAgdGV4dC1kZWNvcmF0aW9uOiBub25lO1xuICAgIH1cblxuICAgIC5zdWNjZXNzLWNhcmQgYTpob3ZlciB7XG4gICAgICB0ZXh0LWRlY29yYXRpb246IHVuZGVybGluZTtcbiAgICB9XG5cbiAgICAvKiBJY29uIGZpeGVzIGZvciBwcm9wZXIgZGlzcGxheSAqL1xuICAgIG1hdC1pY29uIHtcbiAgICAgIGRpc3BsYXk6IGlubGluZS1mbGV4ICFpbXBvcnRhbnQ7XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyICFpbXBvcnRhbnQ7XG4gICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlciAhaW1wb3J0YW50O1xuICAgICAgdmVydGljYWwtYWxpZ246IG1pZGRsZSAhaW1wb3J0YW50O1xuICAgICAgbGluZS1oZWlnaHQ6IDEgIWltcG9ydGFudDtcbiAgICB9XG5cbiAgICAuY29kZS1oZWFkZXIge1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICBnYXA6IDhweDtcbiAgICB9XG5cbiAgICAuY29kZS1oZWFkZXIgbWF0LWljb24ge1xuICAgICAgZm9udC1zaXplOiAxOHB4O1xuICAgICAgd2lkdGg6IDE4cHg7XG4gICAgICBoZWlnaHQ6IDE4cHg7XG4gICAgfVxuXG4gICAgLnN0ZXAtbm90ZXMge1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0O1xuICAgICAgZ2FwOiA4cHg7XG4gICAgICBtYXJnaW4tdG9wOiAxMnB4O1xuICAgICAgcGFkZGluZzogMTJweDtcbiAgICAgIGJhY2tncm91bmQ6ICNmMGY5ZmY7XG4gICAgICBib3JkZXItcmFkaXVzOiA4cHg7XG4gICAgICBib3JkZXItbGVmdDogNHB4IHNvbGlkICMwZWE1ZTk7XG4gICAgfVxuXG4gICAgLnN0ZXAtbm90ZXMgbWF0LWljb24ge1xuICAgICAgY29sb3I6ICMwZWE1ZTk7XG4gICAgICBmb250LXNpemU6IDE4cHg7XG4gICAgICB3aWR0aDogMThweDtcbiAgICAgIGhlaWdodDogMThweDtcbiAgICAgIG1hcmdpbi10b3A6IDJweDtcbiAgICAgIGZsZXgtc2hyaW5rOiAwO1xuICAgIH1cblxuICAgIC5mbG93LWFycm93IHtcbiAgICAgIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgICAgIG1hcmdpbjogMTZweCAwO1xuICAgIH1cblxuICAgIC5mbG93LWFycm93IG1hdC1pY29uIHtcbiAgICAgIGNvbG9yOiAjNjY3ZWVhO1xuICAgICAgZm9udC1zaXplOiAyNHB4O1xuICAgICAgd2lkdGg6IDI0cHg7XG4gICAgICBoZWlnaHQ6IDI0cHg7XG4gICAgfVxuXG4gICAgbWF0LWNhcmQtaGVhZGVyIC5tYXQtbWRjLWNhcmQtYXZhdGFyIG1hdC1pY29uIHtcbiAgICAgIGZvbnQtc2l6ZTogMjBweCAhaW1wb3J0YW50O1xuICAgICAgd2lkdGg6IDIwcHggIWltcG9ydGFudDtcbiAgICAgIGhlaWdodDogMjBweCAhaW1wb3J0YW50O1xuICAgICAgbGluZS1oZWlnaHQ6IDEgIWltcG9ydGFudDtcbiAgICB9XG5cbiAgICBAbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcbiAgICAgIC5yZXF1aXJlbWVudHMtZ3JpZCB7XG4gICAgICAgIGdyaWQtdGVtcGxhdGUtY29sdW1uczogMWZyO1xuICAgICAgfVxuXG4gICAgICAuZmxvdy1zdGVwLWhlYWRlciB7XG4gICAgICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgICAgIGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0O1xuICAgICAgICBnYXA6IDhweDtcbiAgICAgIH1cbiAgICB9XG4gICJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "MatTabsModule", "MatCardModule", "MatIconModule", "MatButtonModule", "MatExpansionModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "req_r1", "installCommand", "ɵɵtemplate", "GettingStartedComponent_mat_card_16_div_11_Template", "ɵɵstyleProp", "color", "ɵɵtextInterpolate1", "icon", "name", "version", "description", "ɵɵproperty", "step_r2", "commands", "notes", "GettingStartedComponent_mat_expansion_panel_22_div_11_Template", "GettingStartedComponent_mat_expansion_panel_22_div_12_Template", "i_r3", "title", "details", "step_r4", "command", "GettingStartedComponent_div_72_div_8_Template", "GettingStartedComponent_div_72_div_9_Template", "i_r5", "ctx_r5", "quickStartFlow", "length", "GettingStartedComponent", "constructor", "prerequisites", "installationSteps", "backendConfig", "frontendConfig", "selectors", "decls", "vars", "consts", "template", "GettingStartedComponent_Template", "rf", "ctx", "GettingStartedComponent_mat_card_16_Template", "GettingStartedComponent_mat_expansion_panel_22_Template", "GettingStartedComponent_div_72_Template", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i2", "RouterLink", "i3", "Mat<PERSON><PERSON>", "MatTabGroup", "i4", "MatCard", "MatCardActions", "MatCardAvatar", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardSubtitle", "MatCardTitle", "i5", "MatIcon", "i6", "MatButton", "i7", "Mat<PERSON><PERSON>rdi<PERSON>", "MatExpansionPanel", "MatExpansionPanelHeader", "MatExpansionPanelTitle", "MatExpansionPanelDescription", "styles"], "sources": ["D:\\TGI\\Blockchain.SPT\\frontend\\src\\app\\modules\\documentation\\components\\getting-started\\getting-started.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatExpansionModule } from '@angular/material/expansion';\n\n@Component({\n  selector: 'app-getting-started',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    MatTabsModule,\n    MatCardModule,\n    MatIconModule,\n    MatButtonModule,\n    MatExpansionModule\n  ],\n  template: `\n    <div class=\"getting-started-container\">\n      <div class=\"page-header\">\n        <h1>\n          <mat-icon>play_arrow</mat-icon>\n          Getting Started\n        </h1>\n        <p class=\"page-subtitle\">\n          Get SPT up and running in your development environment\n        </p>\n      </div>\n\n      <mat-tab-group class=\"content-tabs\" animationDuration=\"300ms\">\n        <!-- Prerequisites Tab -->\n        <mat-tab label=\"Prerequisites\">\n          <div class=\"tab-content\">\n            <h2>System Requirements</h2>\n            <p>Before installing SPT, ensure you have the following prerequisites:</p>\n            \n            <div class=\"requirements-grid\">\n              <mat-card class=\"requirement-card\" *ngFor=\"let req of prerequisites\">\n                <mat-card-header>\n                  <mat-icon mat-card-avatar [style.background-color]=\"req.color\">\n                    {{ req.icon }}\n                  </mat-icon>\n                  <mat-card-title>{{ req.name }}</mat-card-title>\n                  <mat-card-subtitle>{{ req.version }}</mat-card-subtitle>\n                </mat-card-header>\n                <mat-card-content>\n                  <p>{{ req.description }}</p>\n                  <div class=\"install-command\" *ngIf=\"req.installCommand\">\n                    <strong>Installation:</strong>\n                    <code>{{ req.installCommand }}</code>\n                  </div>\n                </mat-card-content>\n              </mat-card>\n            </div>\n          </div>\n        </mat-tab>\n\n        <!-- Installation Tab -->\n        <mat-tab label=\"Installation\">\n          <div class=\"tab-content\">\n            <h2>Installation Steps</h2>\n            \n            <mat-accordion class=\"installation-steps\">\n              <mat-expansion-panel *ngFor=\"let step of installationSteps; let i = index\" [expanded]=\"i === 0\">\n                <mat-expansion-panel-header>\n                  <mat-panel-title>\n                    <span class=\"step-number\">{{ i + 1 }}</span>\n                    {{ step.title }}\n                  </mat-panel-title>\n                  <mat-panel-description>\n                    {{ step.description }}\n                  </mat-panel-description>\n                </mat-expansion-panel-header>\n                \n                <div class=\"step-content\">\n                  <p>{{ step.details }}</p>\n                  <div class=\"code-block\" *ngIf=\"step.commands\">\n                    <div class=\"code-header\">\n                      <mat-icon>terminal</mat-icon>\n                      <span>Commands</span>\n                    </div>\n                    <pre><code>{{ step.commands }}</code></pre>\n                  </div>\n                  <div class=\"step-notes\" *ngIf=\"step.notes\">\n                    <mat-icon>info</mat-icon>\n                    <span>{{ step.notes }}</span>\n                  </div>\n                </div>\n              </mat-expansion-panel>\n            </mat-accordion>\n          </div>\n        </mat-tab>\n\n        <!-- Configuration Tab -->\n        <mat-tab label=\"Configuration\">\n          <div class=\"tab-content\">\n            <h2>Configuration</h2>\n            <p>Configure SPT for your development environment:</p>\n\n            <mat-card class=\"config-card\">\n              <mat-card-header>\n                <mat-icon mat-card-avatar>settings</mat-icon>\n                <mat-card-title>Backend Configuration</mat-card-title>\n                <mat-card-subtitle>spt.config.json</mat-card-subtitle>\n              </mat-card-header>\n              <mat-card-content>\n                <div class=\"code-block\">\n                  <div class=\"code-header\">\n                    <mat-icon>code</mat-icon>\n                    <span>Configuration File</span>\n                  </div>\n                  <pre><code>{{ backendConfig }}</code></pre>\n                </div>\n              </mat-card-content>\n            </mat-card>\n\n            <mat-card class=\"config-card\">\n              <mat-card-header>\n                <mat-icon mat-card-avatar>web</mat-icon>\n                <mat-card-title>Frontend Configuration</mat-card-title>\n                <mat-card-subtitle>Environment Variables</mat-card-subtitle>\n              </mat-card-header>\n              <mat-card-content>\n                <div class=\"code-block\">\n                  <div class=\"code-header\">\n                    <mat-icon>code</mat-icon>\n                    <span>Environment File</span>\n                  </div>\n                  <pre><code>{{ frontendConfig }}</code></pre>\n                </div>\n              </mat-card-content>\n            </mat-card>\n          </div>\n        </mat-tab>\n\n        <!-- Quick Start Tab -->\n        <mat-tab label=\"Quick Start\">\n          <div class=\"tab-content\">\n            <h2>Quick Start Guide</h2>\n            <p>Get SPT running with these simple commands:</p>\n\n            <div class=\"quick-start-flow\">\n              <div class=\"flow-step\" *ngFor=\"let step of quickStartFlow; let i = index\">\n                <div class=\"flow-step-header\">\n                  <div class=\"flow-step-number\">{{ i + 1 }}</div>\n                  <h3>{{ step.title }}</h3>\n                </div>\n                <p>{{ step.description }}</p>\n                <div class=\"code-block\" *ngIf=\"step.command\">\n                  <pre><code>{{ step.command }}</code></pre>\n                </div>\n                <div class=\"flow-arrow\" *ngIf=\"i < quickStartFlow.length - 1\">\n                  <mat-icon>arrow_downward</mat-icon>\n                </div>\n              </div>\n            </div>\n\n            <mat-card class=\"success-card\">\n              <mat-card-header>\n                <mat-icon mat-card-avatar style=\"background-color: #4caf50;\">check_circle</mat-icon>\n                <mat-card-title>Success!</mat-card-title>\n                <mat-card-subtitle>SPT is now running</mat-card-subtitle>\n              </mat-card-header>\n              <mat-card-content>\n                <p>You can now access:</p>\n                <ul>\n                  <li><strong>Web Dashboard:</strong> <a href=\"http://localhost:4200\" target=\"_blank\">http://localhost:4200</a></li>\n                  <li><strong>API Server:</strong> <a href=\"http://localhost:8080\" target=\"_blank\">http://localhost:8080</a></li>\n                  <li><strong>Health Check:</strong> <a href=\"http://localhost:8080/health\" target=\"_blank\">http://localhost:8080/health</a></li>\n                </ul>\n              </mat-card-content>\n              <mat-card-actions>\n                <button mat-raised-button color=\"primary\" routerLink=\"/doc/api-reference\">\n                  <mat-icon>api</mat-icon>\n                  Explore API\n                </button>\n                <button mat-stroked-button routerLink=\"/doc/security-practices\">\n                  <mat-icon>security</mat-icon>\n                  Security Guide\n                </button>\n              </mat-card-actions>\n            </mat-card>\n          </div>\n        </mat-tab>\n      </mat-tab-group>\n    </div>\n  `,\n  styles: [`\n    .getting-started-container {\n      max-width: 1200px;\n      margin: 0 auto;\n      padding: 48px;\n      background: #ffffff;\n    }\n\n    .page-header {\n      text-align: center;\n      margin-bottom: 48px;\n      padding: 32px 0;\n    }\n\n    .page-header h1 {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      gap: 16px;\n      color: #1a202c;\n      margin: 0 0 12px 0;\n      font-size: 2.5em;\n      font-weight: 800;\n      letter-spacing: -1px;\n    }\n\n    .page-header h1 mat-icon {\n      font-size: 48px;\n      width: 48px;\n      height: 48px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      color: #667eea;\n    }\n\n    .page-subtitle {\n      color: #64748b;\n      font-size: 1.25em;\n      margin: 0;\n      font-weight: 500;\n    }\n\n    .content-tabs {\n      margin-bottom: 32px;\n    }\n\n    .tab-content {\n      padding: 24px 0;\n    }\n\n    .requirements-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));\n      gap: 32px;\n      margin-top: 32px;\n    }\n\n    .install-command {\n      margin-top: 12px;\n      padding: 8px;\n      background: #f5f5f5;\n      border-radius: 4px;\n    }\n\n    .installation-steps {\n      margin-top: 24px;\n    }\n\n    .step-number {\n      background: #1976d2;\n      color: white;\n      border-radius: 50%;\n      width: 24px;\n      height: 24px;\n      display: inline-flex;\n      align-items: center;\n      justify-content: center;\n      font-size: 0.8em;\n      font-weight: bold;\n      margin-right: 12px;\n    }\n\n    .step-content {\n      padding: 16px 0;\n    }\n\n    .code-block {\n      margin: 16px 0;\n      border: 1px solid #e0e0e0;\n      border-radius: 8px;\n      overflow: hidden;\n    }\n\n    .code-header {\n      background: #f5f5f5;\n      padding: 8px 16px;\n      display: flex;\n      align-items: center;\n      gap: 8px;\n      font-weight: 500;\n      border-bottom: 1px solid #e0e0e0;\n    }\n\n    .code-block pre {\n      margin: 0;\n      padding: 16px;\n      background: #fafafa;\n      overflow-x: auto;\n    }\n\n    .code-block code {\n      font-family: 'Courier New', monospace;\n      font-size: 0.9em;\n    }\n\n    .step-notes {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n      margin-top: 12px;\n      padding: 8px 12px;\n      background: #e3f2fd;\n      border-radius: 4px;\n      color: #1976d2;\n    }\n\n    .config-card {\n      margin-bottom: 24px;\n    }\n\n    .quick-start-flow {\n      margin: 24px 0;\n    }\n\n    .flow-step {\n      margin-bottom: 32px;\n      position: relative;\n    }\n\n    .flow-step-header {\n      display: flex;\n      align-items: center;\n      gap: 16px;\n      margin-bottom: 12px;\n    }\n\n    .flow-step-number {\n      background: #1976d2;\n      color: white;\n      border-radius: 50%;\n      width: 40px;\n      height: 40px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-weight: bold;\n      font-size: 1.1em;\n    }\n\n    .flow-step h3 {\n      margin: 0;\n      color: #1976d2;\n    }\n\n    .flow-arrow {\n      text-align: center;\n      margin: 16px 0;\n      color: #1976d2;\n    }\n\n    .success-card {\n      margin-top: 32px;\n      border: 2px solid #4caf50;\n    }\n\n    .success-card ul {\n      margin: 16px 0;\n    }\n\n    .success-card a {\n      color: #1976d2;\n      text-decoration: none;\n    }\n\n    .success-card a:hover {\n      text-decoration: underline;\n    }\n\n    /* Icon fixes for proper display */\n    mat-icon {\n      display: inline-flex !important;\n      align-items: center !important;\n      justify-content: center !important;\n      vertical-align: middle !important;\n      line-height: 1 !important;\n    }\n\n    .code-header {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n    }\n\n    .code-header mat-icon {\n      font-size: 18px;\n      width: 18px;\n      height: 18px;\n    }\n\n    .step-notes {\n      display: flex;\n      align-items: flex-start;\n      gap: 8px;\n      margin-top: 12px;\n      padding: 12px;\n      background: #f0f9ff;\n      border-radius: 8px;\n      border-left: 4px solid #0ea5e9;\n    }\n\n    .step-notes mat-icon {\n      color: #0ea5e9;\n      font-size: 18px;\n      width: 18px;\n      height: 18px;\n      margin-top: 2px;\n      flex-shrink: 0;\n    }\n\n    .flow-arrow {\n      text-align: center;\n      margin: 16px 0;\n    }\n\n    .flow-arrow mat-icon {\n      color: #667eea;\n      font-size: 24px;\n      width: 24px;\n      height: 24px;\n    }\n\n    mat-card-header .mat-mdc-card-avatar mat-icon {\n      font-size: 20px !important;\n      width: 20px !important;\n      height: 20px !important;\n      line-height: 1 !important;\n    }\n\n    @media (max-width: 768px) {\n      .requirements-grid {\n        grid-template-columns: 1fr;\n      }\n\n      .flow-step-header {\n        flex-direction: column;\n        align-items: flex-start;\n        gap: 8px;\n      }\n    }\n  `]\n})\nexport class GettingStartedComponent {\n  prerequisites = [\n    {\n      name: 'Go',\n      version: '1.21+',\n      description: 'Required for the backend services and CLI tool',\n      icon: 'code',\n      color: '#00ADD8',\n      installCommand: 'https://golang.org/doc/install'\n    },\n    {\n      name: 'Node.js',\n      version: '18+',\n      description: 'Required for the frontend dashboard and development tools',\n      icon: 'javascript',\n      color: '#339933',\n      installCommand: 'https://nodejs.org/'\n    },\n    {\n      name: 'Angular CLI',\n      version: 'Latest',\n      description: 'Required for frontend development and building',\n      icon: 'web',\n      color: '#DD0031',\n      installCommand: 'npm install -g @angular/cli'\n    },\n    {\n      name: 'Git',\n      version: 'Latest',\n      description: 'Required for version control and repository management',\n      icon: 'source',\n      color: '#F05032',\n      installCommand: 'https://git-scm.com/downloads'\n    }\n  ];\n\n  installationSteps = [\n    {\n      title: 'Clone Repository',\n      description: 'Get the SPT source code',\n      details: 'Clone the SPT repository from GitHub to your local development environment.',\n      commands: 'git clone https://github.com/blockchain-spt/spt.git\\ncd spt',\n      notes: 'Make sure you have Git installed and configured'\n    },\n    {\n      title: 'Backend Setup',\n      description: 'Install Go dependencies and start backend',\n      details: 'Navigate to the backend directory, install dependencies, and start the Go server.',\n      commands: 'cd backend\\ngo mod tidy\\ngo run cmd/main.go',\n      notes: 'Backend will start on port 8080 by default'\n    },\n    {\n      title: 'Frontend Setup',\n      description: 'Install Node.js dependencies and start frontend',\n      details: 'Navigate to the frontend directory, install npm packages, and start the Angular development server.',\n      commands: 'cd frontend\\nnpm install\\nnpm start',\n      notes: 'Frontend will start on port 4200 by default'\n    },\n    {\n      title: 'CLI Tool (Optional)',\n      description: 'Build the command-line interface',\n      details: 'Build the SPT CLI tool for command-line security scanning.',\n      commands: 'make cli\\n# or\\ngo build -o spt cmd/main.go',\n      notes: 'CLI tool will be available as ./spt'\n    }\n  ];\n\n  quickStartFlow = [\n    {\n      title: 'Clone & Navigate',\n      description: 'Get the source code and navigate to the project directory',\n      command: 'git clone https://github.com/blockchain-spt/spt.git\\ncd spt'\n    },\n    {\n      title: 'Start Backend',\n      description: 'Launch the Go backend server with all APIs',\n      command: 'cd backend && go run cmd/main.go'\n    },\n    {\n      title: 'Start Frontend',\n      description: 'Launch the Angular development server in a new terminal',\n      command: 'cd frontend && npm install && npm start'\n    },\n    {\n      title: 'Access Application',\n      description: 'Open your browser and start using SPT',\n      command: 'Open http://localhost:4200 in your browser'\n    }\n  ];\n\n  backendConfig = `{\n  \"environment\": \"development\",\n  \"server\": {\n    \"port\": 8080,\n    \"host\": \"localhost\"\n  },\n  \"database\": {\n    \"type\": \"sqlite\",\n    \"database\": \"spt.db\"\n  },\n  \"security\": {\n    \"jwt_secret\": \"your-secret-key\",\n    \"cors_enabled\": true\n  },\n  \"scanning\": {\n    \"max_concurrent_scans\": 5,\n    \"timeout_seconds\": 300\n  }\n}`;\n\n  frontendConfig = `# Frontend Environment Variables\nAPI_URL=http://localhost:8080\nWS_URL=ws://localhost:8080/ws\nENVIRONMENT=development\nDEBUG=true`;\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,kBAAkB,QAAQ,6BAA6B;;;;;;;;;;;IA6C5CC,EADF,CAAAC,cAAA,cAAwD,aAC9C;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC9BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAChCF,EADgC,CAAAG,YAAA,EAAO,EACjC;;;;IADEH,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,cAAA,CAAwB;;;;;IAVhCP,EAFJ,CAAAC,cAAA,mBAAqE,sBAClD,mBACgD;IAC7DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACXH,EAAA,CAAAC,cAAA,qBAAgB;IAAAD,EAAA,CAAAE,MAAA,GAAc;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAC/CH,EAAA,CAAAC,cAAA,wBAAmB;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IACtCF,EADsC,CAAAG,YAAA,EAAoB,EACxC;IAEhBH,EADF,CAAAC,cAAA,uBAAkB,QACb;IAAAD,EAAA,CAAAE,MAAA,IAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC5BH,EAAA,CAAAQ,UAAA,KAAAC,mDAAA,kBAAwD;IAK5DT,EADE,CAAAG,YAAA,EAAmB,EACV;;;;IAbmBH,EAAA,CAAAI,SAAA,GAAoC;IAApCJ,EAAA,CAAAU,WAAA,qBAAAJ,MAAA,CAAAK,KAAA,CAAoC;IAC5DX,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAY,kBAAA,MAAAN,MAAA,CAAAO,IAAA,MACF;IACgBb,EAAA,CAAAI,SAAA,GAAc;IAAdJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAQ,IAAA,CAAc;IACXd,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAS,OAAA,CAAiB;IAGjCf,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAU,WAAA,CAAqB;IACMhB,EAAA,CAAAI,SAAA,EAAwB;IAAxBJ,EAAA,CAAAiB,UAAA,SAAAX,MAAA,CAAAC,cAAA,CAAwB;;;;;IA+BlDP,EAFJ,CAAAC,cAAA,cAA8C,cACnB,eACb;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAChBF,EADgB,CAAAG,YAAA,EAAO,EACjB;IACDH,EAAL,CAAAC,cAAA,UAAK,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAChCF,EADgC,CAAAG,YAAA,EAAO,EAAM,EACvC;;;;IADOH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAAa,OAAA,CAAAC,QAAA,CAAmB;;;;;IAG9BnB,EADF,CAAAC,cAAA,cAA2C,eAC/B;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IACxBF,EADwB,CAAAG,YAAA,EAAO,EACzB;;;;IADEH,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAa,OAAA,CAAAE,KAAA,CAAgB;;;;;IAnBtBpB,EAHN,CAAAC,cAAA,8BAAgG,iCAClE,sBACT,eACW;IAAAD,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5CH,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAkB;IAClBH,EAAA,CAAAC,cAAA,4BAAuB;IACrBD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAwB,EACG;IAG3BH,EADF,CAAAC,cAAA,cAA0B,QACrB;IAAAD,EAAA,CAAAE,MAAA,IAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAQzBH,EAPA,CAAAQ,UAAA,KAAAa,8DAAA,kBAA8C,KAAAC,8DAAA,kBAOH;IAK/CtB,EADE,CAAAG,YAAA,EAAM,EACc;;;;;IAzBqDH,EAAA,CAAAiB,UAAA,aAAAM,IAAA,OAAoB;IAG/DvB,EAAA,CAAAI,SAAA,GAAW;IAAXJ,EAAA,CAAAK,iBAAA,CAAAkB,IAAA,KAAW;IACrCvB,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAY,kBAAA,MAAAM,OAAA,CAAAM,KAAA,MACF;IAEExB,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAY,kBAAA,MAAAM,OAAA,CAAAF,WAAA,MACF;IAIGhB,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAK,iBAAA,CAAAa,OAAA,CAAAO,OAAA,CAAkB;IACIzB,EAAA,CAAAI,SAAA,EAAmB;IAAnBJ,EAAA,CAAAiB,UAAA,SAAAC,OAAA,CAAAC,QAAA,CAAmB;IAOnBnB,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAiB,UAAA,SAAAC,OAAA,CAAAE,KAAA,CAAgB;;;;;IAkEpCpB,EADP,CAAAC,cAAA,cAA6C,UACtC,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAC/BF,EAD+B,CAAAG,YAAA,EAAO,EAAM,EACtC;;;;IADOH,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAK,iBAAA,CAAAqB,OAAA,CAAAC,OAAA,CAAkB;;;;;IAG7B3B,EADF,CAAAC,cAAA,cAA8D,eAClD;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAC1BF,EAD0B,CAAAG,YAAA,EAAW,EAC/B;;;;;IATJH,EAFJ,CAAAC,cAAA,cAA0E,cAC1C,cACE;IAAAD,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC/CH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IACtBF,EADsB,CAAAG,YAAA,EAAK,EACrB;IACNH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAI7BH,EAHA,CAAAQ,UAAA,IAAAoB,6CAAA,kBAA6C,IAAAC,6CAAA,kBAGiB;IAGhE7B,EAAA,CAAAG,YAAA,EAAM;;;;;;IAV4BH,EAAA,CAAAI,SAAA,GAAW;IAAXJ,EAAA,CAAAK,iBAAA,CAAAyB,IAAA,KAAW;IACrC9B,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAqB,OAAA,CAAAF,KAAA,CAAgB;IAEnBxB,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAK,iBAAA,CAAAqB,OAAA,CAAAV,WAAA,CAAsB;IACAhB,EAAA,CAAAI,SAAA,EAAkB;IAAlBJ,EAAA,CAAAiB,UAAA,SAAAS,OAAA,CAAAC,OAAA,CAAkB;IAGlB3B,EAAA,CAAAI,SAAA,EAAmC;IAAnCJ,EAAA,CAAAiB,UAAA,SAAAa,IAAA,GAAAC,MAAA,CAAAC,cAAA,CAAAC,MAAA,KAAmC;;;AA0S5E,OAAM,MAAOC,uBAAuB;EA5bpCC,YAAA;IA6bE,KAAAC,aAAa,GAAG,CACd;MACEtB,IAAI,EAAE,IAAI;MACVC,OAAO,EAAE,OAAO;MAChBC,WAAW,EAAE,gDAAgD;MAC7DH,IAAI,EAAE,MAAM;MACZF,KAAK,EAAE,SAAS;MAChBJ,cAAc,EAAE;KACjB,EACD;MACEO,IAAI,EAAE,SAAS;MACfC,OAAO,EAAE,KAAK;MACdC,WAAW,EAAE,2DAA2D;MACxEH,IAAI,EAAE,YAAY;MAClBF,KAAK,EAAE,SAAS;MAChBJ,cAAc,EAAE;KACjB,EACD;MACEO,IAAI,EAAE,aAAa;MACnBC,OAAO,EAAE,QAAQ;MACjBC,WAAW,EAAE,gDAAgD;MAC7DH,IAAI,EAAE,KAAK;MACXF,KAAK,EAAE,SAAS;MAChBJ,cAAc,EAAE;KACjB,EACD;MACEO,IAAI,EAAE,KAAK;MACXC,OAAO,EAAE,QAAQ;MACjBC,WAAW,EAAE,wDAAwD;MACrEH,IAAI,EAAE,QAAQ;MACdF,KAAK,EAAE,SAAS;MAChBJ,cAAc,EAAE;KACjB,CACF;IAED,KAAA8B,iBAAiB,GAAG,CAClB;MACEb,KAAK,EAAE,kBAAkB;MACzBR,WAAW,EAAE,yBAAyB;MACtCS,OAAO,EAAE,6EAA6E;MACtFN,QAAQ,EAAE,6DAA6D;MACvEC,KAAK,EAAE;KACR,EACD;MACEI,KAAK,EAAE,eAAe;MACtBR,WAAW,EAAE,2CAA2C;MACxDS,OAAO,EAAE,mFAAmF;MAC5FN,QAAQ,EAAE,6CAA6C;MACvDC,KAAK,EAAE;KACR,EACD;MACEI,KAAK,EAAE,gBAAgB;MACvBR,WAAW,EAAE,iDAAiD;MAC9DS,OAAO,EAAE,qGAAqG;MAC9GN,QAAQ,EAAE,qCAAqC;MAC/CC,KAAK,EAAE;KACR,EACD;MACEI,KAAK,EAAE,qBAAqB;MAC5BR,WAAW,EAAE,kCAAkC;MAC/CS,OAAO,EAAE,4DAA4D;MACrEN,QAAQ,EAAE,6CAA6C;MACvDC,KAAK,EAAE;KACR,CACF;IAED,KAAAY,cAAc,GAAG,CACf;MACER,KAAK,EAAE,kBAAkB;MACzBR,WAAW,EAAE,2DAA2D;MACxEW,OAAO,EAAE;KACV,EACD;MACEH,KAAK,EAAE,eAAe;MACtBR,WAAW,EAAE,4CAA4C;MACzDW,OAAO,EAAE;KACV,EACD;MACEH,KAAK,EAAE,gBAAgB;MACvBR,WAAW,EAAE,yDAAyD;MACtEW,OAAO,EAAE;KACV,EACD;MACEH,KAAK,EAAE,oBAAoB;MAC3BR,WAAW,EAAE,uCAAuC;MACpDW,OAAO,EAAE;KACV,CACF;IAED,KAAAW,aAAa,GAAG;;;;;;;;;;;;;;;;;;EAkBhB;IAEA,KAAAC,cAAc,GAAG;;;;WAIR;;;;uCAlHEL,uBAAuB;IAAA;EAAA;;;YAAvBA,uBAAuB;MAAAM,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA5a1B9C,EAHN,CAAAC,cAAA,aAAuC,aACZ,SACnB,eACQ;UAAAD,EAAA,CAAAE,MAAA,iBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC/BH,EAAA,CAAAE,MAAA,wBACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,WAAyB;UACvBD,EAAA,CAAAE,MAAA,+DACF;UACFF,EADE,CAAAG,YAAA,EAAI,EACA;UAMAH,EAJN,CAAAC,cAAA,uBAA8D,iBAE7B,cACJ,UACnB;UAAAD,EAAA,CAAAE,MAAA,2BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC5BH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,2EAAmE;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAE1EH,EAAA,CAAAC,cAAA,cAA+B;UAC7BD,EAAA,CAAAQ,UAAA,KAAAwC,4CAAA,uBAAqE;UAkB3EhD,EAFI,CAAAG,YAAA,EAAM,EACF,EACE;UAKNH,EAFJ,CAAAC,cAAA,kBAA8B,cACH,UACnB;UAAAD,EAAA,CAAAE,MAAA,0BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAE3BH,EAAA,CAAAC,cAAA,wBAA0C;UACxCD,EAAA,CAAAQ,UAAA,KAAAyC,uDAAA,mCAAgG;UA4BtGjD,EAFI,CAAAG,YAAA,EAAgB,EACZ,EACE;UAKNH,EAFJ,CAAAC,cAAA,mBAA+B,cACJ,UACnB;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACtBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,uDAA+C;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAIlDH,EAFJ,CAAAC,cAAA,oBAA8B,uBACX,oBACW;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7CH,EAAA,CAAAC,cAAA,sBAAgB;UAAAD,EAAA,CAAAE,MAAA,6BAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAiB;UACtDH,EAAA,CAAAC,cAAA,yBAAmB;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UACpCF,EADoC,CAAAG,YAAA,EAAoB,EACtC;UAIZH,EAHN,CAAAC,cAAA,wBAAkB,eACQ,eACG,gBACb;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzBH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,0BAAkB;UAC1BF,EAD0B,CAAAG,YAAA,EAAO,EAC3B;UACDH,EAAL,CAAAC,cAAA,WAAK,YAAM;UAAAD,EAAA,CAAAE,MAAA,IAAmB;UAGpCF,EAHoC,CAAAG,YAAA,EAAO,EAAM,EACvC,EACW,EACV;UAIPH,EAFJ,CAAAC,cAAA,oBAA8B,uBACX,oBACW;UAAAD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACxCH,EAAA,CAAAC,cAAA,sBAAgB;UAAAD,EAAA,CAAAE,MAAA,8BAAsB;UAAAF,EAAA,CAAAG,YAAA,EAAiB;UACvDH,EAAA,CAAAC,cAAA,yBAAmB;UAAAD,EAAA,CAAAE,MAAA,6BAAqB;UAC1CF,EAD0C,CAAAG,YAAA,EAAoB,EAC5C;UAIZH,EAHN,CAAAC,cAAA,wBAAkB,eACQ,eACG,gBACb;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzBH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UACxBF,EADwB,CAAAG,YAAA,EAAO,EACzB;UACDH,EAAL,CAAAC,cAAA,WAAK,YAAM;UAAAD,EAAA,CAAAE,MAAA,IAAoB;UAKzCF,EALyC,CAAAG,YAAA,EAAO,EAAM,EACxC,EACW,EACV,EACP,EACE;UAKNH,EAFJ,CAAAC,cAAA,mBAA6B,cACF,UACnB;UAAAD,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC1BH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,mDAA2C;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAElDH,EAAA,CAAAC,cAAA,eAA8B;UAC5BD,EAAA,CAAAQ,UAAA,KAAA0C,uCAAA,mBAA0E;UAa5ElD,EAAA,CAAAG,YAAA,EAAM;UAIFH,EAFJ,CAAAC,cAAA,oBAA+B,uBACZ,oBAC8C;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACpFH,EAAA,CAAAC,cAAA,sBAAgB;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAiB;UACzCH,EAAA,CAAAC,cAAA,yBAAmB;UAAAD,EAAA,CAAAE,MAAA,0BAAkB;UACvCF,EADuC,CAAAG,YAAA,EAAoB,EACzC;UAEhBH,EADF,CAAAC,cAAA,wBAAkB,SACb;UAAAD,EAAA,CAAAE,MAAA,2BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAEpBH,EADN,CAAAC,cAAA,UAAI,UACE,cAAQ;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAACH,EAAA,CAAAC,cAAA,aAAgD;UAAAD,EAAA,CAAAE,MAAA,6BAAqB;UAAIF,EAAJ,CAAAG,YAAA,EAAI,EAAK;UAC9GH,EAAJ,CAAAC,cAAA,UAAI,cAAQ;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAACH,EAAA,CAAAC,cAAA,aAAgD;UAAAD,EAAA,CAAAE,MAAA,6BAAqB;UAAIF,EAAJ,CAAAG,YAAA,EAAI,EAAK;UAC3GH,EAAJ,CAAAC,cAAA,UAAI,cAAQ;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAACH,EAAA,CAAAC,cAAA,aAAuD;UAAAD,EAAA,CAAAE,MAAA,oCAA4B;UAE1HF,EAF0H,CAAAG,YAAA,EAAI,EAAK,EAC5H,EACY;UAGfH,EAFJ,CAAAC,cAAA,yBAAkB,mBAC0D,iBAC9D;UAAAD,EAAA,CAAAE,MAAA,YAAG;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACxBH,EAAA,CAAAE,MAAA,sBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAEPH,EADF,CAAAC,cAAA,mBAAgE,iBACpD;UAAAD,EAAA,CAAAE,MAAA,iBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7BH,EAAA,CAAAE,MAAA,yBACF;UAMZF,EANY,CAAAG,YAAA,EAAS,EACQ,EACV,EACP,EACE,EACI,EACZ;;;UApJuDH,EAAA,CAAAI,SAAA,IAAgB;UAAhBJ,EAAA,CAAAiB,UAAA,YAAA8B,GAAA,CAAAX,aAAA,CAAgB;UA0B7BpC,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAAiB,UAAA,YAAA8B,GAAA,CAAAV,iBAAA,CAAsB;UAgD7CrC,EAAA,CAAAI,SAAA,IAAmB;UAAnBJ,EAAA,CAAAK,iBAAA,CAAA0C,GAAA,CAAAT,aAAA,CAAmB;UAiBnBtC,EAAA,CAAAI,SAAA,IAAoB;UAApBJ,EAAA,CAAAK,iBAAA,CAAA0C,GAAA,CAAAR,cAAA,CAAoB;UAcKvC,EAAA,CAAAI,SAAA,GAAmB;UAAnBJ,EAAA,CAAAiB,UAAA,YAAA8B,GAAA,CAAAf,cAAA,CAAmB;;;qBArIrEvC,YAAY,EAAA0D,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZ3D,YAAY,EAAA4D,EAAA,CAAAC,UAAA,EACZ5D,aAAa,EAAA6D,EAAA,CAAAC,MAAA,EAAAD,EAAA,CAAAE,WAAA,EACb9D,aAAa,EAAA+D,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,aAAA,EAAAH,EAAA,CAAAI,cAAA,EAAAJ,EAAA,CAAAK,aAAA,EAAAL,EAAA,CAAAM,eAAA,EAAAN,EAAA,CAAAO,YAAA,EACbrE,aAAa,EAAAsE,EAAA,CAAAC,OAAA,EACbtE,eAAe,EAAAuE,EAAA,CAAAC,SAAA,EACfvE,kBAAkB,EAAAwE,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,iBAAA,EAAAF,EAAA,CAAAG,uBAAA,EAAAH,EAAA,CAAAI,sBAAA,EAAAJ,EAAA,CAAAK,4BAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}