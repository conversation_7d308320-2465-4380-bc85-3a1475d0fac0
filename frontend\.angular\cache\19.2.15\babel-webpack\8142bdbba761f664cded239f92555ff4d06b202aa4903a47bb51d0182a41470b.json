{"ast": null, "code": "import { map, take } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nexport let AuthGuard = /*#__PURE__*/(() => {\n  class AuthGuard {\n    constructor(authService, router) {\n      this.authService = authService;\n      this.router = router;\n    }\n    canActivate(route, state) {\n      return this.checkAuth(state.url);\n    }\n    canActivateChild(route, state) {\n      return this.checkAuth(state.url);\n    }\n    checkAuth(url) {\n      return this.authService.currentUser$.pipe(take(1), map(user => {\n        if (user && this.authService.isAuthenticated()) {\n          return true;\n        } else {\n          this.router.navigate(['/login'], {\n            queryParams: {\n              returnUrl: url\n            }\n          });\n          return false;\n        }\n      }));\n    }\n    static {\n      this.ɵfac = function AuthGuard_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || AuthGuard)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.Router));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: AuthGuard,\n        factory: AuthGuard.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return AuthGuard;\n})();\nexport let RoleGuard = /*#__PURE__*/(() => {\n  class RoleGuard {\n    constructor(authService, router) {\n      this.authService = authService;\n      this.router = router;\n    }\n    canActivate(route, state) {\n      const requiredRoles = route.data['roles'];\n      if (!requiredRoles || requiredRoles.length === 0) {\n        return true;\n      }\n      return this.authService.currentUser$.pipe(take(1), map(user => {\n        if (!user || !this.authService.isAuthenticated()) {\n          this.router.navigate(['/login'], {\n            queryParams: {\n              returnUrl: state.url\n            }\n          });\n          return false;\n        }\n        if (this.authService.hasAnyRole(requiredRoles)) {\n          return true;\n        } else {\n          this.router.navigate(['/unauthorized']);\n          return false;\n        }\n      }));\n    }\n    static {\n      this.ɵfac = function RoleGuard_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || RoleGuard)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.Router));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: RoleGuard,\n        factory: RoleGuard.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return RoleGuard;\n})();\nexport let GuestGuard = /*#__PURE__*/(() => {\n  class GuestGuard {\n    constructor(authService, router) {\n      this.authService = authService;\n      this.router = router;\n    }\n    canActivate() {\n      return this.authService.currentUser$.pipe(take(1), map(user => {\n        if (user && this.authService.isAuthenticated()) {\n          this.router.navigate(['/dashboard']);\n          return false;\n        }\n        return true;\n      }));\n    }\n    static {\n      this.ɵfac = function GuestGuard_Factory(__ngFactoryType__) {\n        return new (__ngFactoryType__ || GuestGuard)(i0.ɵɵinject(i1.AuthService), i0.ɵɵinject(i2.Router));\n      };\n    }\n    static {\n      this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n        token: GuestGuard,\n        factory: GuestGuard.ɵfac,\n        providedIn: 'root'\n      });\n    }\n  }\n  return GuestGuard;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}