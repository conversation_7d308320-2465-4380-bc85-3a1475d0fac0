# API Key Authentication

The SPT backend now supports both JWT and API key authentication. This is particularly useful for the VS Code extension and other programmatic access.

## How It Works

The authentication middleware checks for authentication in the following order:

1. **API Key in X-API-Key header** (preferred for VS Code extension)
2. **API Key in Authorization header** (fallback: `Authorization: Bearer <api-key>`)
3. **JWT token in Authorization header** (`Authorization: Bearer <jwt-token>`)
4. **Development mode fallback** (allows requests without authentication)

## API Key Format

API keys are generated in the format: `spt_<64-character-hex-string>`

Example: `spt_a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456`

## Using API Keys

### VS Code Extension

The VS Code extension sends API keys in both headers for maximum compatibility:

```typescript
headers: {
  'X-API-Key': 'your-api-key-here',
  'Authorization': 'Bearer your-api-key-here'
}
```

### cURL Examples

Using X-API-Key header:
```bash
curl -H "X-API-Key: vscode-extension-key" \
     http://localhost:8080/api/v1/scan/history
```

Using Authorization header:
```bash
curl -H "Authorization: Bearer vscode-extension-key" \
     http://localhost:8080/api/v1/scan/history
```

## API Key Management

### Create API Key

```bash
POST /api/v1/api-keys
Content-Type: application/json
Authorization: Bearer <your-jwt-token>

{
  "name": "My API Key",
  "permissions": ["scan:read", "scan:write", "report:read"],
  "expires_at": "2024-12-31T23:59:59Z"
}
```

Response:
```json
{
  "api_key": "spt_a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456",
  "key_info": {
    "id": "uuid",
    "name": "My API Key",
    "prefix": "spt_a1b2c3",
    "permissions": ["scan:read", "scan:write", "report:read"],
    "is_active": true,
    "expires_at": "2024-12-31T23:59:59Z",
    "created_at": "2024-01-01T00:00:00Z"
  },
  "message": "API key created successfully. Please store it securely as it won't be shown again."
}
```

### List API Keys

```bash
GET /api/v1/api-keys
Authorization: Bearer <your-jwt-token>
```

### Delete API Key

```bash
DELETE /api/v1/api-keys/{key-id}
Authorization: Bearer <your-jwt-token>
```

## Test API Keys

For development and testing, the following API keys are automatically created:

- **VS Code Extension**: `vscode-extension-key`
- **Development**: `dev-key-123`

These keys are associated with the admin user and have full permissions.

## Security Features

1. **Hashed Storage**: API keys are stored as SHA-256 hashes in the database
2. **Expiration**: API keys can have expiration dates
3. **Permissions**: Each API key has specific permissions
4. **Last Used Tracking**: The system tracks when each API key was last used
5. **User Association**: Each API key is associated with a specific user
6. **Prefix Display**: Only the first 10 characters are shown for identification

## Database Schema

The API keys are stored in the `api_keys` table with the following structure:

```sql
CREATE TABLE api_keys (
    id UUID PRIMARY KEY,
    user_id UUID REFERENCES users(id),
    name VARCHAR(255) NOT NULL,
    key_hash VARCHAR(64) UNIQUE NOT NULL,  -- SHA-256 hash
    prefix VARCHAR(10) NOT NULL,           -- First 10 chars for display
    permissions TEXT[] NOT NULL,
    is_active BOOLEAN DEFAULT true,
    last_used TIMESTAMP,
    expires_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

## Middleware Implementation

The authentication middleware (`AuthMiddleware`) now:

1. Accepts a storage interface for database operations
2. Validates API keys against the database
3. Sets user context from the associated user record
4. Updates the last_used timestamp asynchronously
5. Supports both header formats for maximum compatibility

## VS Code Extension Configuration

Update your VS Code settings:

```json
{
  "spt.serverUrl": "http://localhost:8080",
  "spt.apiKey": "vscode-extension-key"
}
```

The extension will automatically use the API key for all requests to the SPT backend.
