package api

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

// WebSocket upgrader
var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		// Get the origin from the request
		origin := r.Header.Get("Origin")

		// Allow same-origin requests
		if origin == "" {
			return true
		}

		// List of allowed origins for production
		allowedOrigins := []string{
			"http://localhost:4200",  // Angular dev server
			"http://localhost:3000",  // Alternative dev server
			"https://localhost:4200", // HTTPS dev server
			"https://localhost:3000", // HTTPS alternative
			"http://127.0.0.1:4200",  // Local IP
			"http://127.0.0.1:3000",  // Local IP alternative
		}

		// Check if origin is in allowed list
		for _, allowed := range allowedOrigins {
			if origin == allowed {
				return true
			}
		}

		// For production, you should add your actual domain here
		// Example: "https://yourdomain.com"

		// Log rejected origins for security monitoring
		if origin != "" {
			// Note: In production, you might want to use a proper logger
			// instead of printing to stdout
			fmt.Printf("WebSocket connection rejected from origin: %s\n", origin)
		}

		return false
	},
}

// SetupRoutes configures all API routes
func SetupRoutes(router *gin.Engine, handler *Handler, storage APIKeyStorage) {
	// Initialize WebSocket manager
	wsManager := NewWebSocketManager()
	go wsManager.Run()

	// Store WebSocket manager in handler for use in scan operations
	handler.wsManager = wsManager

	// Add middleware
	router.Use(AuthMiddleware(storage))
	router.Use(RateLimitMiddleware())
	router.Use(SecurityMiddleware())
	router.Use(ErrorHandlingMiddleware())
	router.Use(RequestIDMiddleware())
	router.Use(MetricsMiddleware())

	// Health check endpoint
	router.GET("/health", handler.HealthCheck)

	// API v1 routes
	v1 := router.Group("/api/v1")
	{
		// Authentication endpoints (public)
		auth := v1.Group("/auth")
		{
			auth.POST("/login", handler.Login)
			auth.POST("/register", handler.Register)
			auth.POST("/logout", handler.Logout)
			auth.POST("/refresh", handler.RefreshToken)
			auth.GET("/me", handler.GetCurrentUser)
		}

		// API key management endpoints (protected)
		apiKeys := v1.Group("/api-keys")
		{
			apiKeys.POST("", handler.CreateAPIKey)
			apiKeys.GET("", handler.GetAPIKeys)
			apiKeys.DELETE("/:id", handler.DeleteAPIKey)
		}

		// Scan endpoints
		scan := v1.Group("/scan")
		{
			scan.POST("/start", handler.StartScan)
			scan.GET("/result/:id", handler.GetScanResult)
			scan.GET("/history", handler.GetScanHistory)
			scan.GET("/file", handler.ScanFile)
			scan.POST("/:id/cancel", handler.CancelScan)
			scan.GET("/statistics", handler.GetScanStatistics)
			scan.GET("/:id/export", handler.ExportScanResults)
		}

		// Report endpoints
		report := v1.Group("/report")
		{
			report.POST("/generate", handler.GenerateReport)
		}

		// Security checklist endpoints
		v1.GET("/checklist", handler.GetSecurityChecklist)

		// Configuration endpoints
		config := v1.Group("/config")
		{
			config.GET("", handler.GetConfig)
			config.PUT("", handler.UpdateConfig)
		}
	}

	// WebSocket endpoint for real-time updates
	router.GET("/ws", wsManager.HandleWebSocket)

	// Static file serving (for frontend if needed)
	router.Static("/static", "./web/static")

	// Catch-all for SPA routing (if frontend is served from same server)
	router.NoRoute(func(c *gin.Context) {
		// Check if it's an API request
		if c.Request.URL.Path[:4] == "/api" {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "API endpoint not found",
				"path":  c.Request.URL.Path,
			})
			return
		}

		// For non-API requests, serve index.html (SPA routing)
		c.File("./web/index.html")
	})
}

// Old WebSocket handler removed - using WebSocketManager now
