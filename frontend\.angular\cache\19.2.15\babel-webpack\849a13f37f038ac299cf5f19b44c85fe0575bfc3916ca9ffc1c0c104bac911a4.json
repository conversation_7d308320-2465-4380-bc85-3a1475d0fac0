{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatTableModule } from '@angular/material/table';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/material/tabs\";\nimport * as i3 from \"@angular/material/card\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/chips\";\nimport * as i7 from \"@angular/material/expansion\";\nimport * as i8 from \"@angular/material/table\";\nfunction VscodeExtensionComponent_mat_card_62_div_9_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const benefit_r1 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(benefit_r1);\n  }\n}\nfunction VscodeExtensionComponent_mat_card_62_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 41)(1, \"h4\");\n    i0.ɵɵtext(2, \"Benefits:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ul\");\n    i0.ɵɵtemplate(4, VscodeExtensionComponent_mat_card_62_div_9_li_4_Template, 2, 1, \"li\", 42);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const feature_r2 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", feature_r2.benefits);\n  }\n}\nfunction VscodeExtensionComponent_mat_card_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 39)(1, \"mat-card-header\")(2, \"mat-icon\", 5);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-card-title\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-card-content\")(7, \"p\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, VscodeExtensionComponent_mat_card_62_div_9_Template, 5, 1, \"div\", 40);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const feature_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"background-color\", ctx_r2.getFeatureColor(feature_r2.icon));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", feature_r2.icon, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(feature_r2.title);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(feature_r2.description);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", feature_r2.benefits.length > 0);\n  }\n}\nfunction VscodeExtensionComponent_mat_card_70_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"div\", 22)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"terminal\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\");\n    i0.ɵɵtext(5, \"Command\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"pre\")(7, \"code\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const step_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(step_r4.command);\n  }\n}\nfunction VscodeExtensionComponent_mat_card_70_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const step_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(step_r4.notes);\n  }\n}\nfunction VscodeExtensionComponent_mat_card_70_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 43)(1, \"mat-card-header\")(2, \"div\", 44);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-card-title\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-card-subtitle\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"mat-card-content\")(9, \"p\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"div\", 45);\n    i0.ɵɵtemplate(12, VscodeExtensionComponent_mat_card_70_div_12_Template, 9, 1, \"div\", 46);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, VscodeExtensionComponent_mat_card_70_div_13_Template, 5, 1, \"div\", 47);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const step_r4 = ctx.$implicit;\n    const i_r5 = ctx.index;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i_r5 + 1);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(step_r4.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(step_r4.subtitle);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(step_r4.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", step_r4.command);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", step_r4.notes);\n  }\n}\nfunction VscodeExtensionComponent_th_127_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 49);\n    i0.ɵɵtext(1, \"Setting\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction VscodeExtensionComponent_td_128_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 50)(1, \"code\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const setting_r6 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(setting_r6.key);\n  }\n}\nfunction VscodeExtensionComponent_th_130_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 49);\n    i0.ɵɵtext(1, \"Type\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction VscodeExtensionComponent_td_131_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 50)(1, \"mat-chip\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const setting_r7 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(setting_r7.type);\n  }\n}\nfunction VscodeExtensionComponent_th_133_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 49);\n    i0.ɵɵtext(1, \"Default\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction VscodeExtensionComponent_td_134_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 50)(1, \"code\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const setting_r8 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(setting_r8.default);\n  }\n}\nfunction VscodeExtensionComponent_th_136_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 49);\n    i0.ɵɵtext(1, \"Description\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction VscodeExtensionComponent_td_137_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 50);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const setting_r9 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(setting_r9.description);\n  }\n}\nfunction VscodeExtensionComponent_tr_138_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 51);\n  }\n}\nfunction VscodeExtensionComponent_tr_139_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 52);\n  }\n}\nfunction VscodeExtensionComponent_mat_expansion_panel_148_div_11_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const step_r10 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(step_r10);\n  }\n}\nfunction VscodeExtensionComponent_mat_expansion_panel_148_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57)(1, \"h4\");\n    i0.ɵɵtext(2, \"Steps:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ol\");\n    i0.ɵɵtemplate(4, VscodeExtensionComponent_mat_expansion_panel_148_div_11_li_4_Template, 2, 1, \"li\", 42);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const usage_r11 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", usage_r11.steps);\n  }\n}\nfunction VscodeExtensionComponent_mat_expansion_panel_148_div_12_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\")(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"lightbulb\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const tip_r12 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(tip_r12);\n  }\n}\nfunction VscodeExtensionComponent_mat_expansion_panel_148_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 58)(1, \"h4\");\n    i0.ɵɵtext(2, \"Tips:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"ul\", 59);\n    i0.ɵɵtemplate(4, VscodeExtensionComponent_mat_expansion_panel_148_div_12_li_4_Template, 5, 1, \"li\", 42);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const usage_r11 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", usage_r11.tips);\n  }\n}\nfunction VscodeExtensionComponent_mat_expansion_panel_148_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-expansion-panel\", 53)(1, \"mat-expansion-panel-header\")(2, \"mat-panel-title\")(3, \"mat-icon\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-panel-description\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 54)(9, \"p\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, VscodeExtensionComponent_mat_expansion_panel_148_div_11_Template, 5, 1, \"div\", 55)(12, VscodeExtensionComponent_mat_expansion_panel_148_div_12_Template, 5, 1, \"div\", 56);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const usage_r11 = ctx.$implicit;\n    i0.ɵɵproperty(\"expanded\", usage_r11.expanded);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(usage_r11.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", usage_r11.title, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", usage_r11.description, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(usage_r11.details);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", usage_r11.steps);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", usage_r11.tips);\n  }\n}\nfunction VscodeExtensionComponent_mat_card_156_li_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const step_r13 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(step_r13);\n  }\n}\nfunction VscodeExtensionComponent_mat_card_156_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"h4\");\n    i0.ɵɵtext(2, \"Prevention:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const issue_r14 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(issue_r14.prevention);\n  }\n}\nfunction VscodeExtensionComponent_mat_card_156_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 60)(1, \"mat-card-header\")(2, \"mat-icon\", 5);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-card-title\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-card-subtitle\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"mat-card-content\")(9, \"div\", 61)(10, \"h4\");\n    i0.ɵɵtext(11, \"Solution:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"ol\");\n    i0.ɵɵtemplate(13, VscodeExtensionComponent_mat_card_156_li_13_Template, 2, 1, \"li\", 42);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(14, VscodeExtensionComponent_mat_card_156_div_14_Template, 5, 1, \"div\", 62);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const issue_r14 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"background-color\", issue_r14.severity === \"high\" ? \"#f44336\" : \"#ff9800\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", issue_r14.icon, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(issue_r14.problem);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(issue_r14.description);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", issue_r14.solution);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", issue_r14.prevention);\n  }\n}\nexport class VscodeExtensionComponent {\n  constructor() {\n    this.settingsColumns = ['key', 'type', 'default', 'description'];\n    this.configExample = `{\n  \"spt.enabled\": true,\n  \"spt.serverUrl\": \"http://localhost:8080\",\n  \"spt.apiKey\": \"your-api-key-here\",\n  \"spt.autoScan\": true,\n  \"spt.scanOnOpen\": false,\n  \"spt.chains\": [\"ethereum\", \"bitcoin\", \"general\"],\n  \"spt.severity\": \"medium\",\n  \"spt.showInlineDecorations\": true,\n  \"spt.showProblems\": true,\n  \"spt.enableCodeLens\": true,\n  \"spt.enableHover\": true\n}`;\n    this.extensionFeatures = [{\n      title: 'Real-time Security Scanning',\n      description: 'Automatic security analysis as you type, with instant feedback on potential vulnerabilities.',\n      icon: 'security',\n      benefits: ['Immediate vulnerability detection', 'Reduced security debt', 'Faster development cycles', 'Proactive security measures']\n    }, {\n      title: 'Inline Decorations',\n      description: 'Visual indicators directly in your code highlighting security issues and suggestions.',\n      icon: 'visibility',\n      benefits: ['Clear visual feedback', 'Context-aware highlighting', 'Severity-based color coding', 'Non-intrusive indicators']\n    }, {\n      title: 'Problems Panel Integration',\n      description: 'Security issues appear in VS Code\\'s Problems panel with detailed descriptions and fixes.',\n      icon: 'bug_report',\n      benefits: ['Centralized issue tracking', 'Detailed error descriptions', 'Quick navigation to issues', 'Integration with existing workflow']\n    }, {\n      title: 'CodeLens Integration',\n      description: 'Actionable security suggestions and metrics displayed directly above your code.',\n      icon: 'lens',\n      benefits: ['Contextual security metrics', 'One-click security actions', 'Code quality insights', 'Performance recommendations']\n    }, {\n      title: 'Hover Information',\n      description: 'Detailed security information and recommendations on hover over code elements.',\n      icon: 'info',\n      benefits: ['Instant security documentation', 'Best practice suggestions', 'Vulnerability explanations', 'Quick reference access']\n    }, {\n      title: 'Multi-chain Support',\n      description: 'Support for Ethereum, Bitcoin, and general blockchain security patterns.',\n      icon: 'link',\n      benefits: ['Comprehensive blockchain coverage', 'Chain-specific security rules', 'Unified security approach', 'Extensible architecture']\n    }];\n    this.installationSteps = [{\n      title: 'Install from VS Code Marketplace',\n      subtitle: 'Recommended method',\n      description: 'Search for \"SPT Security\" in the VS Code Extensions marketplace and install.',\n      command: 'ext install blockchain-spt.vscode-spt',\n      notes: 'Extension will be automatically activated after installation'\n    }, {\n      title: 'Configure Backend Connection',\n      subtitle: 'Connect to SPT server',\n      description: 'Configure the extension to connect to your SPT backend server.',\n      command: 'Configure spt.serverUrl in VS Code settings',\n      notes: 'Default server URL is http://localhost:8080'\n    }, {\n      title: 'Verify Installation',\n      subtitle: 'Test the connection',\n      description: 'Open a blockchain project and verify that security scanning is working.',\n      notes: 'Check the status bar for SPT connection indicator'\n    }];\n    this.configSettings = [{\n      key: 'spt.enabled',\n      type: 'boolean',\n      default: 'true',\n      description: 'Enable/disable SPT security analysis'\n    }, {\n      key: 'spt.serverUrl',\n      type: 'string',\n      default: 'http://localhost:8080',\n      description: 'SPT backend server URL'\n    }, {\n      key: 'spt.apiKey',\n      type: 'string',\n      default: '\"\"',\n      description: 'API key for authentication'\n    }, {\n      key: 'spt.autoScan',\n      type: 'boolean',\n      default: 'true',\n      description: 'Automatically scan files on save'\n    }, {\n      key: 'spt.scanOnOpen',\n      type: 'boolean',\n      default: 'false',\n      description: 'Automatically scan files when opened'\n    }, {\n      key: 'spt.chains',\n      type: 'array',\n      default: '[\"ethereum\", \"bitcoin\", \"general\"]',\n      description: 'Blockchain chains to analyze'\n    }, {\n      key: 'spt.severity',\n      type: 'string',\n      default: '\"medium\"',\n      description: 'Minimum severity level to show'\n    }, {\n      key: 'spt.showInlineDecorations',\n      type: 'boolean',\n      default: 'true',\n      description: 'Show inline security decorations'\n    }, {\n      key: 'spt.showProblems',\n      type: 'boolean',\n      default: 'true',\n      description: 'Show security issues in Problems panel'\n    }, {\n      key: 'spt.enableCodeLens',\n      type: 'boolean',\n      default: 'true',\n      description: 'Enable security-related CodeLens'\n    }, {\n      key: 'spt.enableHover',\n      type: 'boolean',\n      default: 'true',\n      description: 'Enable security information on hover'\n    }];\n    this.usageExamples = [{\n      title: 'Scanning Files',\n      description: 'How to scan files for security issues',\n      icon: 'scanner',\n      expanded: true,\n      details: 'Files are automatically scanned when saved (if auto-scan is enabled) or you can manually trigger scans.',\n      steps: ['Open a blockchain project in VS Code', 'Save a file to trigger automatic scanning', 'Or use Ctrl+Shift+P → \"SPT: Scan Current File\"', 'View results in Problems panel or inline decorations'],\n      tips: ['Enable auto-scan for continuous security monitoring', 'Use the Problems panel to navigate between issues', 'Check the status bar for scan progress']\n    }, {\n      title: 'Understanding Security Issues',\n      description: 'How to interpret and resolve security findings',\n      icon: 'help',\n      details: 'Security issues are displayed with severity levels and detailed descriptions to help you understand and fix them.',\n      steps: ['Hover over highlighted code to see issue details', 'Click on issues in Problems panel for more information', 'Use CodeLens actions for quick fixes', 'Follow the recommended solutions'],\n      tips: ['Start with critical and high severity issues', 'Use hover information for quick context', 'Check references for additional learning']\n    }, {\n      title: 'Configuring Scan Settings',\n      description: 'Customize scanning behavior for your project',\n      icon: 'tune',\n      details: 'Adjust scan settings to match your project requirements and development workflow.',\n      steps: ['Open VS Code settings (Ctrl+,)', 'Search for \"SPT\" to find extension settings', 'Adjust chains, severity, and scan triggers', 'Save settings and restart if needed'],\n      tips: ['Use workspace settings for project-specific configuration', 'Adjust severity threshold based on project maturity', 'Enable scan-on-open for comprehensive coverage']\n    }];\n    this.troubleshootingIssues = [{\n      problem: 'Extension Not Connecting to Server',\n      description: 'SPT extension cannot connect to the backend server',\n      icon: 'cloud_off',\n      severity: 'high',\n      solution: ['Verify SPT backend server is running on configured port', 'Check spt.serverUrl setting in VS Code preferences', 'Ensure firewall is not blocking the connection', 'Try restarting VS Code and the SPT server'],\n      prevention: 'Always start the SPT backend server before using the extension'\n    }, {\n      problem: 'No Security Issues Detected',\n      description: 'Extension is running but not finding any security issues',\n      icon: 'search_off',\n      severity: 'medium',\n      solution: ['Check if the file type is supported (Solidity, JavaScript, etc.)', 'Verify the correct blockchain chains are selected', 'Lower the severity threshold in settings', 'Ensure the file contains actual security-relevant code'],\n      prevention: 'Review supported file types and ensure proper project structure'\n    }, {\n      problem: 'Performance Issues',\n      description: 'Extension is causing VS Code to slow down',\n      icon: 'speed',\n      severity: 'medium',\n      solution: ['Disable auto-scan and use manual scanning', 'Increase scan timeout in settings', 'Exclude large files or directories from scanning', 'Reduce the number of enabled blockchain chains'],\n      prevention: 'Configure appropriate scan settings for your project size'\n    }];\n  }\n  getFeatureColor(icon) {\n    const colors = {\n      'security': '#1976d2',\n      'visibility': '#4caf50',\n      'bug_report': '#f44336',\n      'lens': '#ff9800',\n      'info': '#9c27b0',\n      'link': '#00bcd4'\n    };\n    return colors[icon] || '#1976d2';\n  }\n  static {\n    this.ɵfac = function VscodeExtensionComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || VscodeExtensionComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: VscodeExtensionComponent,\n      selectors: [[\"app-vscode-extension\"]],\n      decls: 157,\n      vars: 8,\n      consts: [[1, \"vscode-extension-container\"], [1, \"page-header\"], [1, \"page-subtitle\"], [1, \"extension-overview\"], [1, \"overview-card\"], [\"mat-card-avatar\", \"\"], [1, \"quick-stats\"], [1, \"stat\"], [\"mat-raised-button\", \"\", \"color\", \"primary\"], [\"mat-stroked-button\", \"\"], [\"animationDuration\", \"300ms\", 1, \"extension-tabs\"], [\"label\", \"Features\"], [1, \"tab-content\"], [1, \"features-grid\"], [\"class\", \"feature-card\", 4, \"ngFor\", \"ngForOf\"], [\"label\", \"Installation\"], [1, \"installation-steps\"], [\"class\", \"step-card\", 4, \"ngFor\", \"ngForOf\"], [\"label\", \"Configuration\"], [1, \"config-sections\"], [1, \"config-section\"], [1, \"code-block\"], [1, \"code-header\"], [\"mat-table\", \"\", 1, \"settings-table\", 3, \"dataSource\"], [\"matColumnDef\", \"key\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"type\"], [\"matColumnDef\", \"default\"], [\"matColumnDef\", \"description\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"label\", \"Usage\"], [1, \"usage-sections\"], [1, \"usage-panels\"], [3, \"expanded\", 4, \"ngFor\", \"ngForOf\"], [\"label\", \"Troubleshooting\"], [1, \"troubleshooting-sections\"], [\"class\", \"troubleshooting-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"feature-card\"], [\"class\", \"benefits-list\", 4, \"ngIf\"], [1, \"benefits-list\"], [4, \"ngFor\", \"ngForOf\"], [1, \"step-card\"], [\"mat-card-avatar\", \"\", 1, \"step-number\"], [1, \"step-content\"], [\"class\", \"code-block\", 4, \"ngIf\"], [\"class\", \"step-notes\", 4, \"ngIf\"], [1, \"step-notes\"], [\"mat-header-cell\", \"\"], [\"mat-cell\", \"\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\"], [3, \"expanded\"], [1, \"usage-content\"], [\"class\", \"usage-steps\", 4, \"ngIf\"], [\"class\", \"usage-tips\", 4, \"ngIf\"], [1, \"usage-steps\"], [1, \"usage-tips\"], [1, \"tips-list\"], [1, \"troubleshooting-card\"], [1, \"solution-steps\"], [\"class\", \"prevention-tips\", 4, \"ngIf\"], [1, \"prevention-tips\"]],\n      template: function VscodeExtensionComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h1\")(3, \"mat-icon\");\n          i0.ɵɵtext(4, \"extension\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(5, \" VS Code Extension \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p\", 2);\n          i0.ɵɵtext(7, \" Real-time security analysis directly in your code editor \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"div\", 3)(9, \"mat-card\", 4)(10, \"mat-card-header\")(11, \"mat-icon\", 5);\n          i0.ɵɵtext(12, \"info\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"mat-card-title\");\n          i0.ɵɵtext(14, \"SPT VS Code Extension\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"mat-card-subtitle\");\n          i0.ɵɵtext(16, \"Integrated security scanning for blockchain development\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"mat-card-content\")(18, \"p\");\n          i0.ɵɵtext(19, \"The SPT VS Code extension brings powerful security analysis directly into your development environment, providing real-time feedback and suggestions as you code.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"div\", 6)(21, \"div\", 7)(22, \"mat-icon\");\n          i0.ɵɵtext(23, \"security\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(24, \"div\")(25, \"strong\");\n          i0.ɵɵtext(26, \"Real-time Scanning\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(27, \"p\");\n          i0.ɵɵtext(28, \"Instant security feedback\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(29, \"div\", 7)(30, \"mat-icon\");\n          i0.ɵɵtext(31, \"lightbulb\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"div\")(33, \"strong\");\n          i0.ɵɵtext(34, \"Smart Suggestions\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"p\");\n          i0.ɵɵtext(36, \"Contextual security recommendations\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(37, \"div\", 7)(38, \"mat-icon\");\n          i0.ɵɵtext(39, \"integration_instructions\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"div\")(41, \"strong\");\n          i0.ɵɵtext(42, \"Seamless Integration\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"p\");\n          i0.ɵɵtext(44, \"Works with existing workflow\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(45, \"mat-card-actions\")(46, \"button\", 8)(47, \"mat-icon\");\n          i0.ɵɵtext(48, \"download\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(49, \" Install Extension \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"button\", 9)(51, \"mat-icon\");\n          i0.ɵɵtext(52, \"code\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(53, \" View Source \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(54, \"mat-tab-group\", 10)(55, \"mat-tab\", 11)(56, \"div\", 12)(57, \"h2\");\n          i0.ɵɵtext(58, \"Key Features\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(59, \"p\");\n          i0.ɵɵtext(60, \"Discover the powerful features that make SPT extension essential for secure blockchain development.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"div\", 13);\n          i0.ɵɵtemplate(62, VscodeExtensionComponent_mat_card_62_Template, 10, 6, \"mat-card\", 14);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(63, \"mat-tab\", 15)(64, \"div\", 12)(65, \"h2\");\n          i0.ɵɵtext(66, \"Installation Guide\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(67, \"p\");\n          i0.ɵɵtext(68, \"Get the SPT extension installed and configured in VS Code.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(69, \"div\", 16);\n          i0.ɵɵtemplate(70, VscodeExtensionComponent_mat_card_70_Template, 14, 6, \"mat-card\", 17);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(71, \"mat-tab\", 18)(72, \"div\", 12)(73, \"h2\");\n          i0.ɵɵtext(74, \"Extension Configuration\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(75, \"p\");\n          i0.ɵɵtext(76, \"Customize the SPT extension to fit your development workflow and preferences.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(77, \"div\", 19)(78, \"mat-card\", 20)(79, \"mat-card-header\")(80, \"mat-icon\", 5);\n          i0.ɵɵtext(81, \"settings\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(82, \"mat-card-title\");\n          i0.ɵɵtext(83, \"Settings Overview\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(84, \"mat-card-subtitle\");\n          i0.ɵɵtext(85, \"Configure extension behavior\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(86, \"mat-card-content\")(87, \"p\");\n          i0.ɵɵtext(88, \"Access extension settings through VS Code preferences:\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(89, \"div\", 21)(90, \"div\", 22)(91, \"mat-icon\");\n          i0.ɵɵtext(92, \"keyboard\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(93, \"span\");\n          i0.ɵɵtext(94, \"Keyboard Shortcut\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(95, \"pre\")(96, \"code\");\n          i0.ɵɵtext(97, \"Ctrl+Shift+P \\u2192 \\\"Preferences: Open Settings (JSON)\\\"\");\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(98, \"mat-card\", 20)(99, \"mat-card-header\")(100, \"mat-icon\", 5);\n          i0.ɵɵtext(101, \"code\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(102, \"mat-card-title\");\n          i0.ɵɵtext(103, \"Configuration Example\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(104, \"mat-card-subtitle\");\n          i0.ɵɵtext(105, \"settings.json\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(106, \"mat-card-content\")(107, \"div\", 21)(108, \"div\", 22)(109, \"mat-icon\");\n          i0.ɵɵtext(110, \"code\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(111, \"span\");\n          i0.ɵɵtext(112, \"JSON Configuration\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(113, \"pre\")(114, \"code\");\n          i0.ɵɵtext(115);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(116, \"mat-card\", 20)(117, \"mat-card-header\")(118, \"mat-icon\", 5);\n          i0.ɵɵtext(119, \"list\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(120, \"mat-card-title\");\n          i0.ɵɵtext(121, \"Available Settings\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(122, \"mat-card-subtitle\");\n          i0.ɵɵtext(123, \"Complete settings reference\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(124, \"mat-card-content\")(125, \"table\", 23);\n          i0.ɵɵelementContainerStart(126, 24);\n          i0.ɵɵtemplate(127, VscodeExtensionComponent_th_127_Template, 2, 0, \"th\", 25)(128, VscodeExtensionComponent_td_128_Template, 3, 1, \"td\", 26);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(129, 27);\n          i0.ɵɵtemplate(130, VscodeExtensionComponent_th_130_Template, 2, 0, \"th\", 25)(131, VscodeExtensionComponent_td_131_Template, 3, 1, \"td\", 26);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(132, 28);\n          i0.ɵɵtemplate(133, VscodeExtensionComponent_th_133_Template, 2, 0, \"th\", 25)(134, VscodeExtensionComponent_td_134_Template, 3, 1, \"td\", 26);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(135, 29);\n          i0.ɵɵtemplate(136, VscodeExtensionComponent_th_136_Template, 2, 0, \"th\", 25)(137, VscodeExtensionComponent_td_137_Template, 2, 1, \"td\", 26);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵtemplate(138, VscodeExtensionComponent_tr_138_Template, 1, 0, \"tr\", 30)(139, VscodeExtensionComponent_tr_139_Template, 1, 0, \"tr\", 31);\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(140, \"mat-tab\", 32)(141, \"div\", 12)(142, \"h2\");\n          i0.ɵɵtext(143, \"Using the Extension\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(144, \"p\");\n          i0.ɵɵtext(145, \"Learn how to effectively use SPT extension features in your daily development workflow.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(146, \"div\", 33)(147, \"mat-accordion\", 34);\n          i0.ɵɵtemplate(148, VscodeExtensionComponent_mat_expansion_panel_148_Template, 13, 7, \"mat-expansion-panel\", 35);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(149, \"mat-tab\", 36)(150, \"div\", 12)(151, \"h2\");\n          i0.ɵɵtext(152, \"Troubleshooting\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(153, \"p\");\n          i0.ɵɵtext(154, \"Common issues and solutions for the SPT VS Code extension.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(155, \"div\", 37);\n          i0.ɵɵtemplate(156, VscodeExtensionComponent_mat_card_156_Template, 15, 7, \"mat-card\", 38);\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(62);\n          i0.ɵɵproperty(\"ngForOf\", ctx.extensionFeatures);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngForOf\", ctx.installationSteps);\n          i0.ɵɵadvance(45);\n          i0.ɵɵtextInterpolate(ctx.configExample);\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"dataSource\", ctx.configSettings);\n          i0.ɵɵadvance(13);\n          i0.ɵɵproperty(\"matHeaderRowDef\", ctx.settingsColumns);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"matRowDefColumns\", ctx.settingsColumns);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngForOf\", ctx.usageExamples);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngForOf\", ctx.troubleshootingIssues);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.NgIf, MatTabsModule, i2.MatTab, i2.MatTabGroup, MatCardModule, i3.MatCard, i3.MatCardActions, i3.MatCardAvatar, i3.MatCardContent, i3.MatCardHeader, i3.MatCardSubtitle, i3.MatCardTitle, MatIconModule, i4.MatIcon, MatButtonModule, i5.MatButton, MatChipsModule, i6.MatChip, MatExpansionModule, i7.MatAccordion, i7.MatExpansionPanel, i7.MatExpansionPanelHeader, i7.MatExpansionPanelTitle, i7.MatExpansionPanelDescription, MatTableModule, i8.MatTable, i8.MatHeaderCellDef, i8.MatHeaderRowDef, i8.MatColumnDef, i8.MatCellDef, i8.MatRowDef, i8.MatHeaderCell, i8.MatCell, i8.MatHeaderRow, i8.MatRow],\n      styles: [\".vscode-extension-container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n}\\n\\n.page-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 32px;\\n}\\n\\n.page-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 12px;\\n  color: #1976d2;\\n  margin: 0 0 8px 0;\\n}\\n\\n.page-subtitle[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 1.1em;\\n  margin: 0;\\n}\\n\\n.extension-overview[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n\\n.overview-card[_ngcontent-%COMP%] {\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.quick-stats[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 16px;\\n  margin-top: 16px;\\n}\\n\\n.stat[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 12px;\\n  background: #f5f5f5;\\n  border-radius: 8px;\\n}\\n\\n.stat[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n}\\n\\n.stat[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  display: block;\\n  margin-bottom: 4px;\\n}\\n\\n.stat[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #666;\\n  font-size: 0.9em;\\n}\\n\\n.extension-tabs[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n\\n.tab-content[_ngcontent-%COMP%] {\\n  padding: 24px 0;\\n}\\n\\n.tab-content[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n  margin-bottom: 8px;\\n}\\n\\n.features-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\\n  gap: 24px;\\n  margin-top: 24px;\\n}\\n\\n.feature-card[_ngcontent-%COMP%] {\\n  height: 100%;\\n}\\n\\n.benefits-list[_ngcontent-%COMP%] {\\n  margin-top: 16px;\\n}\\n\\n.benefits-list[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  color: #1976d2;\\n  font-size: 0.9em;\\n}\\n\\n.benefits-list[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding-left: 20px;\\n}\\n\\n.benefits-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 4px;\\n  color: #666;\\n  font-size: 0.9em;\\n}\\n\\n.installation-steps[_ngcontent-%COMP%] {\\n  margin-top: 24px;\\n}\\n\\n.step-card[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n\\n.step-number[_ngcontent-%COMP%] {\\n  background: #1976d2;\\n  color: white;\\n  border-radius: 50%;\\n  width: 40px;\\n  height: 40px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-weight: bold;\\n}\\n\\n.step-content[_ngcontent-%COMP%] {\\n  margin-top: 16px;\\n}\\n\\n.code-block[_ngcontent-%COMP%] {\\n  border: 1px solid #e0e0e0;\\n  border-radius: 8px;\\n  overflow: hidden;\\n  margin: 16px 0;\\n}\\n\\n.code-header[_ngcontent-%COMP%] {\\n  background: #f5f5f5;\\n  padding: 8px 16px;\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-weight: 500;\\n  border-bottom: 1px solid #e0e0e0;\\n}\\n\\n.code-block[_ngcontent-%COMP%]   pre[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding: 16px;\\n  background: #fafafa;\\n  overflow-x: auto;\\n}\\n\\n.code-block[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  font-family: \\\"Courier New\\\", monospace;\\n  font-size: 0.9em;\\n}\\n\\n.step-notes[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-top: 12px;\\n  padding: 8px 12px;\\n  background: #e3f2fd;\\n  border-radius: 4px;\\n  color: #1976d2;\\n}\\n\\n.config-sections[_ngcontent-%COMP%] {\\n  margin-top: 24px;\\n}\\n\\n.config-section[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n\\n.settings-table[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border: 1px solid #e0e0e0;\\n  border-radius: 8px;\\n}\\n\\n.settings-table[_ngcontent-%COMP%]   code[_ngcontent-%COMP%] {\\n  background: #f5f5f5;\\n  padding: 2px 6px;\\n  border-radius: 4px;\\n  font-size: 0.9em;\\n}\\n\\n.usage-sections[_ngcontent-%COMP%] {\\n  margin-top: 24px;\\n}\\n\\n.usage-panels[_ngcontent-%COMP%] {\\n  margin-top: 16px;\\n}\\n\\n.usage-content[_ngcontent-%COMP%] {\\n  padding: 16px 0;\\n}\\n\\n.usage-steps[_ngcontent-%COMP%], \\n.usage-tips[_ngcontent-%COMP%] {\\n  margin-top: 16px;\\n}\\n\\n.usage-steps[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], \\n.usage-tips[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  color: #1976d2;\\n}\\n\\n.tips-list[_ngcontent-%COMP%] {\\n  list-style: none;\\n  padding: 0;\\n  margin: 0;\\n}\\n\\n.tips-list[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 8px;\\n  margin-bottom: 8px;\\n  padding: 8px;\\n  background: #fff3e0;\\n  border-radius: 4px;\\n}\\n\\n.tips-list[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #f57c00;\\n  margin-top: 2px;\\n}\\n\\n.troubleshooting-sections[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\\n  gap: 24px;\\n  margin-top: 24px;\\n}\\n\\n.troubleshooting-card[_ngcontent-%COMP%] {\\n  height: fit-content;\\n}\\n\\n.solution-steps[_ngcontent-%COMP%], \\n.prevention-tips[_ngcontent-%COMP%] {\\n  margin-top: 16px;\\n}\\n\\n.solution-steps[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%], \\n.prevention-tips[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  color: #1976d2;\\n}\\n\\n.solution-steps[_ngcontent-%COMP%]   ol[_ngcontent-%COMP%] {\\n  margin: 0;\\n  padding-left: 20px;\\n}\\n\\n.solution-steps[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n  color: #666;\\n}\\n\\n.prevention-tips[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #666;\\n}\\n\\n@media (max-width: 768px) {\\n  .quick-stats[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .features-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .troubleshooting-sections[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "MatTabsModule", "MatCardModule", "MatIconModule", "MatButtonModule", "MatChipsModule", "MatExpansionModule", "MatTableModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "benefit_r1", "ɵɵtemplate", "VscodeExtensionComponent_mat_card_62_div_9_li_4_Template", "ɵɵproperty", "feature_r2", "benefits", "VscodeExtensionComponent_mat_card_62_div_9_Template", "ɵɵstyleProp", "ctx_r2", "getFeatureColor", "icon", "ɵɵtextInterpolate1", "title", "description", "length", "step_r4", "command", "notes", "VscodeExtensionComponent_mat_card_70_div_12_Template", "VscodeExtensionComponent_mat_card_70_div_13_Template", "i_r5", "subtitle", "setting_r6", "key", "setting_r7", "type", "setting_r8", "default", "setting_r9", "ɵɵelement", "step_r10", "VscodeExtensionComponent_mat_expansion_panel_148_div_11_li_4_Template", "usage_r11", "steps", "tip_r12", "VscodeExtensionComponent_mat_expansion_panel_148_div_12_li_4_Template", "tips", "VscodeExtensionComponent_mat_expansion_panel_148_div_11_Template", "VscodeExtensionComponent_mat_expansion_panel_148_div_12_Template", "expanded", "details", "step_r13", "issue_r14", "prevention", "VscodeExtensionComponent_mat_card_156_li_13_Template", "VscodeExtensionComponent_mat_card_156_div_14_Template", "severity", "problem", "solution", "VscodeExtensionComponent", "constructor", "settingsColumns", "config<PERSON><PERSON><PERSON>", "extensionFeatures", "installationSteps", "configSettings", "usageExamples", "troubleshootingIssues", "colors", "selectors", "decls", "vars", "consts", "template", "VscodeExtensionComponent_Template", "rf", "ctx", "VscodeExtensionComponent_mat_card_62_Template", "VscodeExtensionComponent_mat_card_70_Template", "ɵɵelementContainerStart", "VscodeExtensionComponent_th_127_Template", "VscodeExtensionComponent_td_128_Template", "VscodeExtensionComponent_th_130_Template", "VscodeExtensionComponent_td_131_Template", "VscodeExtensionComponent_th_133_Template", "VscodeExtensionComponent_td_134_Template", "VscodeExtensionComponent_th_136_Template", "VscodeExtensionComponent_td_137_Template", "VscodeExtensionComponent_tr_138_Template", "VscodeExtensionComponent_tr_139_Template", "VscodeExtensionComponent_mat_expansion_panel_148_Template", "VscodeExtensionComponent_mat_card_156_Template", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i2", "Mat<PERSON><PERSON>", "MatTabGroup", "i3", "MatCard", "MatCardActions", "MatCardAvatar", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardSubtitle", "MatCardTitle", "i4", "MatIcon", "i5", "MatButton", "i6", "MatChip", "i7", "Mat<PERSON><PERSON>rdi<PERSON>", "MatExpansionPanel", "MatExpansionPanelHeader", "MatExpansionPanelTitle", "MatExpansionPanelDescription", "i8", "MatTable", "MatHeaderCellDef", "MatHeaderRowDef", "MatColumnDef", "MatCellDef", "MatRowDef", "MatHeaderCell", "Mat<PERSON>ell", "MatHeaderRow", "MatRow", "styles"], "sources": ["D:\\TGI\\Blockchain.SPT\\frontend\\src\\app\\modules\\documentation\\components\\vscode-extension\\vscode-extension.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatTableModule } from '@angular/material/table';\n\ninterface ExtensionFeature {\n  title: string;\n  description: string;\n  icon: string;\n  screenshot?: string;\n  benefits: string[];\n}\n\ninterface ConfigSetting {\n  key: string;\n  type: string;\n  default: string;\n  description: string;\n}\n\n@Component({\n  selector: 'app-vscode-extension',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatTabsModule,\n    MatCardModule,\n    MatIconModule,\n    MatButtonModule,\n    MatChipsModule,\n    MatExpansionModule,\n    MatTableModule\n  ],\n  template: `\n    <div class=\"vscode-extension-container\">\n      <div class=\"page-header\">\n        <h1>\n          <mat-icon>extension</mat-icon>\n          VS Code Extension\n        </h1>\n        <p class=\"page-subtitle\">\n          Real-time security analysis directly in your code editor\n        </p>\n      </div>\n\n      <div class=\"extension-overview\">\n        <mat-card class=\"overview-card\">\n          <mat-card-header>\n            <mat-icon mat-card-avatar>info</mat-icon>\n            <mat-card-title>SPT VS Code Extension</mat-card-title>\n            <mat-card-subtitle>Integrated security scanning for blockchain development</mat-card-subtitle>\n          </mat-card-header>\n          <mat-card-content>\n            <p>The SPT VS Code extension brings powerful security analysis directly into your development environment, providing real-time feedback and suggestions as you code.</p>\n            <div class=\"quick-stats\">\n              <div class=\"stat\">\n                <mat-icon>security</mat-icon>\n                <div>\n                  <strong>Real-time Scanning</strong>\n                  <p>Instant security feedback</p>\n                </div>\n              </div>\n              <div class=\"stat\">\n                <mat-icon>lightbulb</mat-icon>\n                <div>\n                  <strong>Smart Suggestions</strong>\n                  <p>Contextual security recommendations</p>\n                </div>\n              </div>\n              <div class=\"stat\">\n                <mat-icon>integration_instructions</mat-icon>\n                <div>\n                  <strong>Seamless Integration</strong>\n                  <p>Works with existing workflow</p>\n                </div>\n              </div>\n            </div>\n          </mat-card-content>\n          <mat-card-actions>\n            <button mat-raised-button color=\"primary\">\n              <mat-icon>download</mat-icon>\n              Install Extension\n            </button>\n            <button mat-stroked-button>\n              <mat-icon>code</mat-icon>\n              View Source\n            </button>\n          </mat-card-actions>\n        </mat-card>\n      </div>\n\n      <mat-tab-group class=\"extension-tabs\" animationDuration=\"300ms\">\n        <!-- Features Tab -->\n        <mat-tab label=\"Features\">\n          <div class=\"tab-content\">\n            <h2>Key Features</h2>\n            <p>Discover the powerful features that make SPT extension essential for secure blockchain development.</p>\n            \n            <div class=\"features-grid\">\n              <mat-card class=\"feature-card\" *ngFor=\"let feature of extensionFeatures\">\n                <mat-card-header>\n                  <mat-icon mat-card-avatar [style.background-color]=\"getFeatureColor(feature.icon)\">\n                    {{ feature.icon }}\n                  </mat-icon>\n                  <mat-card-title>{{ feature.title }}</mat-card-title>\n                </mat-card-header>\n                <mat-card-content>\n                  <p>{{ feature.description }}</p>\n                  <div class=\"benefits-list\" *ngIf=\"feature.benefits.length > 0\">\n                    <h4>Benefits:</h4>\n                    <ul>\n                      <li *ngFor=\"let benefit of feature.benefits\">{{ benefit }}</li>\n                    </ul>\n                  </div>\n                </mat-card-content>\n              </mat-card>\n            </div>\n          </div>\n        </mat-tab>\n\n        <!-- Installation Tab -->\n        <mat-tab label=\"Installation\">\n          <div class=\"tab-content\">\n            <h2>Installation Guide</h2>\n            <p>Get the SPT extension installed and configured in VS Code.</p>\n            \n            <div class=\"installation-steps\">\n              <mat-card class=\"step-card\" *ngFor=\"let step of installationSteps; let i = index\">\n                <mat-card-header>\n                  <div mat-card-avatar class=\"step-number\">{{ i + 1 }}</div>\n                  <mat-card-title>{{ step.title }}</mat-card-title>\n                  <mat-card-subtitle>{{ step.subtitle }}</mat-card-subtitle>\n                </mat-card-header>\n                <mat-card-content>\n                  <p>{{ step.description }}</p>\n                  <div class=\"step-content\">\n                    <div class=\"code-block\" *ngIf=\"step.command\">\n                      <div class=\"code-header\">\n                        <mat-icon>terminal</mat-icon>\n                        <span>Command</span>\n                      </div>\n                      <pre><code>{{ step.command }}</code></pre>\n                    </div>\n\n                  </div>\n                  <div class=\"step-notes\" *ngIf=\"step.notes\">\n                    <mat-icon>info</mat-icon>\n                    <span>{{ step.notes }}</span>\n                  </div>\n                </mat-card-content>\n              </mat-card>\n            </div>\n          </div>\n        </mat-tab>\n\n        <!-- Configuration Tab -->\n        <mat-tab label=\"Configuration\">\n          <div class=\"tab-content\">\n            <h2>Extension Configuration</h2>\n            <p>Customize the SPT extension to fit your development workflow and preferences.</p>\n            \n            <div class=\"config-sections\">\n              <mat-card class=\"config-section\">\n                <mat-card-header>\n                  <mat-icon mat-card-avatar>settings</mat-icon>\n                  <mat-card-title>Settings Overview</mat-card-title>\n                  <mat-card-subtitle>Configure extension behavior</mat-card-subtitle>\n                </mat-card-header>\n                <mat-card-content>\n                  <p>Access extension settings through VS Code preferences:</p>\n                  <div class=\"code-block\">\n                    <div class=\"code-header\">\n                      <mat-icon>keyboard</mat-icon>\n                      <span>Keyboard Shortcut</span>\n                    </div>\n                    <pre><code>Ctrl+Shift+P → \"Preferences: Open Settings (JSON)\"</code></pre>\n                  </div>\n                </mat-card-content>\n              </mat-card>\n\n              <mat-card class=\"config-section\">\n                <mat-card-header>\n                  <mat-icon mat-card-avatar>code</mat-icon>\n                  <mat-card-title>Configuration Example</mat-card-title>\n                  <mat-card-subtitle>settings.json</mat-card-subtitle>\n                </mat-card-header>\n                <mat-card-content>\n                  <div class=\"code-block\">\n                    <div class=\"code-header\">\n                      <mat-icon>code</mat-icon>\n                      <span>JSON Configuration</span>\n                    </div>\n                    <pre><code>{{ configExample }}</code></pre>\n                  </div>\n                </mat-card-content>\n              </mat-card>\n\n              <mat-card class=\"config-section\">\n                <mat-card-header>\n                  <mat-icon mat-card-avatar>list</mat-icon>\n                  <mat-card-title>Available Settings</mat-card-title>\n                  <mat-card-subtitle>Complete settings reference</mat-card-subtitle>\n                </mat-card-header>\n                <mat-card-content>\n                  <table mat-table [dataSource]=\"configSettings\" class=\"settings-table\">\n                    <ng-container matColumnDef=\"key\">\n                      <th mat-header-cell *matHeaderCellDef>Setting</th>\n                      <td mat-cell *matCellDef=\"let setting\">\n                        <code>{{ setting.key }}</code>\n                      </td>\n                    </ng-container>\n                    <ng-container matColumnDef=\"type\">\n                      <th mat-header-cell *matHeaderCellDef>Type</th>\n                      <td mat-cell *matCellDef=\"let setting\">\n                        <mat-chip>{{ setting.type }}</mat-chip>\n                      </td>\n                    </ng-container>\n                    <ng-container matColumnDef=\"default\">\n                      <th mat-header-cell *matHeaderCellDef>Default</th>\n                      <td mat-cell *matCellDef=\"let setting\">\n                        <code>{{ setting.default }}</code>\n                      </td>\n                    </ng-container>\n                    <ng-container matColumnDef=\"description\">\n                      <th mat-header-cell *matHeaderCellDef>Description</th>\n                      <td mat-cell *matCellDef=\"let setting\">{{ setting.description }}</td>\n                    </ng-container>\n                    <tr mat-header-row *matHeaderRowDef=\"settingsColumns\"></tr>\n                    <tr mat-row *matRowDef=\"let row; columns: settingsColumns;\"></tr>\n                  </table>\n                </mat-card-content>\n              </mat-card>\n            </div>\n          </div>\n        </mat-tab>\n\n        <!-- Usage Tab -->\n        <mat-tab label=\"Usage\">\n          <div class=\"tab-content\">\n            <h2>Using the Extension</h2>\n            <p>Learn how to effectively use SPT extension features in your daily development workflow.</p>\n            \n            <div class=\"usage-sections\">\n              <mat-accordion class=\"usage-panels\">\n                <mat-expansion-panel *ngFor=\"let usage of usageExamples\" [expanded]=\"usage.expanded\">\n                  <mat-expansion-panel-header>\n                    <mat-panel-title>\n                      <mat-icon>{{ usage.icon }}</mat-icon>\n                      {{ usage.title }}\n                    </mat-panel-title>\n                    <mat-panel-description>\n                      {{ usage.description }}\n                    </mat-panel-description>\n                  </mat-expansion-panel-header>\n                  \n                  <div class=\"usage-content\">\n                    <p>{{ usage.details }}</p>\n                    <div class=\"usage-steps\" *ngIf=\"usage.steps\">\n                      <h4>Steps:</h4>\n                      <ol>\n                        <li *ngFor=\"let step of usage.steps\">{{ step }}</li>\n                      </ol>\n                    </div>\n                    <div class=\"usage-tips\" *ngIf=\"usage.tips\">\n                      <h4>Tips:</h4>\n                      <ul class=\"tips-list\">\n                        <li *ngFor=\"let tip of usage.tips\">\n                          <mat-icon>lightbulb</mat-icon>\n                          <span>{{ tip }}</span>\n                        </li>\n                      </ul>\n                    </div>\n                  </div>\n                </mat-expansion-panel>\n              </mat-accordion>\n            </div>\n          </div>\n        </mat-tab>\n\n        <!-- Troubleshooting Tab -->\n        <mat-tab label=\"Troubleshooting\">\n          <div class=\"tab-content\">\n            <h2>Troubleshooting</h2>\n            <p>Common issues and solutions for the SPT VS Code extension.</p>\n            \n            <div class=\"troubleshooting-sections\">\n              <mat-card class=\"troubleshooting-card\" *ngFor=\"let issue of troubleshootingIssues\">\n                <mat-card-header>\n                  <mat-icon mat-card-avatar [style.background-color]=\"issue.severity === 'high' ? '#f44336' : '#ff9800'\">\n                    {{ issue.icon }}\n                  </mat-icon>\n                  <mat-card-title>{{ issue.problem }}</mat-card-title>\n                  <mat-card-subtitle>{{ issue.description }}</mat-card-subtitle>\n                </mat-card-header>\n                <mat-card-content>\n                  <div class=\"solution-steps\">\n                    <h4>Solution:</h4>\n                    <ol>\n                      <li *ngFor=\"let step of issue.solution\">{{ step }}</li>\n                    </ol>\n                  </div>\n                  <div class=\"prevention-tips\" *ngIf=\"issue.prevention\">\n                    <h4>Prevention:</h4>\n                    <p>{{ issue.prevention }}</p>\n                  </div>\n                </mat-card-content>\n              </mat-card>\n            </div>\n          </div>\n        </mat-tab>\n      </mat-tab-group>\n    </div>\n  `,\n  styles: [`\n    .vscode-extension-container {\n      max-width: 1200px;\n      margin: 0 auto;\n    }\n\n    .page-header {\n      text-align: center;\n      margin-bottom: 32px;\n    }\n\n    .page-header h1 {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      gap: 12px;\n      color: #1976d2;\n      margin: 0 0 8px 0;\n    }\n\n    .page-subtitle {\n      color: #666;\n      font-size: 1.1em;\n      margin: 0;\n    }\n\n    .extension-overview {\n      margin-bottom: 32px;\n    }\n\n    .overview-card {\n      border: 1px solid #e0e0e0;\n    }\n\n    .quick-stats {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n      gap: 16px;\n      margin-top: 16px;\n    }\n\n    .stat {\n      display: flex;\n      align-items: center;\n      gap: 12px;\n      padding: 12px;\n      background: #f5f5f5;\n      border-radius: 8px;\n    }\n\n    .stat mat-icon {\n      color: #1976d2;\n    }\n\n    .stat strong {\n      display: block;\n      margin-bottom: 4px;\n    }\n\n    .stat p {\n      margin: 0;\n      color: #666;\n      font-size: 0.9em;\n    }\n\n    .extension-tabs {\n      margin-bottom: 32px;\n    }\n\n    .tab-content {\n      padding: 24px 0;\n    }\n\n    .tab-content h2 {\n      color: #1976d2;\n      margin-bottom: 8px;\n    }\n\n    .features-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\n      gap: 24px;\n      margin-top: 24px;\n    }\n\n    .feature-card {\n      height: 100%;\n    }\n\n    .benefits-list {\n      margin-top: 16px;\n    }\n\n    .benefits-list h4 {\n      margin: 0 0 8px 0;\n      color: #1976d2;\n      font-size: 0.9em;\n    }\n\n    .benefits-list ul {\n      margin: 0;\n      padding-left: 20px;\n    }\n\n    .benefits-list li {\n      margin-bottom: 4px;\n      color: #666;\n      font-size: 0.9em;\n    }\n\n    .installation-steps {\n      margin-top: 24px;\n    }\n\n    .step-card {\n      margin-bottom: 24px;\n    }\n\n    .step-number {\n      background: #1976d2;\n      color: white;\n      border-radius: 50%;\n      width: 40px;\n      height: 40px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-weight: bold;\n    }\n\n    .step-content {\n      margin-top: 16px;\n    }\n\n    .code-block {\n      border: 1px solid #e0e0e0;\n      border-radius: 8px;\n      overflow: hidden;\n      margin: 16px 0;\n    }\n\n    .code-header {\n      background: #f5f5f5;\n      padding: 8px 16px;\n      display: flex;\n      align-items: center;\n      gap: 8px;\n      font-weight: 500;\n      border-bottom: 1px solid #e0e0e0;\n    }\n\n    .code-block pre {\n      margin: 0;\n      padding: 16px;\n      background: #fafafa;\n      overflow-x: auto;\n    }\n\n    .code-block code {\n      font-family: 'Courier New', monospace;\n      font-size: 0.9em;\n    }\n\n\n\n    .step-notes {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n      margin-top: 12px;\n      padding: 8px 12px;\n      background: #e3f2fd;\n      border-radius: 4px;\n      color: #1976d2;\n    }\n\n    .config-sections {\n      margin-top: 24px;\n    }\n\n    .config-section {\n      margin-bottom: 24px;\n    }\n\n    .settings-table {\n      width: 100%;\n      border: 1px solid #e0e0e0;\n      border-radius: 8px;\n    }\n\n    .settings-table code {\n      background: #f5f5f5;\n      padding: 2px 6px;\n      border-radius: 4px;\n      font-size: 0.9em;\n    }\n\n    .usage-sections {\n      margin-top: 24px;\n    }\n\n    .usage-panels {\n      margin-top: 16px;\n    }\n\n    .usage-content {\n      padding: 16px 0;\n    }\n\n    .usage-steps,\n    .usage-tips {\n      margin-top: 16px;\n    }\n\n    .usage-steps h4,\n    .usage-tips h4 {\n      margin: 0 0 8px 0;\n      color: #1976d2;\n    }\n\n    .tips-list {\n      list-style: none;\n      padding: 0;\n      margin: 0;\n    }\n\n    .tips-list li {\n      display: flex;\n      align-items: flex-start;\n      gap: 8px;\n      margin-bottom: 8px;\n      padding: 8px;\n      background: #fff3e0;\n      border-radius: 4px;\n    }\n\n    .tips-list mat-icon {\n      color: #f57c00;\n      margin-top: 2px;\n    }\n\n    .troubleshooting-sections {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\n      gap: 24px;\n      margin-top: 24px;\n    }\n\n    .troubleshooting-card {\n      height: fit-content;\n    }\n\n    .solution-steps,\n    .prevention-tips {\n      margin-top: 16px;\n    }\n\n    .solution-steps h4,\n    .prevention-tips h4 {\n      margin: 0 0 8px 0;\n      color: #1976d2;\n    }\n\n    .solution-steps ol {\n      margin: 0;\n      padding-left: 20px;\n    }\n\n    .solution-steps li {\n      margin-bottom: 8px;\n      color: #666;\n    }\n\n    .prevention-tips p {\n      margin: 0;\n      color: #666;\n    }\n\n    @media (max-width: 768px) {\n      .quick-stats {\n        grid-template-columns: 1fr;\n      }\n      \n      .features-grid {\n        grid-template-columns: 1fr;\n      }\n      \n      .troubleshooting-sections {\n        grid-template-columns: 1fr;\n      }\n    }\n  `]\n})\nexport class VscodeExtensionComponent {\n  settingsColumns: string[] = ['key', 'type', 'default', 'description'];\n\n  configExample = `{\n  \"spt.enabled\": true,\n  \"spt.serverUrl\": \"http://localhost:8080\",\n  \"spt.apiKey\": \"your-api-key-here\",\n  \"spt.autoScan\": true,\n  \"spt.scanOnOpen\": false,\n  \"spt.chains\": [\"ethereum\", \"bitcoin\", \"general\"],\n  \"spt.severity\": \"medium\",\n  \"spt.showInlineDecorations\": true,\n  \"spt.showProblems\": true,\n  \"spt.enableCodeLens\": true,\n  \"spt.enableHover\": true\n}`;\n\n  extensionFeatures: ExtensionFeature[] = [\n    {\n      title: 'Real-time Security Scanning',\n      description: 'Automatic security analysis as you type, with instant feedback on potential vulnerabilities.',\n      icon: 'security',\n      benefits: [\n        'Immediate vulnerability detection',\n        'Reduced security debt',\n        'Faster development cycles',\n        'Proactive security measures'\n      ]\n    },\n    {\n      title: 'Inline Decorations',\n      description: 'Visual indicators directly in your code highlighting security issues and suggestions.',\n      icon: 'visibility',\n      benefits: [\n        'Clear visual feedback',\n        'Context-aware highlighting',\n        'Severity-based color coding',\n        'Non-intrusive indicators'\n      ]\n    },\n    {\n      title: 'Problems Panel Integration',\n      description: 'Security issues appear in VS Code\\'s Problems panel with detailed descriptions and fixes.',\n      icon: 'bug_report',\n      benefits: [\n        'Centralized issue tracking',\n        'Detailed error descriptions',\n        'Quick navigation to issues',\n        'Integration with existing workflow'\n      ]\n    },\n    {\n      title: 'CodeLens Integration',\n      description: 'Actionable security suggestions and metrics displayed directly above your code.',\n      icon: 'lens',\n      benefits: [\n        'Contextual security metrics',\n        'One-click security actions',\n        'Code quality insights',\n        'Performance recommendations'\n      ]\n    },\n    {\n      title: 'Hover Information',\n      description: 'Detailed security information and recommendations on hover over code elements.',\n      icon: 'info',\n      benefits: [\n        'Instant security documentation',\n        'Best practice suggestions',\n        'Vulnerability explanations',\n        'Quick reference access'\n      ]\n    },\n    {\n      title: 'Multi-chain Support',\n      description: 'Support for Ethereum, Bitcoin, and general blockchain security patterns.',\n      icon: 'link',\n      benefits: [\n        'Comprehensive blockchain coverage',\n        'Chain-specific security rules',\n        'Unified security approach',\n        'Extensible architecture'\n      ]\n    }\n  ];\n\n  installationSteps = [\n    {\n      title: 'Install from VS Code Marketplace',\n      subtitle: 'Recommended method',\n      description: 'Search for \"SPT Security\" in the VS Code Extensions marketplace and install.',\n      command: 'ext install blockchain-spt.vscode-spt',\n      notes: 'Extension will be automatically activated after installation'\n    },\n    {\n      title: 'Configure Backend Connection',\n      subtitle: 'Connect to SPT server',\n      description: 'Configure the extension to connect to your SPT backend server.',\n      command: 'Configure spt.serverUrl in VS Code settings',\n      notes: 'Default server URL is http://localhost:8080'\n    },\n    {\n      title: 'Verify Installation',\n      subtitle: 'Test the connection',\n      description: 'Open a blockchain project and verify that security scanning is working.',\n      notes: 'Check the status bar for SPT connection indicator'\n    }\n  ];\n\n  configSettings: ConfigSetting[] = [\n    { key: 'spt.enabled', type: 'boolean', default: 'true', description: 'Enable/disable SPT security analysis' },\n    { key: 'spt.serverUrl', type: 'string', default: 'http://localhost:8080', description: 'SPT backend server URL' },\n    { key: 'spt.apiKey', type: 'string', default: '\"\"', description: 'API key for authentication' },\n    { key: 'spt.autoScan', type: 'boolean', default: 'true', description: 'Automatically scan files on save' },\n    { key: 'spt.scanOnOpen', type: 'boolean', default: 'false', description: 'Automatically scan files when opened' },\n    { key: 'spt.chains', type: 'array', default: '[\"ethereum\", \"bitcoin\", \"general\"]', description: 'Blockchain chains to analyze' },\n    { key: 'spt.severity', type: 'string', default: '\"medium\"', description: 'Minimum severity level to show' },\n    { key: 'spt.showInlineDecorations', type: 'boolean', default: 'true', description: 'Show inline security decorations' },\n    { key: 'spt.showProblems', type: 'boolean', default: 'true', description: 'Show security issues in Problems panel' },\n    { key: 'spt.enableCodeLens', type: 'boolean', default: 'true', description: 'Enable security-related CodeLens' },\n    { key: 'spt.enableHover', type: 'boolean', default: 'true', description: 'Enable security information on hover' }\n  ];\n\n  usageExamples = [\n    {\n      title: 'Scanning Files',\n      description: 'How to scan files for security issues',\n      icon: 'scanner',\n      expanded: true,\n      details: 'Files are automatically scanned when saved (if auto-scan is enabled) or you can manually trigger scans.',\n      steps: [\n        'Open a blockchain project in VS Code',\n        'Save a file to trigger automatic scanning',\n        'Or use Ctrl+Shift+P → \"SPT: Scan Current File\"',\n        'View results in Problems panel or inline decorations'\n      ],\n      tips: [\n        'Enable auto-scan for continuous security monitoring',\n        'Use the Problems panel to navigate between issues',\n        'Check the status bar for scan progress'\n      ]\n    },\n    {\n      title: 'Understanding Security Issues',\n      description: 'How to interpret and resolve security findings',\n      icon: 'help',\n      details: 'Security issues are displayed with severity levels and detailed descriptions to help you understand and fix them.',\n      steps: [\n        'Hover over highlighted code to see issue details',\n        'Click on issues in Problems panel for more information',\n        'Use CodeLens actions for quick fixes',\n        'Follow the recommended solutions'\n      ],\n      tips: [\n        'Start with critical and high severity issues',\n        'Use hover information for quick context',\n        'Check references for additional learning'\n      ]\n    },\n    {\n      title: 'Configuring Scan Settings',\n      description: 'Customize scanning behavior for your project',\n      icon: 'tune',\n      details: 'Adjust scan settings to match your project requirements and development workflow.',\n      steps: [\n        'Open VS Code settings (Ctrl+,)',\n        'Search for \"SPT\" to find extension settings',\n        'Adjust chains, severity, and scan triggers',\n        'Save settings and restart if needed'\n      ],\n      tips: [\n        'Use workspace settings for project-specific configuration',\n        'Adjust severity threshold based on project maturity',\n        'Enable scan-on-open for comprehensive coverage'\n      ]\n    }\n  ];\n\n  troubleshootingIssues = [\n    {\n      problem: 'Extension Not Connecting to Server',\n      description: 'SPT extension cannot connect to the backend server',\n      icon: 'cloud_off',\n      severity: 'high',\n      solution: [\n        'Verify SPT backend server is running on configured port',\n        'Check spt.serverUrl setting in VS Code preferences',\n        'Ensure firewall is not blocking the connection',\n        'Try restarting VS Code and the SPT server'\n      ],\n      prevention: 'Always start the SPT backend server before using the extension'\n    },\n    {\n      problem: 'No Security Issues Detected',\n      description: 'Extension is running but not finding any security issues',\n      icon: 'search_off',\n      severity: 'medium',\n      solution: [\n        'Check if the file type is supported (Solidity, JavaScript, etc.)',\n        'Verify the correct blockchain chains are selected',\n        'Lower the severity threshold in settings',\n        'Ensure the file contains actual security-relevant code'\n      ],\n      prevention: 'Review supported file types and ensure proper project structure'\n    },\n    {\n      problem: 'Performance Issues',\n      description: 'Extension is causing VS Code to slow down',\n      icon: 'speed',\n      severity: 'medium',\n      solution: [\n        'Disable auto-scan and use manual scanning',\n        'Increase scan timeout in settings',\n        'Exclude large files or directories from scanning',\n        'Reduce the number of enabled blockchain chains'\n      ],\n      prevention: 'Configure appropriate scan settings for your project size'\n    }\n  ];\n\n  getFeatureColor(icon: string): string {\n    const colors: { [key: string]: string } = {\n      'security': '#1976d2',\n      'visibility': '#4caf50',\n      'bug_report': '#f44336',\n      'lens': '#ff9800',\n      'info': '#9c27b0',\n      'link': '#00bcd4'\n    };\n    return colors[icon] || '#1976d2';\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,cAAc,QAAQ,yBAAyB;;;;;;;;;;;;IA4GlCC,EAAA,CAAAC,cAAA,SAA6C;IAAAD,EAAA,CAAAE,MAAA,GAAa;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAAlBH,EAAA,CAAAI,SAAA,EAAa;IAAbJ,EAAA,CAAAK,iBAAA,CAAAC,UAAA,CAAa;;;;;IAF5DN,EADF,CAAAC,cAAA,cAA+D,SACzD;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClBH,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAO,UAAA,IAAAC,wDAAA,iBAA6C;IAEjDR,EADE,CAAAG,YAAA,EAAK,EACD;;;;IAFsBH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAS,UAAA,YAAAC,UAAA,CAAAC,QAAA,CAAmB;;;;;IAV/CX,EAFJ,CAAAC,cAAA,mBAAyE,sBACtD,kBACoE;IACjFD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACXH,EAAA,CAAAC,cAAA,qBAAgB;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IACrCF,EADqC,CAAAG,YAAA,EAAiB,EACpC;IAEhBH,EADF,CAAAC,cAAA,uBAAkB,QACb;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAChCH,EAAA,CAAAO,UAAA,IAAAK,mDAAA,kBAA+D;IAOnEZ,EADE,CAAAG,YAAA,EAAmB,EACV;;;;;IAdmBH,EAAA,CAAAI,SAAA,GAAwD;IAAxDJ,EAAA,CAAAa,WAAA,qBAAAC,MAAA,CAAAC,eAAA,CAAAL,UAAA,CAAAM,IAAA,EAAwD;IAChFhB,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAiB,kBAAA,MAAAP,UAAA,CAAAM,IAAA,MACF;IACgBhB,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAAK,UAAA,CAAAQ,KAAA,CAAmB;IAGhClB,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAK,iBAAA,CAAAK,UAAA,CAAAS,WAAA,CAAyB;IACAnB,EAAA,CAAAI,SAAA,EAAiC;IAAjCJ,EAAA,CAAAS,UAAA,SAAAC,UAAA,CAAAC,QAAA,CAAAS,MAAA,KAAiC;;;;;IA8BvDpB,EAFJ,CAAAC,cAAA,cAA6C,cAClB,eACb;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,cAAO;IACfF,EADe,CAAAG,YAAA,EAAO,EAChB;IACDH,EAAL,CAAAC,cAAA,UAAK,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAC/BF,EAD+B,CAAAG,YAAA,EAAO,EAAM,EACtC;;;;IADOH,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAK,iBAAA,CAAAgB,OAAA,CAAAC,OAAA,CAAkB;;;;;IAK/BtB,EADF,CAAAC,cAAA,cAA2C,eAC/B;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IACxBF,EADwB,CAAAG,YAAA,EAAO,EACzB;;;;IADEH,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAgB,OAAA,CAAAE,KAAA,CAAgB;;;;;IAlBxBvB,EAFJ,CAAAC,cAAA,mBAAkF,sBAC/D,cAC0B;IAAAD,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC1DH,EAAA,CAAAC,cAAA,qBAAgB;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IACjDH,EAAA,CAAAC,cAAA,wBAAmB;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IACxCF,EADwC,CAAAG,YAAA,EAAoB,EAC1C;IAEhBH,EADF,CAAAC,cAAA,uBAAkB,QACb;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC7BH,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAO,UAAA,KAAAiB,oDAAA,kBAA6C;IAQ/CxB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAO,UAAA,KAAAkB,oDAAA,kBAA2C;IAK/CzB,EADE,CAAAG,YAAA,EAAmB,EACV;;;;;IArBkCH,EAAA,CAAAI,SAAA,GAAW;IAAXJ,EAAA,CAAAK,iBAAA,CAAAqB,IAAA,KAAW;IACpC1B,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAgB,OAAA,CAAAH,KAAA,CAAgB;IACblB,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAAgB,OAAA,CAAAM,QAAA,CAAmB;IAGnC3B,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAK,iBAAA,CAAAgB,OAAA,CAAAF,WAAA,CAAsB;IAEEnB,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAS,UAAA,SAAAY,OAAA,CAAAC,OAAA,CAAkB;IASpBtB,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAS,UAAA,SAAAY,OAAA,CAAAE,KAAA,CAAgB;;;;;IA6DrCvB,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAEhDH,EADF,CAAAC,cAAA,aAAuC,WAC/B;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IACzBF,EADyB,CAAAG,YAAA,EAAO,EAC3B;;;;IADGH,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAK,iBAAA,CAAAuB,UAAA,CAAAC,GAAA,CAAiB;;;;;IAIzB7B,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAE7CH,EADF,CAAAC,cAAA,aAAuC,eAC3B;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAC9BF,EAD8B,CAAAG,YAAA,EAAW,EACpC;;;;IADOH,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAK,iBAAA,CAAAyB,UAAA,CAAAC,IAAA,CAAkB;;;;;IAI9B/B,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAEhDH,EADF,CAAAC,cAAA,aAAuC,WAC/B;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAC7BF,EAD6B,CAAAG,YAAA,EAAO,EAC/B;;;;IADGH,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAK,iBAAA,CAAA2B,UAAA,CAAAC,OAAA,CAAqB;;;;;IAI7BjC,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IACtDH,EAAA,CAAAC,cAAA,aAAuC;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAA9BH,EAAA,CAAAI,SAAA,EAAyB;IAAzBJ,EAAA,CAAAK,iBAAA,CAAA6B,UAAA,CAAAf,WAAA,CAAyB;;;;;IAElEnB,EAAA,CAAAmC,SAAA,aAA2D;;;;;IAC3DnC,EAAA,CAAAmC,SAAA,aAAiE;;;;;IAgC7DnC,EAAA,CAAAC,cAAA,SAAqC;IAAAD,EAAA,CAAAE,MAAA,GAAU;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAAfH,EAAA,CAAAI,SAAA,EAAU;IAAVJ,EAAA,CAAAK,iBAAA,CAAA+B,QAAA,CAAU;;;;;IAFjDpC,EADF,CAAAC,cAAA,cAA6C,SACvC;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACfH,EAAA,CAAAC,cAAA,SAAI;IACFD,EAAA,CAAAO,UAAA,IAAA8B,qEAAA,iBAAqC;IAEzCrC,EADE,CAAAG,YAAA,EAAK,EACD;;;;IAFmBH,EAAA,CAAAI,SAAA,GAAc;IAAdJ,EAAA,CAAAS,UAAA,YAAA6B,SAAA,CAAAC,KAAA,CAAc;;;;;IAOjCvC,EADF,CAAAC,cAAA,SAAmC,eACvB;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAS;IACjBF,EADiB,CAAAG,YAAA,EAAO,EACnB;;;;IADGH,EAAA,CAAAI,SAAA,GAAS;IAATJ,EAAA,CAAAK,iBAAA,CAAAmC,OAAA,CAAS;;;;;IAJnBxC,EADF,CAAAC,cAAA,cAA2C,SACrC;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACdH,EAAA,CAAAC,cAAA,aAAsB;IACpBD,EAAA,CAAAO,UAAA,IAAAkC,qEAAA,iBAAmC;IAKvCzC,EADE,CAAAG,YAAA,EAAK,EACD;;;;IALkBH,EAAA,CAAAI,SAAA,GAAa;IAAbJ,EAAA,CAAAS,UAAA,YAAA6B,SAAA,CAAAI,IAAA,CAAa;;;;;IAnBnC1C,EAHN,CAAAC,cAAA,8BAAqF,iCACvD,sBACT,eACL;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACrCH,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAkB;IAClBH,EAAA,CAAAC,cAAA,4BAAuB;IACrBD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAwB,EACG;IAG3BH,EADF,CAAAC,cAAA,cAA2B,QACtB;IAAAD,EAAA,CAAAE,MAAA,IAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAO1BH,EANA,CAAAO,UAAA,KAAAoC,gEAAA,kBAA6C,KAAAC,gEAAA,kBAMF;IAU/C5C,EADE,CAAAG,YAAA,EAAM,EACc;;;;IA7BmCH,EAAA,CAAAS,UAAA,aAAA6B,SAAA,CAAAO,QAAA,CAA2B;IAGpE7C,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAiC,SAAA,CAAAtB,IAAA,CAAgB;IAC1BhB,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAiB,kBAAA,MAAAqB,SAAA,CAAApB,KAAA,MACF;IAEElB,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAiB,kBAAA,MAAAqB,SAAA,CAAAnB,WAAA,MACF;IAIGnB,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAAiC,SAAA,CAAAQ,OAAA,CAAmB;IACI9C,EAAA,CAAAI,SAAA,EAAiB;IAAjBJ,EAAA,CAAAS,UAAA,SAAA6B,SAAA,CAAAC,KAAA,CAAiB;IAMlBvC,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAS,UAAA,SAAA6B,SAAA,CAAAI,IAAA,CAAgB;;;;;IAmCvC1C,EAAA,CAAAC,cAAA,SAAwC;IAAAD,EAAA,CAAAE,MAAA,GAAU;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAAfH,EAAA,CAAAI,SAAA,EAAU;IAAVJ,EAAA,CAAAK,iBAAA,CAAA0C,QAAA,CAAU;;;;;IAIpD/C,EADF,CAAAC,cAAA,cAAsD,SAChD;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAC3BF,EAD2B,CAAAG,YAAA,EAAI,EACzB;;;;IADDH,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAK,iBAAA,CAAA2C,SAAA,CAAAC,UAAA,CAAsB;;;;;IAf3BjD,EAFJ,CAAAC,cAAA,mBAAmF,sBAChE,kBACwF;IACrGD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACXH,EAAA,CAAAC,cAAA,qBAAgB;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IACpDH,EAAA,CAAAC,cAAA,wBAAmB;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAC5CF,EAD4C,CAAAG,YAAA,EAAoB,EAC9C;IAGdH,EAFJ,CAAAC,cAAA,uBAAkB,cACY,UACtB;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClBH,EAAA,CAAAC,cAAA,UAAI;IACFD,EAAA,CAAAO,UAAA,KAAA2C,oDAAA,iBAAwC;IAE5ClD,EADE,CAAAG,YAAA,EAAK,EACD;IACNH,EAAA,CAAAO,UAAA,KAAA4C,qDAAA,kBAAsD;IAK1DnD,EADE,CAAAG,YAAA,EAAmB,EACV;;;;IAlBmBH,EAAA,CAAAI,SAAA,GAA4E;IAA5EJ,EAAA,CAAAa,WAAA,qBAAAmC,SAAA,CAAAI,QAAA,oCAA4E;IACpGpD,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAiB,kBAAA,MAAA+B,SAAA,CAAAhC,IAAA,MACF;IACgBhB,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAA2C,SAAA,CAAAK,OAAA,CAAmB;IAChBrD,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAK,iBAAA,CAAA2C,SAAA,CAAA7B,WAAA,CAAuB;IAMjBnB,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAS,UAAA,YAAAuC,SAAA,CAAAM,QAAA,CAAiB;IAGZtD,EAAA,CAAAI,SAAA,EAAsB;IAAtBJ,EAAA,CAAAS,UAAA,SAAAuC,SAAA,CAAAC,UAAA,CAAsB;;;AAiTtE,OAAM,MAAOM,wBAAwB;EA1kBrCC,YAAA;IA2kBE,KAAAC,eAAe,GAAa,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,aAAa,CAAC;IAErE,KAAAC,aAAa,GAAG;;;;;;;;;;;;EAYhB;IAEA,KAAAC,iBAAiB,GAAuB,CACtC;MACEzC,KAAK,EAAE,6BAA6B;MACpCC,WAAW,EAAE,8FAA8F;MAC3GH,IAAI,EAAE,UAAU;MAChBL,QAAQ,EAAE,CACR,mCAAmC,EACnC,uBAAuB,EACvB,2BAA2B,EAC3B,6BAA6B;KAEhC,EACD;MACEO,KAAK,EAAE,oBAAoB;MAC3BC,WAAW,EAAE,uFAAuF;MACpGH,IAAI,EAAE,YAAY;MAClBL,QAAQ,EAAE,CACR,uBAAuB,EACvB,4BAA4B,EAC5B,6BAA6B,EAC7B,0BAA0B;KAE7B,EACD;MACEO,KAAK,EAAE,4BAA4B;MACnCC,WAAW,EAAE,2FAA2F;MACxGH,IAAI,EAAE,YAAY;MAClBL,QAAQ,EAAE,CACR,4BAA4B,EAC5B,6BAA6B,EAC7B,4BAA4B,EAC5B,oCAAoC;KAEvC,EACD;MACEO,KAAK,EAAE,sBAAsB;MAC7BC,WAAW,EAAE,iFAAiF;MAC9FH,IAAI,EAAE,MAAM;MACZL,QAAQ,EAAE,CACR,6BAA6B,EAC7B,4BAA4B,EAC5B,uBAAuB,EACvB,6BAA6B;KAEhC,EACD;MACEO,KAAK,EAAE,mBAAmB;MAC1BC,WAAW,EAAE,gFAAgF;MAC7FH,IAAI,EAAE,MAAM;MACZL,QAAQ,EAAE,CACR,gCAAgC,EAChC,2BAA2B,EAC3B,4BAA4B,EAC5B,wBAAwB;KAE3B,EACD;MACEO,KAAK,EAAE,qBAAqB;MAC5BC,WAAW,EAAE,0EAA0E;MACvFH,IAAI,EAAE,MAAM;MACZL,QAAQ,EAAE,CACR,mCAAmC,EACnC,+BAA+B,EAC/B,2BAA2B,EAC3B,yBAAyB;KAE5B,CACF;IAED,KAAAiD,iBAAiB,GAAG,CAClB;MACE1C,KAAK,EAAE,kCAAkC;MACzCS,QAAQ,EAAE,oBAAoB;MAC9BR,WAAW,EAAE,8EAA8E;MAC3FG,OAAO,EAAE,uCAAuC;MAChDC,KAAK,EAAE;KACR,EACD;MACEL,KAAK,EAAE,8BAA8B;MACrCS,QAAQ,EAAE,uBAAuB;MACjCR,WAAW,EAAE,gEAAgE;MAC7EG,OAAO,EAAE,6CAA6C;MACtDC,KAAK,EAAE;KACR,EACD;MACEL,KAAK,EAAE,qBAAqB;MAC5BS,QAAQ,EAAE,qBAAqB;MAC/BR,WAAW,EAAE,yEAAyE;MACtFI,KAAK,EAAE;KACR,CACF;IAED,KAAAsC,cAAc,GAAoB,CAChC;MAAEhC,GAAG,EAAE,aAAa;MAAEE,IAAI,EAAE,SAAS;MAAEE,OAAO,EAAE,MAAM;MAAEd,WAAW,EAAE;IAAsC,CAAE,EAC7G;MAAEU,GAAG,EAAE,eAAe;MAAEE,IAAI,EAAE,QAAQ;MAAEE,OAAO,EAAE,uBAAuB;MAAEd,WAAW,EAAE;IAAwB,CAAE,EACjH;MAAEU,GAAG,EAAE,YAAY;MAAEE,IAAI,EAAE,QAAQ;MAAEE,OAAO,EAAE,IAAI;MAAEd,WAAW,EAAE;IAA4B,CAAE,EAC/F;MAAEU,GAAG,EAAE,cAAc;MAAEE,IAAI,EAAE,SAAS;MAAEE,OAAO,EAAE,MAAM;MAAEd,WAAW,EAAE;IAAkC,CAAE,EAC1G;MAAEU,GAAG,EAAE,gBAAgB;MAAEE,IAAI,EAAE,SAAS;MAAEE,OAAO,EAAE,OAAO;MAAEd,WAAW,EAAE;IAAsC,CAAE,EACjH;MAAEU,GAAG,EAAE,YAAY;MAAEE,IAAI,EAAE,OAAO;MAAEE,OAAO,EAAE,oCAAoC;MAAEd,WAAW,EAAE;IAA8B,CAAE,EAChI;MAAEU,GAAG,EAAE,cAAc;MAAEE,IAAI,EAAE,QAAQ;MAAEE,OAAO,EAAE,UAAU;MAAEd,WAAW,EAAE;IAAgC,CAAE,EAC3G;MAAEU,GAAG,EAAE,2BAA2B;MAAEE,IAAI,EAAE,SAAS;MAAEE,OAAO,EAAE,MAAM;MAAEd,WAAW,EAAE;IAAkC,CAAE,EACvH;MAAEU,GAAG,EAAE,kBAAkB;MAAEE,IAAI,EAAE,SAAS;MAAEE,OAAO,EAAE,MAAM;MAAEd,WAAW,EAAE;IAAwC,CAAE,EACpH;MAAEU,GAAG,EAAE,oBAAoB;MAAEE,IAAI,EAAE,SAAS;MAAEE,OAAO,EAAE,MAAM;MAAEd,WAAW,EAAE;IAAkC,CAAE,EAChH;MAAEU,GAAG,EAAE,iBAAiB;MAAEE,IAAI,EAAE,SAAS;MAAEE,OAAO,EAAE,MAAM;MAAEd,WAAW,EAAE;IAAsC,CAAE,CAClH;IAED,KAAA2C,aAAa,GAAG,CACd;MACE5C,KAAK,EAAE,gBAAgB;MACvBC,WAAW,EAAE,uCAAuC;MACpDH,IAAI,EAAE,SAAS;MACf6B,QAAQ,EAAE,IAAI;MACdC,OAAO,EAAE,yGAAyG;MAClHP,KAAK,EAAE,CACL,sCAAsC,EACtC,2CAA2C,EAC3C,gDAAgD,EAChD,sDAAsD,CACvD;MACDG,IAAI,EAAE,CACJ,qDAAqD,EACrD,mDAAmD,EACnD,wCAAwC;KAE3C,EACD;MACExB,KAAK,EAAE,+BAA+B;MACtCC,WAAW,EAAE,gDAAgD;MAC7DH,IAAI,EAAE,MAAM;MACZ8B,OAAO,EAAE,mHAAmH;MAC5HP,KAAK,EAAE,CACL,kDAAkD,EAClD,wDAAwD,EACxD,sCAAsC,EACtC,kCAAkC,CACnC;MACDG,IAAI,EAAE,CACJ,8CAA8C,EAC9C,yCAAyC,EACzC,0CAA0C;KAE7C,EACD;MACExB,KAAK,EAAE,2BAA2B;MAClCC,WAAW,EAAE,8CAA8C;MAC3DH,IAAI,EAAE,MAAM;MACZ8B,OAAO,EAAE,mFAAmF;MAC5FP,KAAK,EAAE,CACL,gCAAgC,EAChC,6CAA6C,EAC7C,4CAA4C,EAC5C,qCAAqC,CACtC;MACDG,IAAI,EAAE,CACJ,2DAA2D,EAC3D,qDAAqD,EACrD,gDAAgD;KAEnD,CACF;IAED,KAAAqB,qBAAqB,GAAG,CACtB;MACEV,OAAO,EAAE,oCAAoC;MAC7ClC,WAAW,EAAE,oDAAoD;MACjEH,IAAI,EAAE,WAAW;MACjBoC,QAAQ,EAAE,MAAM;MAChBE,QAAQ,EAAE,CACR,yDAAyD,EACzD,oDAAoD,EACpD,gDAAgD,EAChD,2CAA2C,CAC5C;MACDL,UAAU,EAAE;KACb,EACD;MACEI,OAAO,EAAE,6BAA6B;MACtClC,WAAW,EAAE,0DAA0D;MACvEH,IAAI,EAAE,YAAY;MAClBoC,QAAQ,EAAE,QAAQ;MAClBE,QAAQ,EAAE,CACR,kEAAkE,EAClE,mDAAmD,EACnD,0CAA0C,EAC1C,wDAAwD,CACzD;MACDL,UAAU,EAAE;KACb,EACD;MACEI,OAAO,EAAE,oBAAoB;MAC7BlC,WAAW,EAAE,2CAA2C;MACxDH,IAAI,EAAE,OAAO;MACboC,QAAQ,EAAE,QAAQ;MAClBE,QAAQ,EAAE,CACR,2CAA2C,EAC3C,mCAAmC,EACnC,kDAAkD,EAClD,gDAAgD,CACjD;MACDL,UAAU,EAAE;KACb,CACF;;EAEDlC,eAAeA,CAACC,IAAY;IAC1B,MAAMgD,MAAM,GAA8B;MACxC,UAAU,EAAE,SAAS;MACrB,YAAY,EAAE,SAAS;MACvB,YAAY,EAAE,SAAS;MACvB,MAAM,EAAE,SAAS;MACjB,MAAM,EAAE,SAAS;MACjB,MAAM,EAAE;KACT;IACD,OAAOA,MAAM,CAAChD,IAAI,CAAC,IAAI,SAAS;EAClC;;;uCAtOWuC,wBAAwB;IAAA;EAAA;;;YAAxBA,wBAAwB;MAAAU,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAzjB3BvE,EAHN,CAAAC,cAAA,aAAwC,aACb,SACnB,eACQ;UAAAD,EAAA,CAAAE,MAAA,gBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC9BH,EAAA,CAAAE,MAAA,0BACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,WAAyB;UACvBD,EAAA,CAAAE,MAAA,iEACF;UACFF,EADE,CAAAG,YAAA,EAAI,EACA;UAKAH,EAHN,CAAAC,cAAA,aAAgC,kBACE,uBACb,mBACW;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzCH,EAAA,CAAAC,cAAA,sBAAgB;UAAAD,EAAA,CAAAE,MAAA,6BAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAiB;UACtDH,EAAA,CAAAC,cAAA,yBAAmB;UAAAD,EAAA,CAAAE,MAAA,+DAAuD;UAC5EF,EAD4E,CAAAG,YAAA,EAAoB,EAC9E;UAEhBH,EADF,CAAAC,cAAA,wBAAkB,SACb;UAAAD,EAAA,CAAAE,MAAA,yKAAiK;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAGpKH,EAFJ,CAAAC,cAAA,cAAyB,cACL,gBACN;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAE3BH,EADF,CAAAC,cAAA,WAAK,cACK;UAAAD,EAAA,CAAAE,MAAA,0BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACnCH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,iCAAyB;UAEhCF,EAFgC,CAAAG,YAAA,EAAI,EAC5B,EACF;UAEJH,EADF,CAAAC,cAAA,cAAkB,gBACN;UAAAD,EAAA,CAAAE,MAAA,iBAAS;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAE5BH,EADF,CAAAC,cAAA,WAAK,cACK;UAAAD,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAClCH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,2CAAmC;UAE1CF,EAF0C,CAAAG,YAAA,EAAI,EACtC,EACF;UAEJH,EADF,CAAAC,cAAA,cAAkB,gBACN;UAAAD,EAAA,CAAAE,MAAA,gCAAwB;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAE3CH,EADF,CAAAC,cAAA,WAAK,cACK;UAAAD,EAAA,CAAAE,MAAA,4BAAoB;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACrCH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,oCAA4B;UAIvCF,EAJuC,CAAAG,YAAA,EAAI,EAC/B,EACF,EACF,EACW;UAGfH,EAFJ,CAAAC,cAAA,wBAAkB,iBAC0B,gBAC9B;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7BH,EAAA,CAAAE,MAAA,2BACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAEPH,EADF,CAAAC,cAAA,iBAA2B,gBACf;UAAAD,EAAA,CAAAE,MAAA,YAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzBH,EAAA,CAAAE,MAAA,qBACF;UAGNF,EAHM,CAAAG,YAAA,EAAS,EACQ,EACV,EACP;UAMAH,EAJN,CAAAC,cAAA,yBAAgE,mBAEpC,eACC,UACnB;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,2GAAmG;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAE1GH,EAAA,CAAAC,cAAA,eAA2B;UACzBD,EAAA,CAAAO,UAAA,KAAAkE,6CAAA,wBAAyE;UAmB/EzE,EAFI,CAAAG,YAAA,EAAM,EACF,EACE;UAKNH,EAFJ,CAAAC,cAAA,mBAA8B,eACH,UACnB;UAAAD,EAAA,CAAAE,MAAA,0BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC3BH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,kEAA0D;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAEjEH,EAAA,CAAAC,cAAA,eAAgC;UAC9BD,EAAA,CAAAO,UAAA,KAAAmE,6CAAA,wBAAkF;UA0BxF1E,EAFI,CAAAG,YAAA,EAAM,EACF,EACE;UAKNH,EAFJ,CAAAC,cAAA,mBAA+B,eACJ,UACnB;UAAAD,EAAA,CAAAE,MAAA,+BAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAChCH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,qFAA6E;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAK9EH,EAHN,CAAAC,cAAA,eAA6B,oBACM,uBACd,mBACW;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7CH,EAAA,CAAAC,cAAA,sBAAgB;UAAAD,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAiB;UAClDH,EAAA,CAAAC,cAAA,yBAAmB;UAAAD,EAAA,CAAAE,MAAA,oCAA4B;UACjDF,EADiD,CAAAG,YAAA,EAAoB,EACnD;UAEhBH,EADF,CAAAC,cAAA,wBAAkB,SACb;UAAAD,EAAA,CAAAE,MAAA,8DAAsD;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAGzDH,EAFJ,CAAAC,cAAA,eAAwB,eACG,gBACb;UAAAD,EAAA,CAAAE,MAAA,gBAAQ;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7BH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,yBAAiB;UACzBF,EADyB,CAAAG,YAAA,EAAO,EAC1B;UACDH,EAAL,CAAAC,cAAA,WAAK,YAAM;UAAAD,EAAA,CAAAE,MAAA,iEAAkD;UAGnEF,EAHmE,CAAAG,YAAA,EAAO,EAAM,EACtE,EACW,EACV;UAIPH,EAFJ,CAAAC,cAAA,oBAAiC,uBACd,oBACW;UAAAD,EAAA,CAAAE,MAAA,aAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzCH,EAAA,CAAAC,cAAA,uBAAgB;UAAAD,EAAA,CAAAE,MAAA,8BAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAiB;UACtDH,EAAA,CAAAC,cAAA,0BAAmB;UAAAD,EAAA,CAAAE,MAAA,sBAAa;UAClCF,EADkC,CAAAG,YAAA,EAAoB,EACpC;UAIZH,EAHN,CAAAC,cAAA,yBAAkB,gBACQ,gBACG,iBACb;UAAAD,EAAA,CAAAE,MAAA,aAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzBH,EAAA,CAAAC,cAAA,aAAM;UAAAD,EAAA,CAAAE,MAAA,2BAAkB;UAC1BF,EAD0B,CAAAG,YAAA,EAAO,EAC3B;UACDH,EAAL,CAAAC,cAAA,YAAK,aAAM;UAAAD,EAAA,CAAAE,MAAA,KAAmB;UAGpCF,EAHoC,CAAAG,YAAA,EAAO,EAAM,EACvC,EACW,EACV;UAIPH,EAFJ,CAAAC,cAAA,qBAAiC,wBACd,oBACW;UAAAD,EAAA,CAAAE,MAAA,aAAI;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACzCH,EAAA,CAAAC,cAAA,uBAAgB;UAAAD,EAAA,CAAAE,MAAA,2BAAkB;UAAAF,EAAA,CAAAG,YAAA,EAAiB;UACnDH,EAAA,CAAAC,cAAA,0BAAmB;UAAAD,EAAA,CAAAE,MAAA,oCAA2B;UAChDF,EADgD,CAAAG,YAAA,EAAoB,EAClD;UAEhBH,EADF,CAAAC,cAAA,yBAAkB,kBACsD;UACpED,EAAA,CAAA2E,uBAAA,SAAiC;UAE/B3E,EADA,CAAAO,UAAA,MAAAqE,wCAAA,iBAAsC,MAAAC,wCAAA,iBACC;;UAIzC7E,EAAA,CAAA2E,uBAAA,SAAkC;UAEhC3E,EADA,CAAAO,UAAA,MAAAuE,wCAAA,iBAAsC,MAAAC,wCAAA,iBACC;;UAIzC/E,EAAA,CAAA2E,uBAAA,SAAqC;UAEnC3E,EADA,CAAAO,UAAA,MAAAyE,wCAAA,iBAAsC,MAAAC,wCAAA,iBACC;;UAIzCjF,EAAA,CAAA2E,uBAAA,SAAyC;UAEvC3E,EADA,CAAAO,UAAA,MAAA2E,wCAAA,iBAAsC,MAAAC,wCAAA,iBACC;;UAGzCnF,EADA,CAAAO,UAAA,MAAA6E,wCAAA,iBAAsD,MAAAC,wCAAA,iBACM;UAMxErF,EALU,CAAAG,YAAA,EAAQ,EACS,EACV,EACP,EACF,EACE;UAKNH,EAFJ,CAAAC,cAAA,oBAAuB,gBACI,WACnB;UAAAD,EAAA,CAAAE,MAAA,4BAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAC5BH,EAAA,CAAAC,cAAA,UAAG;UAAAD,EAAA,CAAAE,MAAA,gGAAuF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAG5FH,EADF,CAAAC,cAAA,gBAA4B,0BACU;UAClCD,EAAA,CAAAO,UAAA,MAAA+E,yDAAA,mCAAqF;UAiC7FtF,EAHM,CAAAG,YAAA,EAAgB,EACZ,EACF,EACE;UAKNH,EAFJ,CAAAC,cAAA,oBAAiC,gBACN,WACnB;UAAAD,EAAA,CAAAE,MAAA,wBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxBH,EAAA,CAAAC,cAAA,UAAG;UAAAD,EAAA,CAAAE,MAAA,mEAA0D;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAEjEH,EAAA,CAAAC,cAAA,gBAAsC;UACpCD,EAAA,CAAAO,UAAA,MAAAgF,8CAAA,wBAAmF;UAyB7FvF,EAJQ,CAAAG,YAAA,EAAM,EACF,EACE,EACI,EACZ;;;UApNuDH,EAAA,CAAAI,SAAA,IAAoB;UAApBJ,EAAA,CAAAS,UAAA,YAAA+D,GAAA,CAAAb,iBAAA,CAAoB;UA4B1B3D,EAAA,CAAAI,SAAA,GAAsB;UAAtBJ,EAAA,CAAAS,UAAA,YAAA+D,GAAA,CAAAZ,iBAAA,CAAsB;UAiElD5D,EAAA,CAAAI,SAAA,IAAmB;UAAnBJ,EAAA,CAAAK,iBAAA,CAAAmE,GAAA,CAAAd,aAAA,CAAmB;UAYf1D,EAAA,CAAAI,SAAA,IAA6B;UAA7BJ,EAAA,CAAAS,UAAA,eAAA+D,GAAA,CAAAX,cAAA,CAA6B;UAuBxB7D,EAAA,CAAAI,SAAA,IAAgC;UAAhCJ,EAAA,CAAAS,UAAA,oBAAA+D,GAAA,CAAAf,eAAA,CAAgC;UACnBzD,EAAA,CAAAI,SAAA,EAAyB;UAAzBJ,EAAA,CAAAS,UAAA,qBAAA+D,GAAA,CAAAf,eAAA,CAAyB;UAgBvBzD,EAAA,CAAAI,SAAA,GAAgB;UAAhBJ,EAAA,CAAAS,UAAA,YAAA+D,GAAA,CAAAV,aAAA,CAAgB;UA0CA9D,EAAA,CAAAI,SAAA,GAAwB;UAAxBJ,EAAA,CAAAS,UAAA,YAAA+D,GAAA,CAAAT,qBAAA,CAAwB;;;qBAtQ3FvE,YAAY,EAAAgG,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZjG,aAAa,EAAAkG,EAAA,CAAAC,MAAA,EAAAD,EAAA,CAAAE,WAAA,EACbnG,aAAa,EAAAoG,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,aAAA,EAAAH,EAAA,CAAAI,cAAA,EAAAJ,EAAA,CAAAK,aAAA,EAAAL,EAAA,CAAAM,eAAA,EAAAN,EAAA,CAAAO,YAAA,EACb1G,aAAa,EAAA2G,EAAA,CAAAC,OAAA,EACb3G,eAAe,EAAA4G,EAAA,CAAAC,SAAA,EACf5G,cAAc,EAAA6G,EAAA,CAAAC,OAAA,EACd7G,kBAAkB,EAAA8G,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,iBAAA,EAAAF,EAAA,CAAAG,uBAAA,EAAAH,EAAA,CAAAI,sBAAA,EAAAJ,EAAA,CAAAK,4BAAA,EAClBlH,cAAc,EAAAmH,EAAA,CAAAC,QAAA,EAAAD,EAAA,CAAAE,gBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,YAAA,EAAAJ,EAAA,CAAAK,UAAA,EAAAL,EAAA,CAAAM,SAAA,EAAAN,EAAA,CAAAO,aAAA,EAAAP,EAAA,CAAAQ,OAAA,EAAAR,EAAA,CAAAS,YAAA,EAAAT,EAAA,CAAAU,MAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}