{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatTableModule } from '@angular/material/table';\nlet VscodeExtensionComponent = class VscodeExtensionComponent {\n  constructor() {\n    this.settingsColumns = ['key', 'type', 'default', 'description'];\n    this.configExample = `{\n  \"spt.enabled\": true,\n  \"spt.serverUrl\": \"http://localhost:8080\",\n  \"spt.apiKey\": \"your-api-key-here\",\n  \"spt.autoScan\": true,\n  \"spt.scanOnOpen\": false,\n  \"spt.chains\": [\"ethereum\", \"bitcoin\", \"general\"],\n  \"spt.severity\": \"medium\",\n  \"spt.showInlineDecorations\": true,\n  \"spt.showProblems\": true,\n  \"spt.enableCodeLens\": true,\n  \"spt.enableHover\": true\n}`;\n    this.extensionFeatures = [{\n      title: 'Real-time Security Scanning',\n      description: 'Automatic security analysis as you type, with instant feedback on potential vulnerabilities.',\n      icon: 'security',\n      benefits: ['Immediate vulnerability detection', 'Reduced security debt', 'Faster development cycles', 'Proactive security measures']\n    }, {\n      title: 'Inline Decorations',\n      description: 'Visual indicators directly in your code highlighting security issues and suggestions.',\n      icon: 'visibility',\n      benefits: ['Clear visual feedback', 'Context-aware highlighting', 'Severity-based color coding', 'Non-intrusive indicators']\n    }, {\n      title: 'Problems Panel Integration',\n      description: 'Security issues appear in VS Code\\'s Problems panel with detailed descriptions and fixes.',\n      icon: 'bug_report',\n      benefits: ['Centralized issue tracking', 'Detailed error descriptions', 'Quick navigation to issues', 'Integration with existing workflow']\n    }, {\n      title: 'CodeLens Integration',\n      description: 'Actionable security suggestions and metrics displayed directly above your code.',\n      icon: 'lens',\n      benefits: ['Contextual security metrics', 'One-click security actions', 'Code quality insights', 'Performance recommendations']\n    }, {\n      title: 'Hover Information',\n      description: 'Detailed security information and recommendations on hover over code elements.',\n      icon: 'info',\n      benefits: ['Instant security documentation', 'Best practice suggestions', 'Vulnerability explanations', 'Quick reference access']\n    }, {\n      title: 'Multi-chain Support',\n      description: 'Support for Ethereum, Bitcoin, and general blockchain security patterns.',\n      icon: 'link',\n      benefits: ['Comprehensive blockchain coverage', 'Chain-specific security rules', 'Unified security approach', 'Extensible architecture']\n    }];\n    this.installationSteps = [{\n      title: 'Install from VS Code Marketplace',\n      subtitle: 'Recommended method',\n      description: 'Search for \"SPT Security\" in the VS Code Extensions marketplace and install.',\n      command: 'ext install blockchain-spt.vscode-spt',\n      notes: 'Extension will be automatically activated after installation'\n    }, {\n      title: 'Configure Backend Connection',\n      subtitle: 'Connect to SPT server',\n      description: 'Configure the extension to connect to your SPT backend server.',\n      command: 'Configure spt.serverUrl in VS Code settings',\n      notes: 'Default server URL is http://localhost:8080'\n    }, {\n      title: 'Verify Installation',\n      subtitle: 'Test the connection',\n      description: 'Open a blockchain project and verify that security scanning is working.',\n      notes: 'Check the status bar for SPT connection indicator'\n    }];\n    this.configSettings = [{\n      key: 'spt.enabled',\n      type: 'boolean',\n      default: 'true',\n      description: 'Enable/disable SPT security analysis'\n    }, {\n      key: 'spt.serverUrl',\n      type: 'string',\n      default: 'http://localhost:8080',\n      description: 'SPT backend server URL'\n    }, {\n      key: 'spt.apiKey',\n      type: 'string',\n      default: '\"\"',\n      description: 'API key for authentication'\n    }, {\n      key: 'spt.autoScan',\n      type: 'boolean',\n      default: 'true',\n      description: 'Automatically scan files on save'\n    }, {\n      key: 'spt.scanOnOpen',\n      type: 'boolean',\n      default: 'false',\n      description: 'Automatically scan files when opened'\n    }, {\n      key: 'spt.chains',\n      type: 'array',\n      default: '[\"ethereum\", \"bitcoin\", \"general\"]',\n      description: 'Blockchain chains to analyze'\n    }, {\n      key: 'spt.severity',\n      type: 'string',\n      default: '\"medium\"',\n      description: 'Minimum severity level to show'\n    }, {\n      key: 'spt.showInlineDecorations',\n      type: 'boolean',\n      default: 'true',\n      description: 'Show inline security decorations'\n    }, {\n      key: 'spt.showProblems',\n      type: 'boolean',\n      default: 'true',\n      description: 'Show security issues in Problems panel'\n    }, {\n      key: 'spt.enableCodeLens',\n      type: 'boolean',\n      default: 'true',\n      description: 'Enable security-related CodeLens'\n    }, {\n      key: 'spt.enableHover',\n      type: 'boolean',\n      default: 'true',\n      description: 'Enable security information on hover'\n    }];\n    this.usageExamples = [{\n      title: 'Scanning Files',\n      description: 'How to scan files for security issues',\n      icon: 'scanner',\n      expanded: true,\n      details: 'Files are automatically scanned when saved (if auto-scan is enabled) or you can manually trigger scans.',\n      steps: ['Open a blockchain project in VS Code', 'Save a file to trigger automatic scanning', 'Or use Ctrl+Shift+P → \"SPT: Scan Current File\"', 'View results in Problems panel or inline decorations'],\n      tips: ['Enable auto-scan for continuous security monitoring', 'Use the Problems panel to navigate between issues', 'Check the status bar for scan progress']\n    }, {\n      title: 'Understanding Security Issues',\n      description: 'How to interpret and resolve security findings',\n      icon: 'help',\n      details: 'Security issues are displayed with severity levels and detailed descriptions to help you understand and fix them.',\n      steps: ['Hover over highlighted code to see issue details', 'Click on issues in Problems panel for more information', 'Use CodeLens actions for quick fixes', 'Follow the recommended solutions'],\n      tips: ['Start with critical and high severity issues', 'Use hover information for quick context', 'Check references for additional learning']\n    }, {\n      title: 'Configuring Scan Settings',\n      description: 'Customize scanning behavior for your project',\n      icon: 'tune',\n      details: 'Adjust scan settings to match your project requirements and development workflow.',\n      steps: ['Open VS Code settings (Ctrl+,)', 'Search for \"SPT\" to find extension settings', 'Adjust chains, severity, and scan triggers', 'Save settings and restart if needed'],\n      tips: ['Use workspace settings for project-specific configuration', 'Adjust severity threshold based on project maturity', 'Enable scan-on-open for comprehensive coverage']\n    }];\n    this.troubleshootingIssues = [{\n      problem: 'Extension Not Connecting to Server',\n      description: 'SPT extension cannot connect to the backend server',\n      icon: 'cloud_off',\n      severity: 'high',\n      solution: ['Verify SPT backend server is running on configured port', 'Check spt.serverUrl setting in VS Code preferences', 'Ensure firewall is not blocking the connection', 'Try restarting VS Code and the SPT server'],\n      prevention: 'Always start the SPT backend server before using the extension'\n    }, {\n      problem: 'No Security Issues Detected',\n      description: 'Extension is running but not finding any security issues',\n      icon: 'search_off',\n      severity: 'medium',\n      solution: ['Check if the file type is supported (Solidity, JavaScript, etc.)', 'Verify the correct blockchain chains are selected', 'Lower the severity threshold in settings', 'Ensure the file contains actual security-relevant code'],\n      prevention: 'Review supported file types and ensure proper project structure'\n    }, {\n      problem: 'Performance Issues',\n      description: 'Extension is causing VS Code to slow down',\n      icon: 'speed',\n      severity: 'medium',\n      solution: ['Disable auto-scan and use manual scanning', 'Increase scan timeout in settings', 'Exclude large files or directories from scanning', 'Reduce the number of enabled blockchain chains'],\n      prevention: 'Configure appropriate scan settings for your project size'\n    }];\n  }\n  getFeatureColor(icon) {\n    const colors = {\n      'security': '#1976d2',\n      'visibility': '#4caf50',\n      'bug_report': '#f44336',\n      'lens': '#ff9800',\n      'info': '#9c27b0',\n      'link': '#00bcd4'\n    };\n    return colors[icon] || '#1976d2';\n  }\n};\nVscodeExtensionComponent = __decorate([Component({\n  selector: 'app-vscode-extension',\n  standalone: true,\n  imports: [CommonModule, MatTabsModule, MatCardModule, MatIconModule, MatButtonModule, MatChipsModule, MatExpansionModule, MatTableModule],\n  template: `\n    <div class=\"spt-vscode-container\">\n      <!-- Hero Section -->\n      <header class=\"spt-hero-section\">\n        <div class=\"spt-hero-content\">\n          <div class=\"spt-hero-icon\">\n            <mat-icon>extension</mat-icon>\n          </div>\n          <div class=\"spt-hero-text\">\n            <h1 class=\"spt-hero-title\">VS Code Extension</h1>\n            <p class=\"spt-hero-subtitle\">\n              Real-time security analysis directly in your code editor with intelligent suggestions and seamless workflow integration.\n            </p>\n          </div>\n        </div>\n        <div class=\"spt-hero-actions\">\n          <button mat-raised-button color=\"primary\" class=\"spt-primary-btn\">\n            <mat-icon>download</mat-icon>\n            <span>Install Extension</span>\n          </button>\n          <button mat-stroked-button class=\"spt-secondary-btn\">\n            <mat-icon>code</mat-icon>\n            <span>View Source</span>\n          </button>\n        </div>\n      </header>\n\n      <!-- Key Features Overview -->\n      <section class=\"spt-features-overview\">\n        <div class=\"spt-section-header\">\n          <h2 class=\"spt-section-title\">Key Features</h2>\n          <p class=\"spt-section-subtitle\">\n            Discover the powerful features that make SPT extension essential for secure blockchain development.\n          </p>\n        </div>\n\n        <div class=\"spt-features-grid\">\n          <div class=\"spt-feature-card spt-feature-primary\" *ngFor=\"let feature of primaryFeatures\">\n            <div class=\"spt-feature-icon\" [style.background]=\"feature.color\">\n              <mat-icon>{{ feature.icon }}</mat-icon>\n            </div>\n            <div class=\"spt-feature-content\">\n              <h3 class=\"spt-feature-title\">{{ feature.title }}</h3>\n              <p class=\"spt-feature-description\">{{ feature.description }}</p>\n              <div class=\"spt-feature-benefits\" *ngIf=\"feature.benefits.length > 0\">\n                <ul class=\"spt-benefits-list\">\n                  <li *ngFor=\"let benefit of feature.benefits\" class=\"spt-benefit-item\">\n                    <mat-icon class=\"spt-benefit-icon\">check_circle</mat-icon>\n                    <span>{{ benefit }}</span>\n                  </li>\n                </ul>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- Detailed Information Tabs -->\n      <section class=\"spt-details-section\">\n        <mat-tab-group class=\"spt-tab-group\" animationDuration=\"300ms\">\n          <!-- Installation Tab -->\n          <mat-tab label=\"Installation\">\n            <div class=\"spt-tab-content\">\n              <div class=\"spt-tab-header\">\n                <h2 class=\"spt-tab-title\">Installation Guide</h2>\n                <p class=\"spt-tab-subtitle\">Get the SPT extension installed and configured in VS Code quickly and easily.</p>\n              </div>\n\n              <div class=\"spt-installation-steps\">\n                <div class=\"spt-step-card\" *ngFor=\"let step of installationSteps; let i = index\">\n                  <div class=\"spt-step-header\">\n                    <div class=\"spt-step-number\">{{ i + 1 }}</div>\n                    <div class=\"spt-step-info\">\n                      <h3 class=\"spt-step-title\">{{ step.title }}</h3>\n                      <p class=\"spt-step-subtitle\">{{ step.subtitle }}</p>\n                    </div>\n                  </div>\n                  <div class=\"spt-step-content\">\n                    <p class=\"spt-step-description\">{{ step.description }}</p>\n                    <div class=\"spt-code-block\" *ngIf=\"step.command\">\n                      <div class=\"spt-code-header\">\n                        <mat-icon>terminal</mat-icon>\n                        <span>Command</span>\n                        <button mat-icon-button class=\"spt-copy-btn\" (click)=\"copyToClipboard(step.command)\">\n                          <mat-icon>content_copy</mat-icon>\n                        </button>\n                      </div>\n                      <pre class=\"spt-code-content\"><code>{{ step.command }}</code></pre>\n                    </div>\n                    <div class=\"spt-step-note\" *ngIf=\"step.notes\">\n                      <mat-icon>info</mat-icon>\n                      <span>{{ step.notes }}</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </mat-tab>\n\n          <!-- Configuration Tab -->\n          <mat-tab label=\"Configuration\">\n            <div class=\"spt-tab-content\">\n              <div class=\"spt-tab-header\">\n                <h2 class=\"spt-tab-title\">Extension Configuration</h2>\n                <p class=\"spt-tab-subtitle\">Customize the SPT extension to fit your development workflow and preferences.</p>\n              </div>\n\n              <div class=\"spt-config-sections\">\n                <div class=\"spt-config-card\">\n                  <div class=\"spt-config-header\">\n                    <mat-icon class=\"spt-config-icon\">settings</mat-icon>\n                    <div class=\"spt-config-info\">\n                      <h3 class=\"spt-config-title\">Settings Overview</h3>\n                      <p class=\"spt-config-subtitle\">Configure extension behavior</p>\n                    </div>\n                  </div>\n                  <div class=\"spt-config-content\">\n                    <p>Access extension settings through VS Code preferences:</p>\n                    <div class=\"spt-code-block\">\n                      <div class=\"spt-code-header\">\n                        <mat-icon>keyboard</mat-icon>\n                        <span>Keyboard Shortcut</span>\n                      </div>\n                      <pre class=\"spt-code-content\"><code>Ctrl+Shift+P → \"Preferences: Open Settings (JSON)\"</code></pre>\n                    </div>\n                  </div>\n                </div>\n\n                <div class=\"spt-config-card\">\n                  <div class=\"spt-config-header\">\n                    <mat-icon class=\"spt-config-icon\">code</mat-icon>\n                    <div class=\"spt-config-info\">\n                      <h3 class=\"spt-config-title\">Configuration Example</h3>\n                      <p class=\"spt-config-subtitle\">settings.json</p>\n                    </div>\n                  </div>\n                  <div class=\"spt-config-content\">\n                    <div class=\"spt-code-block\">\n                      <div class=\"spt-code-header\">\n                        <mat-icon>code</mat-icon>\n                        <span>JSON Configuration</span>\n                        <button mat-icon-button class=\"spt-copy-btn\" (click)=\"copyToClipboard(configExample)\">\n                          <mat-icon>content_copy</mat-icon>\n                        </button>\n                      </div>\n                      <pre class=\"spt-code-content\"><code>{{ configExample }}</code></pre>\n                    </div>\n                  </div>\n                </div>\n\n                <div class=\"spt-config-card\">\n                  <div class=\"spt-config-header\">\n                    <mat-icon class=\"spt-config-icon\">list</mat-icon>\n                    <div class=\"spt-config-info\">\n                      <h3 class=\"spt-config-title\">Available Settings</h3>\n                      <p class=\"spt-config-subtitle\">Complete settings reference</p>\n                    </div>\n                  </div>\n                  <div class=\"spt-config-content\">\n                    <div class=\"spt-settings-table\">\n                      <div class=\"spt-table-header\">\n                        <div class=\"spt-table-cell\">Setting</div>\n                        <div class=\"spt-table-cell\">Type</div>\n                        <div class=\"spt-table-cell\">Default</div>\n                        <div class=\"spt-table-cell\">Description</div>\n                      </div>\n                      <div class=\"spt-table-row\" *ngFor=\"let setting of configSettings\">\n                        <div class=\"spt-table-cell\">\n                          <code class=\"spt-setting-key\">{{ setting.key }}</code>\n                        </div>\n                        <div class=\"spt-table-cell\">\n                          <span class=\"spt-badge spt-badge-info\">{{ setting.type }}</span>\n                        </div>\n                        <div class=\"spt-table-cell\">\n                          <code class=\"spt-setting-value\">{{ setting.default }}</code>\n                        </div>\n                        <div class=\"spt-table-cell\">\n                          <span class=\"spt-setting-desc\">{{ setting.description }}</span>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </mat-tab>\n\n        <!-- Configuration Tab -->\n        <mat-tab label=\"Configuration\">\n          <div class=\"tab-content\">\n            <h2>Extension Configuration</h2>\n            <p>Customize the SPT extension to fit your development workflow and preferences.</p>\n            \n            <div class=\"config-sections\">\n              <mat-card class=\"config-section\">\n                <mat-card-header>\n                  <mat-icon mat-card-avatar>settings</mat-icon>\n                  <mat-card-title>Settings Overview</mat-card-title>\n                  <mat-card-subtitle>Configure extension behavior</mat-card-subtitle>\n                </mat-card-header>\n                <mat-card-content>\n                  <p>Access extension settings through VS Code preferences:</p>\n                  <div class=\"code-block\">\n                    <div class=\"code-header\">\n                      <mat-icon>keyboard</mat-icon>\n                      <span>Keyboard Shortcut</span>\n                    </div>\n                    <pre><code>Ctrl+Shift+P → \"Preferences: Open Settings (JSON)\"</code></pre>\n                  </div>\n                </mat-card-content>\n              </mat-card>\n\n              <mat-card class=\"config-section\">\n                <mat-card-header>\n                  <mat-icon mat-card-avatar>code</mat-icon>\n                  <mat-card-title>Configuration Example</mat-card-title>\n                  <mat-card-subtitle>settings.json</mat-card-subtitle>\n                </mat-card-header>\n                <mat-card-content>\n                  <div class=\"code-block\">\n                    <div class=\"code-header\">\n                      <mat-icon>code</mat-icon>\n                      <span>JSON Configuration</span>\n                    </div>\n                    <pre><code>{{ configExample }}</code></pre>\n                  </div>\n                </mat-card-content>\n              </mat-card>\n\n              <mat-card class=\"config-section\">\n                <mat-card-header>\n                  <mat-icon mat-card-avatar>list</mat-icon>\n                  <mat-card-title>Available Settings</mat-card-title>\n                  <mat-card-subtitle>Complete settings reference</mat-card-subtitle>\n                </mat-card-header>\n                <mat-card-content>\n                  <table mat-table [dataSource]=\"configSettings\" class=\"settings-table\">\n                    <ng-container matColumnDef=\"key\">\n                      <th mat-header-cell *matHeaderCellDef>Setting</th>\n                      <td mat-cell *matCellDef=\"let setting\">\n                        <code>{{ setting.key }}</code>\n                      </td>\n                    </ng-container>\n                    <ng-container matColumnDef=\"type\">\n                      <th mat-header-cell *matHeaderCellDef>Type</th>\n                      <td mat-cell *matCellDef=\"let setting\">\n                        <mat-chip>{{ setting.type }}</mat-chip>\n                      </td>\n                    </ng-container>\n                    <ng-container matColumnDef=\"default\">\n                      <th mat-header-cell *matHeaderCellDef>Default</th>\n                      <td mat-cell *matCellDef=\"let setting\">\n                        <code>{{ setting.default }}</code>\n                      </td>\n                    </ng-container>\n                    <ng-container matColumnDef=\"description\">\n                      <th mat-header-cell *matHeaderCellDef>Description</th>\n                      <td mat-cell *matCellDef=\"let setting\">{{ setting.description }}</td>\n                    </ng-container>\n                    <tr mat-header-row *matHeaderRowDef=\"settingsColumns\"></tr>\n                    <tr mat-row *matRowDef=\"let row; columns: settingsColumns;\"></tr>\n                  </table>\n                </mat-card-content>\n              </mat-card>\n            </div>\n          </div>\n        </mat-tab>\n\n        <!-- Usage Tab -->\n        <mat-tab label=\"Usage\">\n          <div class=\"tab-content\">\n            <h2>Using the Extension</h2>\n            <p>Learn how to effectively use SPT extension features in your daily development workflow.</p>\n            \n            <div class=\"usage-sections\">\n              <mat-accordion class=\"usage-panels\">\n                <mat-expansion-panel *ngFor=\"let usage of usageExamples\" [expanded]=\"usage.expanded\">\n                  <mat-expansion-panel-header>\n                    <mat-panel-title>\n                      <mat-icon>{{ usage.icon }}</mat-icon>\n                      {{ usage.title }}\n                    </mat-panel-title>\n                    <mat-panel-description>\n                      {{ usage.description }}\n                    </mat-panel-description>\n                  </mat-expansion-panel-header>\n                  \n                  <div class=\"usage-content\">\n                    <p>{{ usage.details }}</p>\n                    <div class=\"usage-steps\" *ngIf=\"usage.steps\">\n                      <h4>Steps:</h4>\n                      <ol>\n                        <li *ngFor=\"let step of usage.steps\">{{ step }}</li>\n                      </ol>\n                    </div>\n                    <div class=\"usage-tips\" *ngIf=\"usage.tips\">\n                      <h4>Tips:</h4>\n                      <ul class=\"tips-list\">\n                        <li *ngFor=\"let tip of usage.tips\">\n                          <mat-icon>lightbulb</mat-icon>\n                          <span>{{ tip }}</span>\n                        </li>\n                      </ul>\n                    </div>\n                  </div>\n                </mat-expansion-panel>\n              </mat-accordion>\n            </div>\n          </div>\n        </mat-tab>\n\n        <!-- Troubleshooting Tab -->\n        <mat-tab label=\"Troubleshooting\">\n          <div class=\"tab-content\">\n            <h2>Troubleshooting</h2>\n            <p>Common issues and solutions for the SPT VS Code extension.</p>\n            \n            <div class=\"troubleshooting-sections\">\n              <mat-card class=\"troubleshooting-card\" *ngFor=\"let issue of troubleshootingIssues\">\n                <mat-card-header>\n                  <mat-icon mat-card-avatar [style.background-color]=\"issue.severity === 'high' ? '#f44336' : '#ff9800'\">\n                    {{ issue.icon }}\n                  </mat-icon>\n                  <mat-card-title>{{ issue.problem }}</mat-card-title>\n                  <mat-card-subtitle>{{ issue.description }}</mat-card-subtitle>\n                </mat-card-header>\n                <mat-card-content>\n                  <div class=\"solution-steps\">\n                    <h4>Solution:</h4>\n                    <ol>\n                      <li *ngFor=\"let step of issue.solution\">{{ step }}</li>\n                    </ol>\n                  </div>\n                  <div class=\"prevention-tips\" *ngIf=\"issue.prevention\">\n                    <h4>Prevention:</h4>\n                    <p>{{ issue.prevention }}</p>\n                  </div>\n                </mat-card-content>\n              </mat-card>\n            </div>\n          </div>\n        </mat-tab>\n      </mat-tab-group>\n    </div>\n  `,\n  styles: [`\n    .vscode-extension-container {\n      max-width: 1200px;\n      margin: 0 auto;\n    }\n\n    .page-header {\n      text-align: center;\n      margin-bottom: 32px;\n    }\n\n    .page-header h1 {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      gap: 12px;\n      color: #1976d2;\n      margin: 0 0 8px 0;\n    }\n\n    .page-subtitle {\n      color: #666;\n      font-size: 1.1em;\n      margin: 0;\n    }\n\n    .extension-overview {\n      margin-bottom: 32px;\n    }\n\n    .overview-card {\n      border: 1px solid #e0e0e0;\n    }\n\n    .quick-stats {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n      gap: 16px;\n      margin-top: 16px;\n    }\n\n    .stat {\n      padding: 12px;\n      background: #f5f5f5;\n      border-radius: 8px;\n    }\n\n    .extension-tabs {\n      margin-bottom: 32px;\n    }\n\n    .tab-content {\n      padding: 24px 0;\n    }\n\n    .tab-content h2 {\n      color: #1976d2;\n      margin-bottom: 8px;\n    }\n\n    .features-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\n      gap: 24px;\n      margin-top: 24px;\n    }\n\n    .benefits-list {\n      margin-top: 16px;\n    }\n\n    .installation-steps {\n      margin-top: 24px;\n    }\n\n    .step-card {\n      margin-bottom: 24px;\n    }\n\n    .step-number {\n      background: #1976d2;\n      color: white;\n      border-radius: 50%;\n      width: 40px;\n      height: 40px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-weight: bold;\n    }\n\n    .step-content {\n      margin-top: 16px;\n    }\n\n    .code-block {\n      border: 1px solid #e0e0e0;\n      border-radius: 8px;\n      overflow: hidden;\n      margin: 16px 0;\n    }\n\n    .code-header {\n      background: #f5f5f5;\n      padding: 8px 16px;\n      display: flex;\n      align-items: center;\n      gap: 8px;\n      font-weight: 500;\n      border-bottom: 1px solid #e0e0e0;\n    }\n\n    .code-block pre {\n      margin: 0;\n      padding: 16px;\n      background: #fafafa;\n      overflow-x: auto;\n    }\n\n    .code-block code {\n      font-family: 'Courier New', monospace;\n      font-size: 0.9em;\n    }\n\n\n\n    .step-notes {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n      margin-top: 12px;\n      padding: 8px 12px;\n      background: #e3f2fd;\n      border-radius: 4px;\n      color: #1976d2;\n    }\n\n    .config-sections {\n      margin-top: 24px;\n    }\n\n    .config-section {\n      margin-bottom: 24px;\n    }\n\n    .settings-table {\n      width: 100%;\n      border: 1px solid #e0e0e0;\n      border-radius: 8px;\n    }\n\n    .settings-table code {\n      background: #f5f5f5;\n      padding: 2px 6px;\n      border-radius: 4px;\n      font-size: 0.9em;\n    }\n\n    .usage-sections {\n      margin-top: 24px;\n    }\n\n    .usage-panels {\n      margin-top: 16px;\n    }\n\n    .usage-content {\n      padding: 16px 0;\n    }\n\n    .usage-steps,\n    .usage-tips {\n      margin-top: 16px;\n    }\n\n    .usage-steps h4,\n    .usage-tips h4 {\n      margin: 0 0 8px 0;\n      color: #1976d2;\n    }\n\n    .tips-list {\n      list-style: none;\n      padding: 0;\n      margin: 0;\n    }\n\n    .tips-list li {\n      display: flex;\n      align-items: flex-start;\n      gap: 8px;\n      margin-bottom: 8px;\n      padding: 8px;\n      background: #fff3e0;\n      border-radius: 4px;\n    }\n\n    .tips-list mat-icon {\n      color: #f57c00;\n      margin-top: 2px;\n    }\n\n    .troubleshooting-sections {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\n      gap: 24px;\n      margin-top: 24px;\n    }\n\n    .troubleshooting-card {\n      height: fit-content;\n    }\n\n    .solution-steps,\n    .prevention-tips {\n      margin-top: 16px;\n    }\n\n    .solution-steps h4,\n    .prevention-tips h4 {\n      margin: 0 0 8px 0;\n      color: #1976d2;\n    }\n\n    .solution-steps ol {\n      margin: 0;\n      padding-left: 20px;\n    }\n\n    .solution-steps li {\n      margin-bottom: 8px;\n      color: #666;\n    }\n\n    .prevention-tips p {\n      margin: 0;\n      color: #666;\n    }\n\n    @media (max-width: 768px) {\n      .quick-stats {\n        grid-template-columns: 1fr;\n      }\n      \n      .features-grid {\n        grid-template-columns: 1fr;\n      }\n      \n      .troubleshooting-sections {\n        grid-template-columns: 1fr;\n      }\n    }\n  `]\n})], VscodeExtensionComponent);\nexport { VscodeExtensionComponent };", "map": {"version": 3, "names": ["Component", "CommonModule", "MatTabsModule", "MatCardModule", "MatIconModule", "MatButtonModule", "MatChipsModule", "MatExpansionModule", "MatTableModule", "VscodeExtensionComponent", "constructor", "settingsColumns", "config<PERSON><PERSON><PERSON>", "extensionFeatures", "title", "description", "icon", "benefits", "installationSteps", "subtitle", "command", "notes", "configSettings", "key", "type", "default", "usageExamples", "expanded", "details", "steps", "tips", "troubleshootingIssues", "problem", "severity", "solution", "prevention", "getFeatureColor", "colors", "__decorate", "selector", "standalone", "imports", "template", "styles"], "sources": ["D:\\TGI\\Blockchain.SPT\\frontend\\src\\app\\modules\\documentation\\components\\vscode-extension\\vscode-extension.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatTableModule } from '@angular/material/table';\n\ninterface ExtensionFeature {\n  title: string;\n  description: string;\n  icon: string;\n  screenshot?: string;\n  benefits: string[];\n}\n\ninterface ConfigSetting {\n  key: string;\n  type: string;\n  default: string;\n  description: string;\n}\n\n@Component({\n  selector: 'app-vscode-extension',\n  standalone: true,\n  imports: [\n    CommonModule,\n    MatTabsModule,\n    MatCardModule,\n    MatIconModule,\n    MatButtonModule,\n    MatChipsModule,\n    MatExpansionModule,\n    MatTableModule\n  ],\n  template: `\n    <div class=\"spt-vscode-container\">\n      <!-- Hero Section -->\n      <header class=\"spt-hero-section\">\n        <div class=\"spt-hero-content\">\n          <div class=\"spt-hero-icon\">\n            <mat-icon>extension</mat-icon>\n          </div>\n          <div class=\"spt-hero-text\">\n            <h1 class=\"spt-hero-title\">VS Code Extension</h1>\n            <p class=\"spt-hero-subtitle\">\n              Real-time security analysis directly in your code editor with intelligent suggestions and seamless workflow integration.\n            </p>\n          </div>\n        </div>\n        <div class=\"spt-hero-actions\">\n          <button mat-raised-button color=\"primary\" class=\"spt-primary-btn\">\n            <mat-icon>download</mat-icon>\n            <span>Install Extension</span>\n          </button>\n          <button mat-stroked-button class=\"spt-secondary-btn\">\n            <mat-icon>code</mat-icon>\n            <span>View Source</span>\n          </button>\n        </div>\n      </header>\n\n      <!-- Key Features Overview -->\n      <section class=\"spt-features-overview\">\n        <div class=\"spt-section-header\">\n          <h2 class=\"spt-section-title\">Key Features</h2>\n          <p class=\"spt-section-subtitle\">\n            Discover the powerful features that make SPT extension essential for secure blockchain development.\n          </p>\n        </div>\n\n        <div class=\"spt-features-grid\">\n          <div class=\"spt-feature-card spt-feature-primary\" *ngFor=\"let feature of primaryFeatures\">\n            <div class=\"spt-feature-icon\" [style.background]=\"feature.color\">\n              <mat-icon>{{ feature.icon }}</mat-icon>\n            </div>\n            <div class=\"spt-feature-content\">\n              <h3 class=\"spt-feature-title\">{{ feature.title }}</h3>\n              <p class=\"spt-feature-description\">{{ feature.description }}</p>\n              <div class=\"spt-feature-benefits\" *ngIf=\"feature.benefits.length > 0\">\n                <ul class=\"spt-benefits-list\">\n                  <li *ngFor=\"let benefit of feature.benefits\" class=\"spt-benefit-item\">\n                    <mat-icon class=\"spt-benefit-icon\">check_circle</mat-icon>\n                    <span>{{ benefit }}</span>\n                  </li>\n                </ul>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <!-- Detailed Information Tabs -->\n      <section class=\"spt-details-section\">\n        <mat-tab-group class=\"spt-tab-group\" animationDuration=\"300ms\">\n          <!-- Installation Tab -->\n          <mat-tab label=\"Installation\">\n            <div class=\"spt-tab-content\">\n              <div class=\"spt-tab-header\">\n                <h2 class=\"spt-tab-title\">Installation Guide</h2>\n                <p class=\"spt-tab-subtitle\">Get the SPT extension installed and configured in VS Code quickly and easily.</p>\n              </div>\n\n              <div class=\"spt-installation-steps\">\n                <div class=\"spt-step-card\" *ngFor=\"let step of installationSteps; let i = index\">\n                  <div class=\"spt-step-header\">\n                    <div class=\"spt-step-number\">{{ i + 1 }}</div>\n                    <div class=\"spt-step-info\">\n                      <h3 class=\"spt-step-title\">{{ step.title }}</h3>\n                      <p class=\"spt-step-subtitle\">{{ step.subtitle }}</p>\n                    </div>\n                  </div>\n                  <div class=\"spt-step-content\">\n                    <p class=\"spt-step-description\">{{ step.description }}</p>\n                    <div class=\"spt-code-block\" *ngIf=\"step.command\">\n                      <div class=\"spt-code-header\">\n                        <mat-icon>terminal</mat-icon>\n                        <span>Command</span>\n                        <button mat-icon-button class=\"spt-copy-btn\" (click)=\"copyToClipboard(step.command)\">\n                          <mat-icon>content_copy</mat-icon>\n                        </button>\n                      </div>\n                      <pre class=\"spt-code-content\"><code>{{ step.command }}</code></pre>\n                    </div>\n                    <div class=\"spt-step-note\" *ngIf=\"step.notes\">\n                      <mat-icon>info</mat-icon>\n                      <span>{{ step.notes }}</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </mat-tab>\n\n          <!-- Configuration Tab -->\n          <mat-tab label=\"Configuration\">\n            <div class=\"spt-tab-content\">\n              <div class=\"spt-tab-header\">\n                <h2 class=\"spt-tab-title\">Extension Configuration</h2>\n                <p class=\"spt-tab-subtitle\">Customize the SPT extension to fit your development workflow and preferences.</p>\n              </div>\n\n              <div class=\"spt-config-sections\">\n                <div class=\"spt-config-card\">\n                  <div class=\"spt-config-header\">\n                    <mat-icon class=\"spt-config-icon\">settings</mat-icon>\n                    <div class=\"spt-config-info\">\n                      <h3 class=\"spt-config-title\">Settings Overview</h3>\n                      <p class=\"spt-config-subtitle\">Configure extension behavior</p>\n                    </div>\n                  </div>\n                  <div class=\"spt-config-content\">\n                    <p>Access extension settings through VS Code preferences:</p>\n                    <div class=\"spt-code-block\">\n                      <div class=\"spt-code-header\">\n                        <mat-icon>keyboard</mat-icon>\n                        <span>Keyboard Shortcut</span>\n                      </div>\n                      <pre class=\"spt-code-content\"><code>Ctrl+Shift+P → \"Preferences: Open Settings (JSON)\"</code></pre>\n                    </div>\n                  </div>\n                </div>\n\n                <div class=\"spt-config-card\">\n                  <div class=\"spt-config-header\">\n                    <mat-icon class=\"spt-config-icon\">code</mat-icon>\n                    <div class=\"spt-config-info\">\n                      <h3 class=\"spt-config-title\">Configuration Example</h3>\n                      <p class=\"spt-config-subtitle\">settings.json</p>\n                    </div>\n                  </div>\n                  <div class=\"spt-config-content\">\n                    <div class=\"spt-code-block\">\n                      <div class=\"spt-code-header\">\n                        <mat-icon>code</mat-icon>\n                        <span>JSON Configuration</span>\n                        <button mat-icon-button class=\"spt-copy-btn\" (click)=\"copyToClipboard(configExample)\">\n                          <mat-icon>content_copy</mat-icon>\n                        </button>\n                      </div>\n                      <pre class=\"spt-code-content\"><code>{{ configExample }}</code></pre>\n                    </div>\n                  </div>\n                </div>\n\n                <div class=\"spt-config-card\">\n                  <div class=\"spt-config-header\">\n                    <mat-icon class=\"spt-config-icon\">list</mat-icon>\n                    <div class=\"spt-config-info\">\n                      <h3 class=\"spt-config-title\">Available Settings</h3>\n                      <p class=\"spt-config-subtitle\">Complete settings reference</p>\n                    </div>\n                  </div>\n                  <div class=\"spt-config-content\">\n                    <div class=\"spt-settings-table\">\n                      <div class=\"spt-table-header\">\n                        <div class=\"spt-table-cell\">Setting</div>\n                        <div class=\"spt-table-cell\">Type</div>\n                        <div class=\"spt-table-cell\">Default</div>\n                        <div class=\"spt-table-cell\">Description</div>\n                      </div>\n                      <div class=\"spt-table-row\" *ngFor=\"let setting of configSettings\">\n                        <div class=\"spt-table-cell\">\n                          <code class=\"spt-setting-key\">{{ setting.key }}</code>\n                        </div>\n                        <div class=\"spt-table-cell\">\n                          <span class=\"spt-badge spt-badge-info\">{{ setting.type }}</span>\n                        </div>\n                        <div class=\"spt-table-cell\">\n                          <code class=\"spt-setting-value\">{{ setting.default }}</code>\n                        </div>\n                        <div class=\"spt-table-cell\">\n                          <span class=\"spt-setting-desc\">{{ setting.description }}</span>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </mat-tab>\n\n        <!-- Configuration Tab -->\n        <mat-tab label=\"Configuration\">\n          <div class=\"tab-content\">\n            <h2>Extension Configuration</h2>\n            <p>Customize the SPT extension to fit your development workflow and preferences.</p>\n            \n            <div class=\"config-sections\">\n              <mat-card class=\"config-section\">\n                <mat-card-header>\n                  <mat-icon mat-card-avatar>settings</mat-icon>\n                  <mat-card-title>Settings Overview</mat-card-title>\n                  <mat-card-subtitle>Configure extension behavior</mat-card-subtitle>\n                </mat-card-header>\n                <mat-card-content>\n                  <p>Access extension settings through VS Code preferences:</p>\n                  <div class=\"code-block\">\n                    <div class=\"code-header\">\n                      <mat-icon>keyboard</mat-icon>\n                      <span>Keyboard Shortcut</span>\n                    </div>\n                    <pre><code>Ctrl+Shift+P → \"Preferences: Open Settings (JSON)\"</code></pre>\n                  </div>\n                </mat-card-content>\n              </mat-card>\n\n              <mat-card class=\"config-section\">\n                <mat-card-header>\n                  <mat-icon mat-card-avatar>code</mat-icon>\n                  <mat-card-title>Configuration Example</mat-card-title>\n                  <mat-card-subtitle>settings.json</mat-card-subtitle>\n                </mat-card-header>\n                <mat-card-content>\n                  <div class=\"code-block\">\n                    <div class=\"code-header\">\n                      <mat-icon>code</mat-icon>\n                      <span>JSON Configuration</span>\n                    </div>\n                    <pre><code>{{ configExample }}</code></pre>\n                  </div>\n                </mat-card-content>\n              </mat-card>\n\n              <mat-card class=\"config-section\">\n                <mat-card-header>\n                  <mat-icon mat-card-avatar>list</mat-icon>\n                  <mat-card-title>Available Settings</mat-card-title>\n                  <mat-card-subtitle>Complete settings reference</mat-card-subtitle>\n                </mat-card-header>\n                <mat-card-content>\n                  <table mat-table [dataSource]=\"configSettings\" class=\"settings-table\">\n                    <ng-container matColumnDef=\"key\">\n                      <th mat-header-cell *matHeaderCellDef>Setting</th>\n                      <td mat-cell *matCellDef=\"let setting\">\n                        <code>{{ setting.key }}</code>\n                      </td>\n                    </ng-container>\n                    <ng-container matColumnDef=\"type\">\n                      <th mat-header-cell *matHeaderCellDef>Type</th>\n                      <td mat-cell *matCellDef=\"let setting\">\n                        <mat-chip>{{ setting.type }}</mat-chip>\n                      </td>\n                    </ng-container>\n                    <ng-container matColumnDef=\"default\">\n                      <th mat-header-cell *matHeaderCellDef>Default</th>\n                      <td mat-cell *matCellDef=\"let setting\">\n                        <code>{{ setting.default }}</code>\n                      </td>\n                    </ng-container>\n                    <ng-container matColumnDef=\"description\">\n                      <th mat-header-cell *matHeaderCellDef>Description</th>\n                      <td mat-cell *matCellDef=\"let setting\">{{ setting.description }}</td>\n                    </ng-container>\n                    <tr mat-header-row *matHeaderRowDef=\"settingsColumns\"></tr>\n                    <tr mat-row *matRowDef=\"let row; columns: settingsColumns;\"></tr>\n                  </table>\n                </mat-card-content>\n              </mat-card>\n            </div>\n          </div>\n        </mat-tab>\n\n        <!-- Usage Tab -->\n        <mat-tab label=\"Usage\">\n          <div class=\"tab-content\">\n            <h2>Using the Extension</h2>\n            <p>Learn how to effectively use SPT extension features in your daily development workflow.</p>\n            \n            <div class=\"usage-sections\">\n              <mat-accordion class=\"usage-panels\">\n                <mat-expansion-panel *ngFor=\"let usage of usageExamples\" [expanded]=\"usage.expanded\">\n                  <mat-expansion-panel-header>\n                    <mat-panel-title>\n                      <mat-icon>{{ usage.icon }}</mat-icon>\n                      {{ usage.title }}\n                    </mat-panel-title>\n                    <mat-panel-description>\n                      {{ usage.description }}\n                    </mat-panel-description>\n                  </mat-expansion-panel-header>\n                  \n                  <div class=\"usage-content\">\n                    <p>{{ usage.details }}</p>\n                    <div class=\"usage-steps\" *ngIf=\"usage.steps\">\n                      <h4>Steps:</h4>\n                      <ol>\n                        <li *ngFor=\"let step of usage.steps\">{{ step }}</li>\n                      </ol>\n                    </div>\n                    <div class=\"usage-tips\" *ngIf=\"usage.tips\">\n                      <h4>Tips:</h4>\n                      <ul class=\"tips-list\">\n                        <li *ngFor=\"let tip of usage.tips\">\n                          <mat-icon>lightbulb</mat-icon>\n                          <span>{{ tip }}</span>\n                        </li>\n                      </ul>\n                    </div>\n                  </div>\n                </mat-expansion-panel>\n              </mat-accordion>\n            </div>\n          </div>\n        </mat-tab>\n\n        <!-- Troubleshooting Tab -->\n        <mat-tab label=\"Troubleshooting\">\n          <div class=\"tab-content\">\n            <h2>Troubleshooting</h2>\n            <p>Common issues and solutions for the SPT VS Code extension.</p>\n            \n            <div class=\"troubleshooting-sections\">\n              <mat-card class=\"troubleshooting-card\" *ngFor=\"let issue of troubleshootingIssues\">\n                <mat-card-header>\n                  <mat-icon mat-card-avatar [style.background-color]=\"issue.severity === 'high' ? '#f44336' : '#ff9800'\">\n                    {{ issue.icon }}\n                  </mat-icon>\n                  <mat-card-title>{{ issue.problem }}</mat-card-title>\n                  <mat-card-subtitle>{{ issue.description }}</mat-card-subtitle>\n                </mat-card-header>\n                <mat-card-content>\n                  <div class=\"solution-steps\">\n                    <h4>Solution:</h4>\n                    <ol>\n                      <li *ngFor=\"let step of issue.solution\">{{ step }}</li>\n                    </ol>\n                  </div>\n                  <div class=\"prevention-tips\" *ngIf=\"issue.prevention\">\n                    <h4>Prevention:</h4>\n                    <p>{{ issue.prevention }}</p>\n                  </div>\n                </mat-card-content>\n              </mat-card>\n            </div>\n          </div>\n        </mat-tab>\n      </mat-tab-group>\n    </div>\n  `,\n  styles: [`\n    .vscode-extension-container {\n      max-width: 1200px;\n      margin: 0 auto;\n    }\n\n    .page-header {\n      text-align: center;\n      margin-bottom: 32px;\n    }\n\n    .page-header h1 {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      gap: 12px;\n      color: #1976d2;\n      margin: 0 0 8px 0;\n    }\n\n    .page-subtitle {\n      color: #666;\n      font-size: 1.1em;\n      margin: 0;\n    }\n\n    .extension-overview {\n      margin-bottom: 32px;\n    }\n\n    .overview-card {\n      border: 1px solid #e0e0e0;\n    }\n\n    .quick-stats {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n      gap: 16px;\n      margin-top: 16px;\n    }\n\n    .stat {\n      padding: 12px;\n      background: #f5f5f5;\n      border-radius: 8px;\n    }\n\n    .extension-tabs {\n      margin-bottom: 32px;\n    }\n\n    .tab-content {\n      padding: 24px 0;\n    }\n\n    .tab-content h2 {\n      color: #1976d2;\n      margin-bottom: 8px;\n    }\n\n    .features-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\n      gap: 24px;\n      margin-top: 24px;\n    }\n\n    .benefits-list {\n      margin-top: 16px;\n    }\n\n    .installation-steps {\n      margin-top: 24px;\n    }\n\n    .step-card {\n      margin-bottom: 24px;\n    }\n\n    .step-number {\n      background: #1976d2;\n      color: white;\n      border-radius: 50%;\n      width: 40px;\n      height: 40px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-weight: bold;\n    }\n\n    .step-content {\n      margin-top: 16px;\n    }\n\n    .code-block {\n      border: 1px solid #e0e0e0;\n      border-radius: 8px;\n      overflow: hidden;\n      margin: 16px 0;\n    }\n\n    .code-header {\n      background: #f5f5f5;\n      padding: 8px 16px;\n      display: flex;\n      align-items: center;\n      gap: 8px;\n      font-weight: 500;\n      border-bottom: 1px solid #e0e0e0;\n    }\n\n    .code-block pre {\n      margin: 0;\n      padding: 16px;\n      background: #fafafa;\n      overflow-x: auto;\n    }\n\n    .code-block code {\n      font-family: 'Courier New', monospace;\n      font-size: 0.9em;\n    }\n\n\n\n    .step-notes {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n      margin-top: 12px;\n      padding: 8px 12px;\n      background: #e3f2fd;\n      border-radius: 4px;\n      color: #1976d2;\n    }\n\n    .config-sections {\n      margin-top: 24px;\n    }\n\n    .config-section {\n      margin-bottom: 24px;\n    }\n\n    .settings-table {\n      width: 100%;\n      border: 1px solid #e0e0e0;\n      border-radius: 8px;\n    }\n\n    .settings-table code {\n      background: #f5f5f5;\n      padding: 2px 6px;\n      border-radius: 4px;\n      font-size: 0.9em;\n    }\n\n    .usage-sections {\n      margin-top: 24px;\n    }\n\n    .usage-panels {\n      margin-top: 16px;\n    }\n\n    .usage-content {\n      padding: 16px 0;\n    }\n\n    .usage-steps,\n    .usage-tips {\n      margin-top: 16px;\n    }\n\n    .usage-steps h4,\n    .usage-tips h4 {\n      margin: 0 0 8px 0;\n      color: #1976d2;\n    }\n\n    .tips-list {\n      list-style: none;\n      padding: 0;\n      margin: 0;\n    }\n\n    .tips-list li {\n      display: flex;\n      align-items: flex-start;\n      gap: 8px;\n      margin-bottom: 8px;\n      padding: 8px;\n      background: #fff3e0;\n      border-radius: 4px;\n    }\n\n    .tips-list mat-icon {\n      color: #f57c00;\n      margin-top: 2px;\n    }\n\n    .troubleshooting-sections {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\n      gap: 24px;\n      margin-top: 24px;\n    }\n\n    .troubleshooting-card {\n      height: fit-content;\n    }\n\n    .solution-steps,\n    .prevention-tips {\n      margin-top: 16px;\n    }\n\n    .solution-steps h4,\n    .prevention-tips h4 {\n      margin: 0 0 8px 0;\n      color: #1976d2;\n    }\n\n    .solution-steps ol {\n      margin: 0;\n      padding-left: 20px;\n    }\n\n    .solution-steps li {\n      margin-bottom: 8px;\n      color: #666;\n    }\n\n    .prevention-tips p {\n      margin: 0;\n      color: #666;\n    }\n\n    @media (max-width: 768px) {\n      .quick-stats {\n        grid-template-columns: 1fr;\n      }\n      \n      .features-grid {\n        grid-template-columns: 1fr;\n      }\n      \n      .troubleshooting-sections {\n        grid-template-columns: 1fr;\n      }\n    }\n  `]\n})\nexport class VscodeExtensionComponent {\n  settingsColumns: string[] = ['key', 'type', 'default', 'description'];\n\n  configExample = `{\n  \"spt.enabled\": true,\n  \"spt.serverUrl\": \"http://localhost:8080\",\n  \"spt.apiKey\": \"your-api-key-here\",\n  \"spt.autoScan\": true,\n  \"spt.scanOnOpen\": false,\n  \"spt.chains\": [\"ethereum\", \"bitcoin\", \"general\"],\n  \"spt.severity\": \"medium\",\n  \"spt.showInlineDecorations\": true,\n  \"spt.showProblems\": true,\n  \"spt.enableCodeLens\": true,\n  \"spt.enableHover\": true\n}`;\n\n  extensionFeatures: ExtensionFeature[] = [\n    {\n      title: 'Real-time Security Scanning',\n      description: 'Automatic security analysis as you type, with instant feedback on potential vulnerabilities.',\n      icon: 'security',\n      benefits: [\n        'Immediate vulnerability detection',\n        'Reduced security debt',\n        'Faster development cycles',\n        'Proactive security measures'\n      ]\n    },\n    {\n      title: 'Inline Decorations',\n      description: 'Visual indicators directly in your code highlighting security issues and suggestions.',\n      icon: 'visibility',\n      benefits: [\n        'Clear visual feedback',\n        'Context-aware highlighting',\n        'Severity-based color coding',\n        'Non-intrusive indicators'\n      ]\n    },\n    {\n      title: 'Problems Panel Integration',\n      description: 'Security issues appear in VS Code\\'s Problems panel with detailed descriptions and fixes.',\n      icon: 'bug_report',\n      benefits: [\n        'Centralized issue tracking',\n        'Detailed error descriptions',\n        'Quick navigation to issues',\n        'Integration with existing workflow'\n      ]\n    },\n    {\n      title: 'CodeLens Integration',\n      description: 'Actionable security suggestions and metrics displayed directly above your code.',\n      icon: 'lens',\n      benefits: [\n        'Contextual security metrics',\n        'One-click security actions',\n        'Code quality insights',\n        'Performance recommendations'\n      ]\n    },\n    {\n      title: 'Hover Information',\n      description: 'Detailed security information and recommendations on hover over code elements.',\n      icon: 'info',\n      benefits: [\n        'Instant security documentation',\n        'Best practice suggestions',\n        'Vulnerability explanations',\n        'Quick reference access'\n      ]\n    },\n    {\n      title: 'Multi-chain Support',\n      description: 'Support for Ethereum, Bitcoin, and general blockchain security patterns.',\n      icon: 'link',\n      benefits: [\n        'Comprehensive blockchain coverage',\n        'Chain-specific security rules',\n        'Unified security approach',\n        'Extensible architecture'\n      ]\n    }\n  ];\n\n  installationSteps = [\n    {\n      title: 'Install from VS Code Marketplace',\n      subtitle: 'Recommended method',\n      description: 'Search for \"SPT Security\" in the VS Code Extensions marketplace and install.',\n      command: 'ext install blockchain-spt.vscode-spt',\n      notes: 'Extension will be automatically activated after installation'\n    },\n    {\n      title: 'Configure Backend Connection',\n      subtitle: 'Connect to SPT server',\n      description: 'Configure the extension to connect to your SPT backend server.',\n      command: 'Configure spt.serverUrl in VS Code settings',\n      notes: 'Default server URL is http://localhost:8080'\n    },\n    {\n      title: 'Verify Installation',\n      subtitle: 'Test the connection',\n      description: 'Open a blockchain project and verify that security scanning is working.',\n      notes: 'Check the status bar for SPT connection indicator'\n    }\n  ];\n\n  configSettings: ConfigSetting[] = [\n    { key: 'spt.enabled', type: 'boolean', default: 'true', description: 'Enable/disable SPT security analysis' },\n    { key: 'spt.serverUrl', type: 'string', default: 'http://localhost:8080', description: 'SPT backend server URL' },\n    { key: 'spt.apiKey', type: 'string', default: '\"\"', description: 'API key for authentication' },\n    { key: 'spt.autoScan', type: 'boolean', default: 'true', description: 'Automatically scan files on save' },\n    { key: 'spt.scanOnOpen', type: 'boolean', default: 'false', description: 'Automatically scan files when opened' },\n    { key: 'spt.chains', type: 'array', default: '[\"ethereum\", \"bitcoin\", \"general\"]', description: 'Blockchain chains to analyze' },\n    { key: 'spt.severity', type: 'string', default: '\"medium\"', description: 'Minimum severity level to show' },\n    { key: 'spt.showInlineDecorations', type: 'boolean', default: 'true', description: 'Show inline security decorations' },\n    { key: 'spt.showProblems', type: 'boolean', default: 'true', description: 'Show security issues in Problems panel' },\n    { key: 'spt.enableCodeLens', type: 'boolean', default: 'true', description: 'Enable security-related CodeLens' },\n    { key: 'spt.enableHover', type: 'boolean', default: 'true', description: 'Enable security information on hover' }\n  ];\n\n  usageExamples = [\n    {\n      title: 'Scanning Files',\n      description: 'How to scan files for security issues',\n      icon: 'scanner',\n      expanded: true,\n      details: 'Files are automatically scanned when saved (if auto-scan is enabled) or you can manually trigger scans.',\n      steps: [\n        'Open a blockchain project in VS Code',\n        'Save a file to trigger automatic scanning',\n        'Or use Ctrl+Shift+P → \"SPT: Scan Current File\"',\n        'View results in Problems panel or inline decorations'\n      ],\n      tips: [\n        'Enable auto-scan for continuous security monitoring',\n        'Use the Problems panel to navigate between issues',\n        'Check the status bar for scan progress'\n      ]\n    },\n    {\n      title: 'Understanding Security Issues',\n      description: 'How to interpret and resolve security findings',\n      icon: 'help',\n      details: 'Security issues are displayed with severity levels and detailed descriptions to help you understand and fix them.',\n      steps: [\n        'Hover over highlighted code to see issue details',\n        'Click on issues in Problems panel for more information',\n        'Use CodeLens actions for quick fixes',\n        'Follow the recommended solutions'\n      ],\n      tips: [\n        'Start with critical and high severity issues',\n        'Use hover information for quick context',\n        'Check references for additional learning'\n      ]\n    },\n    {\n      title: 'Configuring Scan Settings',\n      description: 'Customize scanning behavior for your project',\n      icon: 'tune',\n      details: 'Adjust scan settings to match your project requirements and development workflow.',\n      steps: [\n        'Open VS Code settings (Ctrl+,)',\n        'Search for \"SPT\" to find extension settings',\n        'Adjust chains, severity, and scan triggers',\n        'Save settings and restart if needed'\n      ],\n      tips: [\n        'Use workspace settings for project-specific configuration',\n        'Adjust severity threshold based on project maturity',\n        'Enable scan-on-open for comprehensive coverage'\n      ]\n    }\n  ];\n\n  troubleshootingIssues = [\n    {\n      problem: 'Extension Not Connecting to Server',\n      description: 'SPT extension cannot connect to the backend server',\n      icon: 'cloud_off',\n      severity: 'high',\n      solution: [\n        'Verify SPT backend server is running on configured port',\n        'Check spt.serverUrl setting in VS Code preferences',\n        'Ensure firewall is not blocking the connection',\n        'Try restarting VS Code and the SPT server'\n      ],\n      prevention: 'Always start the SPT backend server before using the extension'\n    },\n    {\n      problem: 'No Security Issues Detected',\n      description: 'Extension is running but not finding any security issues',\n      icon: 'search_off',\n      severity: 'medium',\n      solution: [\n        'Check if the file type is supported (Solidity, JavaScript, etc.)',\n        'Verify the correct blockchain chains are selected',\n        'Lower the severity threshold in settings',\n        'Ensure the file contains actual security-relevant code'\n      ],\n      prevention: 'Review supported file types and ensure proper project structure'\n    },\n    {\n      problem: 'Performance Issues',\n      description: 'Extension is causing VS Code to slow down',\n      icon: 'speed',\n      severity: 'medium',\n      solution: [\n        'Disable auto-scan and use manual scanning',\n        'Increase scan timeout in settings',\n        'Exclude large files or directories from scanning',\n        'Reduce the number of enabled blockchain chains'\n      ],\n      prevention: 'Configure appropriate scan settings for your project size'\n    }\n  ];\n\n  getFeatureColor(icon: string): string {\n    const colors: { [key: string]: string } = {\n      'security': '#1976d2',\n      'visibility': '#4caf50',\n      'bug_report': '#f44336',\n      'lens': '#ff9800',\n      'info': '#9c27b0',\n      'link': '#00bcd4'\n    };\n    return colors[icon] || '#1976d2';\n  }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,QAAQ,eAAe;AACzC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,cAAc,QAAQ,yBAAyB;AAqnBjD,IAAMC,wBAAwB,GAA9B,MAAMA,wBAAwB;EAA9BC,YAAA;IACL,KAAAC,eAAe,GAAa,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,aAAa,CAAC;IAErE,KAAAC,aAAa,GAAG;;;;;;;;;;;;EAYhB;IAEA,KAAAC,iBAAiB,GAAuB,CACtC;MACEC,KAAK,EAAE,6BAA6B;MACpCC,WAAW,EAAE,8FAA8F;MAC3GC,IAAI,EAAE,UAAU;MAChBC,QAAQ,EAAE,CACR,mCAAmC,EACnC,uBAAuB,EACvB,2BAA2B,EAC3B,6BAA6B;KAEhC,EACD;MACEH,KAAK,EAAE,oBAAoB;MAC3BC,WAAW,EAAE,uFAAuF;MACpGC,IAAI,EAAE,YAAY;MAClBC,QAAQ,EAAE,CACR,uBAAuB,EACvB,4BAA4B,EAC5B,6BAA6B,EAC7B,0BAA0B;KAE7B,EACD;MACEH,KAAK,EAAE,4BAA4B;MACnCC,WAAW,EAAE,2FAA2F;MACxGC,IAAI,EAAE,YAAY;MAClBC,QAAQ,EAAE,CACR,4BAA4B,EAC5B,6BAA6B,EAC7B,4BAA4B,EAC5B,oCAAoC;KAEvC,EACD;MACEH,KAAK,EAAE,sBAAsB;MAC7BC,WAAW,EAAE,iFAAiF;MAC9FC,IAAI,EAAE,MAAM;MACZC,QAAQ,EAAE,CACR,6BAA6B,EAC7B,4BAA4B,EAC5B,uBAAuB,EACvB,6BAA6B;KAEhC,EACD;MACEH,KAAK,EAAE,mBAAmB;MAC1BC,WAAW,EAAE,gFAAgF;MAC7FC,IAAI,EAAE,MAAM;MACZC,QAAQ,EAAE,CACR,gCAAgC,EAChC,2BAA2B,EAC3B,4BAA4B,EAC5B,wBAAwB;KAE3B,EACD;MACEH,KAAK,EAAE,qBAAqB;MAC5BC,WAAW,EAAE,0EAA0E;MACvFC,IAAI,EAAE,MAAM;MACZC,QAAQ,EAAE,CACR,mCAAmC,EACnC,+BAA+B,EAC/B,2BAA2B,EAC3B,yBAAyB;KAE5B,CACF;IAED,KAAAC,iBAAiB,GAAG,CAClB;MACEJ,KAAK,EAAE,kCAAkC;MACzCK,QAAQ,EAAE,oBAAoB;MAC9BJ,WAAW,EAAE,8EAA8E;MAC3FK,OAAO,EAAE,uCAAuC;MAChDC,KAAK,EAAE;KACR,EACD;MACEP,KAAK,EAAE,8BAA8B;MACrCK,QAAQ,EAAE,uBAAuB;MACjCJ,WAAW,EAAE,gEAAgE;MAC7EK,OAAO,EAAE,6CAA6C;MACtDC,KAAK,EAAE;KACR,EACD;MACEP,KAAK,EAAE,qBAAqB;MAC5BK,QAAQ,EAAE,qBAAqB;MAC/BJ,WAAW,EAAE,yEAAyE;MACtFM,KAAK,EAAE;KACR,CACF;IAED,KAAAC,cAAc,GAAoB,CAChC;MAAEC,GAAG,EAAE,aAAa;MAAEC,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE,MAAM;MAAEV,WAAW,EAAE;IAAsC,CAAE,EAC7G;MAAEQ,GAAG,EAAE,eAAe;MAAEC,IAAI,EAAE,QAAQ;MAAEC,OAAO,EAAE,uBAAuB;MAAEV,WAAW,EAAE;IAAwB,CAAE,EACjH;MAAEQ,GAAG,EAAE,YAAY;MAAEC,IAAI,EAAE,QAAQ;MAAEC,OAAO,EAAE,IAAI;MAAEV,WAAW,EAAE;IAA4B,CAAE,EAC/F;MAAEQ,GAAG,EAAE,cAAc;MAAEC,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE,MAAM;MAAEV,WAAW,EAAE;IAAkC,CAAE,EAC1G;MAAEQ,GAAG,EAAE,gBAAgB;MAAEC,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE,OAAO;MAAEV,WAAW,EAAE;IAAsC,CAAE,EACjH;MAAEQ,GAAG,EAAE,YAAY;MAAEC,IAAI,EAAE,OAAO;MAAEC,OAAO,EAAE,oCAAoC;MAAEV,WAAW,EAAE;IAA8B,CAAE,EAChI;MAAEQ,GAAG,EAAE,cAAc;MAAEC,IAAI,EAAE,QAAQ;MAAEC,OAAO,EAAE,UAAU;MAAEV,WAAW,EAAE;IAAgC,CAAE,EAC3G;MAAEQ,GAAG,EAAE,2BAA2B;MAAEC,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE,MAAM;MAAEV,WAAW,EAAE;IAAkC,CAAE,EACvH;MAAEQ,GAAG,EAAE,kBAAkB;MAAEC,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE,MAAM;MAAEV,WAAW,EAAE;IAAwC,CAAE,EACpH;MAAEQ,GAAG,EAAE,oBAAoB;MAAEC,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE,MAAM;MAAEV,WAAW,EAAE;IAAkC,CAAE,EAChH;MAAEQ,GAAG,EAAE,iBAAiB;MAAEC,IAAI,EAAE,SAAS;MAAEC,OAAO,EAAE,MAAM;MAAEV,WAAW,EAAE;IAAsC,CAAE,CAClH;IAED,KAAAW,aAAa,GAAG,CACd;MACEZ,KAAK,EAAE,gBAAgB;MACvBC,WAAW,EAAE,uCAAuC;MACpDC,IAAI,EAAE,SAAS;MACfW,QAAQ,EAAE,IAAI;MACdC,OAAO,EAAE,yGAAyG;MAClHC,KAAK,EAAE,CACL,sCAAsC,EACtC,2CAA2C,EAC3C,gDAAgD,EAChD,sDAAsD,CACvD;MACDC,IAAI,EAAE,CACJ,qDAAqD,EACrD,mDAAmD,EACnD,wCAAwC;KAE3C,EACD;MACEhB,KAAK,EAAE,+BAA+B;MACtCC,WAAW,EAAE,gDAAgD;MAC7DC,IAAI,EAAE,MAAM;MACZY,OAAO,EAAE,mHAAmH;MAC5HC,KAAK,EAAE,CACL,kDAAkD,EAClD,wDAAwD,EACxD,sCAAsC,EACtC,kCAAkC,CACnC;MACDC,IAAI,EAAE,CACJ,8CAA8C,EAC9C,yCAAyC,EACzC,0CAA0C;KAE7C,EACD;MACEhB,KAAK,EAAE,2BAA2B;MAClCC,WAAW,EAAE,8CAA8C;MAC3DC,IAAI,EAAE,MAAM;MACZY,OAAO,EAAE,mFAAmF;MAC5FC,KAAK,EAAE,CACL,gCAAgC,EAChC,6CAA6C,EAC7C,4CAA4C,EAC5C,qCAAqC,CACtC;MACDC,IAAI,EAAE,CACJ,2DAA2D,EAC3D,qDAAqD,EACrD,gDAAgD;KAEnD,CACF;IAED,KAAAC,qBAAqB,GAAG,CACtB;MACEC,OAAO,EAAE,oCAAoC;MAC7CjB,WAAW,EAAE,oDAAoD;MACjEC,IAAI,EAAE,WAAW;MACjBiB,QAAQ,EAAE,MAAM;MAChBC,QAAQ,EAAE,CACR,yDAAyD,EACzD,oDAAoD,EACpD,gDAAgD,EAChD,2CAA2C,CAC5C;MACDC,UAAU,EAAE;KACb,EACD;MACEH,OAAO,EAAE,6BAA6B;MACtCjB,WAAW,EAAE,0DAA0D;MACvEC,IAAI,EAAE,YAAY;MAClBiB,QAAQ,EAAE,QAAQ;MAClBC,QAAQ,EAAE,CACR,kEAAkE,EAClE,mDAAmD,EACnD,0CAA0C,EAC1C,wDAAwD,CACzD;MACDC,UAAU,EAAE;KACb,EACD;MACEH,OAAO,EAAE,oBAAoB;MAC7BjB,WAAW,EAAE,2CAA2C;MACxDC,IAAI,EAAE,OAAO;MACbiB,QAAQ,EAAE,QAAQ;MAClBC,QAAQ,EAAE,CACR,2CAA2C,EAC3C,mCAAmC,EACnC,kDAAkD,EAClD,gDAAgD,CACjD;MACDC,UAAU,EAAE;KACb,CACF;EAaH;EAXEC,eAAeA,CAACpB,IAAY;IAC1B,MAAMqB,MAAM,GAA8B;MACxC,UAAU,EAAE,SAAS;MACrB,YAAY,EAAE,SAAS;MACvB,YAAY,EAAE,SAAS;MACvB,MAAM,EAAE,SAAS;MACjB,MAAM,EAAE,SAAS;MACjB,MAAM,EAAE;KACT;IACD,OAAOA,MAAM,CAACrB,IAAI,CAAC,IAAI,SAAS;EAClC;CACD;AAvOYP,wBAAwB,GAAA6B,UAAA,EApmBpCtC,SAAS,CAAC;EACTuC,QAAQ,EAAE,sBAAsB;EAChCC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPxC,YAAY,EACZC,aAAa,EACbC,aAAa,EACbC,aAAa,EACbC,eAAe,EACfC,cAAc,EACdC,kBAAkB,EAClBC,cAAc,CACf;EACDkC,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAwVT;EACDC,MAAM,EAAE,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4PR;CACF,CAAC,C,EACWlC,wBAAwB,CAuOpC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}