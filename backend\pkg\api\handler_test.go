package api

import (
	"bytes"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"blockchain-spt/backend/pkg/config"
	"blockchain-spt/backend/pkg/models"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockScannerEngine is a mock implementation of ScannerEngine
type MockScannerEngine struct {
	mock.Mock
}

func (m *MockScannerEngine) StartScan(request *models.ScanRequest) (*models.ScanResponse, error) {
	args := m.Called(request)
	return args.Get(0).(*models.ScanResponse), args.Error(1)
}

func (m *MockScannerEngine) GetScanResult(scanID string) (*models.ScanResult, error) {
	args := m.Called(scanID)
	return args.Get(0).(*models.ScanResult), args.Error(1)
}

func (m *MockScannerEngine) GetScanHistory() ([]*models.ScanResult, error) {
	args := m.Called()
	return args.Get(0).([]*models.ScanResult), args.Error(1)
}

func (m *MockScannerEngine) ScanFile(filePath string, chains []string) (*models.ScanResult, error) {
	args := m.Called(filePath, chains)
	return args.Get(0).(*models.ScanResult), args.Error(1)
}

// MockStorage is a mock implementation of StorageInterface
type MockStorage struct {
	mock.Mock
}

func (m *MockStorage) GetScanResult(id string) (*models.ScanResult, error) {
	args := m.Called(id)
	return args.Get(0).(*models.ScanResult), args.Error(1)
}

func (m *MockStorage) CreateScanResult(result *models.ScanResult) error {
	args := m.Called(result)
	return args.Error(0)
}

func (m *MockStorage) UpdateScanResult(result *models.ScanResult) error {
	args := m.Called(result)
	return args.Error(0)
}

func (m *MockStorage) GetScanHistory(limit int) ([]*models.ScanResult, error) {
	args := m.Called(limit)
	return args.Get(0).([]*models.ScanResult), args.Error(1)
}

func setupTestHandler() (*Handler, *MockScannerEngine, *MockStorage) {
	gin.SetMode(gin.TestMode)

	mockScanner := new(MockScannerEngine)
	mockStorage := new(MockStorage)

	cfg := &config.Config{
		Server: config.ServerConfig{
			Host: "localhost",
			Port: 8080,
		},
	}

	handler := NewHandler(mockScanner, mockStorage, cfg)
	return handler, mockScanner, mockStorage
}

func TestHealthCheck(t *testing.T) {
	handler, _, _ := setupTestHandler()

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("GET", "/health", nil)

	handler.HealthCheck(c)

	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "healthy", response["status"])
}

func TestStartScan(t *testing.T) {
	handler, mockScanner, _ := setupTestHandler()

	// Setup mock expectations
	expectedResponse := &models.ScanResponse{
		ScanID:  "550e8400-e29b-41d4-a716-446655440000",
		Status:  "running",
		Message: "Scan started successfully",
	}

	mockScanner.On("StartScan", mock.AnythingOfType("*models.ScanRequest")).Return(expectedResponse, nil)

	// Create request
	requestBody := map[string]interface{}{
		"project_path": "/test/project",
		"chains":       []string{"ethereum"},
	}

	jsonBody, _ := json.Marshal(requestBody)
	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("POST", "/api/v1/scan/start", bytes.NewBuffer(jsonBody))
	c.Request.Header.Set("Content-Type", "application/json")

	handler.StartScan(c)

	assert.Equal(t, http.StatusOK, w.Code)

	var response models.ScanResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "550e8400-e29b-41d4-a716-446655440000", response.ScanID)
	assert.Equal(t, models.ScanStatus("running"), response.Status)

	mockScanner.AssertExpectations(t)
}

func TestGetScanResult(t *testing.T) {
	handler, mockScanner, _ := setupTestHandler()

	// Setup mock expectations
	expectedResult := &models.ScanResult{
		ID:          "550e8400-e29b-41d4-a716-446655440001",
		ProjectPath: "/test/project",
		Status:      models.ScanStatusCompleted,
		Chains:      models.StringArray{"ethereum"},
		Issues:      []models.SecurityIssue{},
	}

	mockScanner.On("GetScanResult", "550e8400-e29b-41d4-a716-446655440001").Return(expectedResult, nil)

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("GET", "/api/v1/scan/result/550e8400-e29b-41d4-a716-446655440001", nil)
	c.Params = gin.Params{{Key: "id", Value: "550e8400-e29b-41d4-a716-446655440001"}}

	handler.GetScanResult(c)

	assert.Equal(t, http.StatusOK, w.Code)

	var response models.ScanResult
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.Equal(t, "550e8400-e29b-41d4-a716-446655440001", response.ID)
	assert.Equal(t, models.ScanStatusCompleted, response.Status)

	mockScanner.AssertExpectations(t)
}

func TestGetSecurityChecklist(t *testing.T) {
	handler, _, _ := setupTestHandler()

	w := httptest.NewRecorder()
	c, _ := gin.CreateTestContext(w)
	c.Request = httptest.NewRequest("GET", "/api/v1/checklist?chain=ethereum", nil)

	// Set query parameters
	c.Request.URL.RawQuery = "chain=ethereum"

	handler.GetSecurityChecklist(c)

	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)

	checklist, ok := response["checklist"].([]interface{})
	assert.True(t, ok)
	assert.Greater(t, len(checklist), 0)

	// Check that all items are for Ethereum
	for _, item := range checklist {
		itemMap := item.(map[string]interface{})
		assert.Equal(t, "ethereum", itemMap["chain"])
	}
}

func TestValidateConfig(t *testing.T) {
	handler, _, _ := setupTestHandler()

	tests := []struct {
		name        string
		config      config.Config
		expectError bool
	}{
		{
			name: "valid config",
			config: config.Config{
				Server: config.ServerConfig{
					Host: "localhost",
					Port: 8080,
				},
				Database: config.DBConfig{
					Type: "postgres",
				},
				Scanning: config.ScanningConfig{
					FileTypes: []string{".sol", ".js"},
				},
				Logging: config.LoggingConfig{
					Level: "info",
				},
			},
			expectError: false,
		},
		{
			name: "invalid port",
			config: config.Config{
				Server: config.ServerConfig{
					Host: "localhost",
					Port: -1,
				},
			},
			expectError: true,
		},
		{
			name: "invalid database type",
			config: config.Config{
				Server: config.ServerConfig{
					Host: "localhost",
					Port: 8080,
				},
				Database: config.DBConfig{
					Type: "invalid",
				},
			},
			expectError: true,
		},
		{
			name: "invalid logging level",
			config: config.Config{
				Server: config.ServerConfig{
					Host: "localhost",
					Port: 8080,
				},
				Logging: config.LoggingConfig{
					Level: "invalid",
				},
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := handler.validateConfig(&tt.config)
			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}
