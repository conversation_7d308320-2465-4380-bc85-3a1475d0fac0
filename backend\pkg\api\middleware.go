package api

import (
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// AuthMiddleware provides authentication middleware that supports both JWT and API key authentication
func AuthMiddleware(storage APIKeyStorage) gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		// Skip authentication for health check and public endpoints
		if isPublicEndpoint(c.Request.URL.Path) {
			c.Next()
			return
		}

		// Check for API key first (for VS Code extension)
		apiKey := c.GetHeader("X-API-Key")
		if apiKey != "" {
			if validateAPIKey(storage, apiKey, c) {
				c.Next()
				return
			} else {
				logrus.Warn("Invalid API key provided")
				c.JSON(401, gin.H{"error": "Invalid API key"})
				c.Abort()
				return
			}
		}

		// Check for JWT token in Authorization header
		authHeader := c.<PERSON>eader("Authorization")
		var token string

		if strings.HasPrefix(authHeader, "Bearer ") {
			token = strings.TrimPrefix(authHeader, "Bearer ")
		}

		// Check if the token might be an API key (fallback for VS Code extension)
		if token != "" && !isValidJWTToken(token) && validateAPIKey(storage, token, c) {
			c.Next()
			return
		}

		// For development, allow requests without token but log it
		if token == "" {
			logrus.Warn("Request without authentication token - allowing for development")
			// Set default user for development
			c.Set("user_id", "dev_user")
			c.Set("username", "admin")
			c.Set("role", "admin")
			c.Set("auth_type", "development")
			c.Next()
			return
		}

		// Validate JWT token (simplified for development)
		if isValidJWTToken(token) {
			// Extract user info from token (simplified)
			userInfo := extractUserFromToken(token)
			c.Set("user_id", userInfo["user_id"])
			c.Set("username", userInfo["username"])
			c.Set("role", userInfo["role"])
			c.Set("auth_type", "jwt")
			c.Set("token", token)
			logrus.Infof("JWT authentication successful for user: %s", userInfo["username"])
		} else {
			// For development, still allow but log warning
			logrus.Warn("Invalid JWT token - allowing for development")
			c.Set("user_id", "dev_user")
			c.Set("username", "admin")
			c.Set("role", "admin")
			c.Set("auth_type", "development")
		}

		c.Next()
	})
}

// RateLimitMiddleware provides rate limiting
func RateLimitMiddleware() gin.HandlerFunc {
	// Simple in-memory rate limiter
	// In production, use Redis or similar
	requests := make(map[string][]time.Time)

	return gin.HandlerFunc(func(c *gin.Context) {
		clientIP := c.ClientIP()
		now := time.Now()

		// Clean old requests (older than 1 minute)
		if times, exists := requests[clientIP]; exists {
			var validTimes []time.Time
			for _, t := range times {
				if now.Sub(t) < time.Minute {
					validTimes = append(validTimes, t)
				}
			}
			requests[clientIP] = validTimes
		}

		// Check rate limit (100 requests per minute)
		if len(requests[clientIP]) >= 100 {
			c.JSON(http.StatusTooManyRequests, gin.H{
				"error":       "Rate limit exceeded",
				"code":        "RATE_LIMIT_EXCEEDED",
				"retry_after": 60,
			})
			c.Abort()
			return
		}

		// Add current request
		requests[clientIP] = append(requests[clientIP], now)

		c.Next()
	})
}

// CORSMiddleware provides CORS support
func CORSMiddleware() gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		origin := c.Request.Header.Get("Origin")

		// Allow specific origins in production
		allowedOrigins := []string{
			"http://localhost:4200",
			"http://localhost:3000",
			"http://127.0.0.1:4200",
			"http://127.0.0.1:3000",
		}

		isAllowed := false
		for _, allowed := range allowedOrigins {
			if origin == allowed {
				isAllowed = true
				break
			}
		}

		if isAllowed || origin == "" {
			c.Header("Access-Control-Allow-Origin", origin)
		}

		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Accept, Authorization, X-API-Key, X-Requested-With")
		c.Header("Access-Control-Allow-Credentials", "true")
		c.Header("Access-Control-Max-Age", "86400")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}

		c.Next()
	})
}

// LoggingMiddleware provides structured logging
func LoggingMiddleware() gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		logrus.WithFields(logrus.Fields{
			"timestamp":  param.TimeStamp.Format(time.RFC3339),
			"status":     param.StatusCode,
			"latency":    param.Latency,
			"client_ip":  param.ClientIP,
			"method":     param.Method,
			"path":       param.Path,
			"user_agent": param.Request.UserAgent(),
			"error":      param.ErrorMessage,
		}).Info("API Request")

		return ""
	})
}

// SecurityMiddleware adds security headers
func SecurityMiddleware() gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		// Security headers
		c.Header("X-Content-Type-Options", "nosniff")
		c.Header("X-Frame-Options", "DENY")
		c.Header("X-XSS-Protection", "1; mode=block")
		c.Header("Referrer-Policy", "strict-origin-when-cross-origin")
		c.Header("Content-Security-Policy", "default-src 'self'")

		c.Next()
	})
}

// ErrorHandlingMiddleware provides centralized error handling
func ErrorHandlingMiddleware() gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		c.Next()

		// Handle any errors that occurred during request processing
		if len(c.Errors) > 0 {
			err := c.Errors.Last()

			logrus.WithFields(logrus.Fields{
				"path":   c.Request.URL.Path,
				"method": c.Request.Method,
				"error":  err.Error(),
			}).Error("Request error")

			// Don't override status if already set
			if c.Writer.Status() == http.StatusOK {
				c.JSON(http.StatusInternalServerError, gin.H{
					"error": "Internal server error",
					"code":  "INTERNAL_ERROR",
				})
			}
		}
	})
}

// Helper functions
func isPublicEndpoint(path string) bool {
	publicEndpoints := []string{
		"/health",
		"/api/v1/health",
		"/api/v1/auth/login",
		"/api/v1/auth/register",
		"/api/v1/auth/refresh",
		"/metrics",
		"/docs",
		"/swagger",
		"/ws", // WebSocket endpoint
	}

	for _, endpoint := range publicEndpoints {
		if strings.HasPrefix(path, endpoint) {
			return true
		}
	}

	return false
}

// validateAPIKey validates an API key against the database and sets user context
func validateAPIKey(storage APIKeyStorage, apiKey string, c *gin.Context) bool {
	// Hash the API key to compare with stored hash
	hasher := sha256.New()
	hasher.Write([]byte(apiKey))
	keyHash := hex.EncodeToString(hasher.Sum(nil))

	// Get API key from database
	apiKeyRecord, err := storage.GetAPIKeyByHash(keyHash)
	if err != nil {
		logrus.WithError(err).Warn("Failed to validate API key")
		return false
	}

	// Set user context for API key authentication
	c.Set("user_id", apiKeyRecord.User.ID)
	c.Set("username", apiKeyRecord.User.Username)
	c.Set("role", string(apiKeyRecord.User.Role))
	c.Set("auth_type", "api_key")
	c.Set("api_key", apiKey)
	c.Set("api_key_id", apiKeyRecord.ID)

	// Update last used timestamp (async to avoid blocking)
	go func() {
		if err := storage.UpdateAPIKeyLastUsed(apiKeyRecord.ID); err != nil {
			logrus.WithError(err).Warn("Failed to update API key last used timestamp")
		}
	}()

	logrus.Infof("API key authentication successful for user: %s", apiKeyRecord.User.Username)
	return true
}

// isValidAPIKey - deprecated, kept for backward compatibility during development
func isValidAPIKey(apiKey string) bool {
	// Fallback for development when storage is not available
	validKeys := []string{
		"dev-key-123",
		"test-key-456",
		"demo-key-789",
		"vscode-extension-key", // Key for VS Code extension
	}

	for _, key := range validKeys {
		if apiKey == key {
			return true
		}
	}

	return false
}

// getUserInfoFromAPIKey - deprecated, kept for backward compatibility during development
func getUserInfoFromAPIKey(apiKey string) map[string]string {
	// Fallback for development when storage is not available
	switch apiKey {
	case "dev-key-123":
		return map[string]string{
			"user_id":  "api_user_1",
			"username": "dev_api_user",
			"role":     "developer",
		}
	case "test-key-456":
		return map[string]string{
			"user_id":  "api_user_2",
			"username": "test_api_user",
			"role":     "tester",
		}
	case "demo-key-789":
		return map[string]string{
			"user_id":  "api_user_3",
			"username": "demo_api_user",
			"role":     "user",
		}
	case "vscode-extension-key":
		return map[string]string{
			"user_id":  "vscode_user",
			"username": "vscode_extension",
			"role":     "admin", // VS Code extension gets admin privileges
		}
	default:
		// Fallback for unknown API keys
		return map[string]string{
			"user_id":  "unknown_api_user",
			"username": "unknown",
			"role":     "user",
		}
	}
}

// isValidJWTToken validates a JWT token (simplified for development)
func isValidJWTToken(token string) bool {
	// Split token into parts
	parts := strings.Split(token, ".")
	if len(parts) != 3 {
		return false
	}

	// For development, just check if it's a properly formatted JWT
	// In production, verify signature and expiration
	return true
}

// extractUserFromToken extracts user information from JWT token
func extractUserFromToken(token string) map[string]string {
	parts := strings.Split(token, ".")
	if len(parts) != 3 {
		return map[string]string{
			"user_id":  "unknown",
			"username": "unknown",
			"role":     "user",
		}
	}

	// Decode payload (second part)
	payload := parts[1]

	// Add padding if needed for base64 decoding
	for len(payload)%4 != 0 {
		payload += "="
	}

	decoded, err := base64.URLEncoding.DecodeString(payload)
	if err != nil {
		logrus.Warnf("Failed to decode JWT payload: %v", err)
		return map[string]string{
			"user_id":  "unknown",
			"username": "unknown",
			"role":     "user",
		}
	}

	var claims map[string]interface{}
	if err := json.Unmarshal(decoded, &claims); err != nil {
		logrus.Warnf("Failed to unmarshal JWT claims: %v", err)
		return map[string]string{
			"user_id":  "unknown",
			"username": "unknown",
			"role":     "user",
		}
	}

	// Extract user information
	userInfo := map[string]string{
		"user_id":  "1",     // Default
		"username": "admin", // Default
		"role":     "admin", // Default
	}

	if sub, ok := claims["sub"].(string); ok {
		userInfo["user_id"] = sub
	}
	if username, ok := claims["username"].(string); ok {
		userInfo["username"] = username
	}
	if role, ok := claims["role"].(string); ok {
		userInfo["role"] = role
	}

	return userInfo
}

// RequestIDMiddleware adds a unique request ID to each request
func RequestIDMiddleware() gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		requestID := c.GetHeader("X-Request-ID")
		if requestID == "" {
			requestID = generateRequestID()
		}

		c.Header("X-Request-ID", requestID)
		c.Set("request_id", requestID)

		c.Next()
	})
}

func generateRequestID() string {
	// Simple request ID generation
	// In production, use UUID or similar
	return fmt.Sprintf("req_%d", time.Now().UnixNano())
}

// MetricsMiddleware collects basic metrics
func MetricsMiddleware() gin.HandlerFunc {
	return gin.HandlerFunc(func(c *gin.Context) {
		start := time.Now()

		c.Next()

		duration := time.Since(start)

		// Log metrics (in production, send to metrics service)
		logrus.WithFields(logrus.Fields{
			"endpoint":      c.Request.URL.Path,
			"method":        c.Request.Method,
			"status_code":   c.Writer.Status(),
			"duration_ms":   duration.Milliseconds(),
			"request_size":  c.Request.ContentLength,
			"response_size": c.Writer.Size(),
		}).Debug("Request metrics")
	})
}
