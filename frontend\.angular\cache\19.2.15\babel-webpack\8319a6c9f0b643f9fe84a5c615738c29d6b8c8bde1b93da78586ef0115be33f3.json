{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatChipsModule } from '@angular/material/chips';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/material/card\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/chips\";\nfunction OverviewComponent_mat_card_22_mat_chip_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-chip\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const tag_r1 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(tag_r1);\n  }\n}\nfunction OverviewComponent_mat_card_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 20)(1, \"mat-card-header\")(2, \"mat-icon\", 17);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-card-title\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-card-content\")(7, \"p\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"mat-chip-listbox\", 21);\n    i0.ɵɵtemplate(10, OverviewComponent_mat_card_22_mat_chip_10_Template, 2, 1, \"mat-chip\", 22);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const feature_r2 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"background-color\", feature_r2.color);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", feature_r2.icon, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(feature_r2.title);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(feature_r2.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", feature_r2.tags);\n  }\n}\nfunction OverviewComponent_mat_card_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 23)(1, \"mat-card-header\")(2, \"mat-icon\", 17);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-card-title\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"mat-card-subtitle\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"mat-card-content\")(9, \"p\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"mat-card-actions\")(12, \"button\", 24)(13, \"mat-icon\");\n    i0.ɵɵtext(14, \"description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(15, \" Documentation \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const tool_r3 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(tool_r3.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(tool_r3.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(tool_r3.type);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(tool_r3.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"routerLink\", tool_r3.docLink);\n  }\n}\nfunction OverviewComponent_div_39_code_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"code\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const step_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(step_r4.code);\n  }\n}\nfunction OverviewComponent_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 27)(4, \"h4\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, OverviewComponent_div_39_code_8_Template, 2, 1, \"code\", 28);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const step_r4 = ctx.$implicit;\n    const i_r5 = ctx.index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r5 + 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(step_r4.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(step_r4.description);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", step_r4.code);\n  }\n}\nexport class OverviewComponent {\n  constructor() {\n    this.features = [{\n      title: 'Smart Contract Analysis',\n      description: 'Comprehensive security analysis for Ethereum smart contracts with vulnerability detection and gas optimization suggestions.',\n      icon: 'smart_toy',\n      color: '#4caf50',\n      tags: ['Solidity', 'Ethereum', 'Security']\n    }, {\n      title: 'Bitcoin Security',\n      description: 'Advanced security checks for Bitcoin applications including wallet security, transaction validation, and UTXO management.',\n      icon: 'currency_bitcoin',\n      color: '#ff9800',\n      tags: ['Bitcoin', 'Wallet', 'UTXO']\n    }, {\n      title: 'Real-time Monitoring',\n      description: 'Live security monitoring with WebSocket connections, real-time alerts, and continuous vulnerability scanning.',\n      icon: 'monitor_heart',\n      color: '#f44336',\n      tags: ['Real-time', 'Monitoring', 'Alerts']\n    }, {\n      title: 'Comprehensive Reports',\n      description: 'Detailed security reports with executive summaries, technical details, and actionable recommendations.',\n      icon: 'assessment',\n      color: '#9c27b0',\n      tags: ['Reports', 'Analytics', 'PDF']\n    }];\n    this.tools = [{\n      name: 'Web Dashboard',\n      type: 'Angular Application',\n      description: 'Modern web interface for security scanning, project management, and report generation.',\n      icon: 'dashboard',\n      docLink: '/doc/getting-started'\n    }, {\n      name: 'CLI Tool',\n      type: 'Command Line',\n      description: 'Powerful command-line interface for automated security scanning and CI/CD integration.',\n      icon: 'terminal',\n      docLink: '/doc/cli-guide'\n    }, {\n      name: 'VS Code Extension',\n      type: 'IDE Integration',\n      description: 'Real-time security highlighting and inline suggestions directly in your code editor.',\n      icon: 'extension',\n      docLink: '/doc/vscode-extension'\n    }, {\n      name: 'REST API',\n      type: 'Backend Service',\n      description: 'RESTful API for integrating SPT security scanning into your development workflow.',\n      icon: 'api',\n      docLink: '/doc/api-reference'\n    }];\n    this.quickStartSteps = [{\n      title: 'Clone Repository',\n      description: 'Get the SPT source code from GitHub',\n      code: 'git clone https://github.com/blockchain-spt/spt.git'\n    }, {\n      title: 'Start Backend',\n      description: 'Launch the Go backend server',\n      code: 'cd backend && go run cmd/main.go'\n    }, {\n      title: 'Start Frontend',\n      description: 'Launch the Angular development server',\n      code: 'cd frontend && npm start'\n    }, {\n      title: 'Begin Scanning',\n      description: 'Access the web dashboard and start your first security scan',\n      code: 'Open http://localhost:4200'\n    }];\n  }\n  static {\n    this.ɵfac = function OverviewComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OverviewComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: OverviewComponent,\n      selectors: [[\"app-overview\"]],\n      decls: 45,\n      vars: 3,\n      consts: [[1, \"overview-container\"], [1, \"hero-section\"], [1, \"hero-content\"], [1, \"hero-title\"], [1, \"hero-icon\"], [1, \"hero-subtitle\"], [1, \"hero-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/doc/getting-started\"], [\"mat-stroked-button\", \"\", \"routerLink\", \"/doc/api-reference\"], [1, \"features-section\"], [1, \"features-grid\"], [\"class\", \"feature-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"tools-section\"], [1, \"tools-grid\"], [\"class\", \"tool-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"quick-start-section\"], [1, \"quick-start-card\"], [\"mat-card-avatar\", \"\"], [1, \"quick-start-steps\"], [\"class\", \"step\", 4, \"ngFor\", \"ngForOf\"], [1, \"feature-card\"], [1, \"feature-tags\"], [4, \"ngFor\", \"ngForOf\"], [1, \"tool-card\"], [\"mat-button\", \"\", 3, \"routerLink\"], [1, \"step\"], [1, \"step-number\"], [1, \"step-content\"], [\"class\", \"step-code\", 4, \"ngIf\"], [1, \"step-code\"]],\n      template: function OverviewComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"h1\", 3)(4, \"mat-icon\", 4);\n          i0.ɵɵtext(5, \"shield\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(6, \" Blockchain Security Protocol Tool \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"p\", 5);\n          i0.ɵɵtext(8, \" Comprehensive security analysis and auditing for Ethereum and Bitcoin blockchain applications \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"div\", 6)(10, \"button\", 7)(11, \"mat-icon\");\n          i0.ɵɵtext(12, \"play_arrow\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(13, \" Get Started \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"button\", 8)(15, \"mat-icon\");\n          i0.ɵɵtext(16, \"api\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(17, \" API Reference \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(18, \"div\", 9)(19, \"h2\");\n          i0.ɵɵtext(20, \"Key Features\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"div\", 10);\n          i0.ɵɵtemplate(22, OverviewComponent_mat_card_22_Template, 11, 6, \"mat-card\", 11);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(23, \"div\", 12)(24, \"h2\");\n          i0.ɵɵtext(25, \"Available Tools\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"div\", 13);\n          i0.ɵɵtemplate(27, OverviewComponent_mat_card_27_Template, 16, 5, \"mat-card\", 14);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(28, \"div\", 15)(29, \"mat-card\", 16)(30, \"mat-card-header\")(31, \"mat-icon\", 17);\n          i0.ɵɵtext(32, \"rocket_launch\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"mat-card-title\");\n          i0.ɵɵtext(34, \"Quick Start\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"mat-card-subtitle\");\n          i0.ɵɵtext(36, \"Get SPT running in minutes\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(37, \"mat-card-content\")(38, \"div\", 18);\n          i0.ɵɵtemplate(39, OverviewComponent_div_39_Template, 9, 4, \"div\", 19);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(40, \"mat-card-actions\")(41, \"button\", 7)(42, \"mat-icon\");\n          i0.ɵɵtext(43, \"arrow_forward\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(44, \" Full Installation Guide \");\n          i0.ɵɵelementEnd()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(22);\n          i0.ɵɵproperty(\"ngForOf\", ctx.features);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", ctx.tools);\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngForOf\", ctx.quickStartSteps);\n        }\n      },\n      dependencies: [CommonModule, i1.NgForOf, i1.NgIf, RouterModule, i2.RouterLink, MatCardModule, i3.MatCard, i3.MatCardActions, i3.MatCardAvatar, i3.MatCardContent, i3.MatCardHeader, i3.MatCardSubtitle, i3.MatCardTitle, MatIconModule, i4.MatIcon, MatButtonModule, i5.MatButton, MatChipsModule, i6.MatChip, i6.MatChipListbox],\n      styles: [\".overview-container[_ngcontent-%COMP%] {\\n  max-width: 1200px;\\n  margin: 0 auto;\\n}\\n\\n.hero-section[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 64px 32px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  color: white;\\n  border-radius: 24px;\\n  margin-bottom: 48px;\\n  position: relative;\\n  overflow: hidden;\\n  box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);\\n}\\n\\n.hero-section[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: url('data:image/svg+xml,<svg xmlns=\\\"http://www.w3.org/2000/svg\\\" viewBox=\\\"0 0 100 100\\\"><defs><pattern id=\\\"grain\\\" width=\\\"100\\\" height=\\\"100\\\" patternUnits=\\\"userSpaceOnUse\\\"><circle cx=\\\"25\\\" cy=\\\"25\\\" r=\\\"1\\\" fill=\\\"white\\\" opacity=\\\"0.1\\\"/><circle cx=\\\"75\\\" cy=\\\"75\\\" r=\\\"1\\\" fill=\\\"white\\\" opacity=\\\"0.1\\\"/><circle cx=\\\"50\\\" cy=\\\"10\\\" r=\\\"0.5\\\" fill=\\\"white\\\" opacity=\\\"0.1\\\"/><circle cx=\\\"10\\\" cy=\\\"60\\\" r=\\\"0.5\\\" fill=\\\"white\\\" opacity=\\\"0.1\\\"/><circle cx=\\\"90\\\" cy=\\\"40\\\" r=\\\"0.5\\\" fill=\\\"white\\\" opacity=\\\"0.1\\\"/></pattern></defs><rect width=\\\"100\\\" height=\\\"100\\\" fill=\\\"url(%23grain)\\\"/></svg>');\\n  pointer-events: none;\\n}\\n\\n.hero-content[_ngcontent-%COMP%] {\\n  padding: 0 24px;\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.hero-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 20px;\\n  font-size: 2.8em;\\n  font-weight: 700;\\n  margin: 0 0 20px 0;\\n  letter-spacing: -1px;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n.hero-icon[_ngcontent-%COMP%] {\\n  font-size: 56px;\\n  width: 56px;\\n  height: 56px;\\n  background: rgba(255, 255, 255, 0.2);\\n  border-radius: 16px;\\n  padding: 12px;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n\\n.hero-subtitle[_ngcontent-%COMP%] {\\n  font-size: 1.3em;\\n  margin: 0 0 40px 0;\\n  opacity: 0.95;\\n  max-width: 700px;\\n  margin-left: auto;\\n  margin-right: auto;\\n  line-height: 1.5;\\n  font-weight: 400;\\n}\\n\\n.hero-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 20px;\\n  justify-content: center;\\n  flex-wrap: wrap;\\n}\\n\\n.hero-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  padding: 12px 32px;\\n  font-size: 1.1em;\\n  font-weight: 600;\\n  border-radius: 50px;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  text-transform: none;\\n  letter-spacing: 0.5px;\\n}\\n\\n.hero-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:first-child {\\n  background: rgba(255, 255, 255, 0.2);\\n  border: 2px solid rgba(255, 255, 255, 0.3);\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n\\n.hero-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:first-child:hover {\\n  background: rgba(255, 255, 255, 0.3);\\n  transform: translateY(-2px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);\\n}\\n\\n.hero-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:last-child {\\n  background: rgba(255, 255, 255, 0.1);\\n  border: 2px solid rgba(255, 255, 255, 0.2);\\n}\\n\\n.hero-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:last-child:hover {\\n  background: rgba(255, 255, 255, 0.2);\\n  transform: translateY(-2px);\\n}\\n\\n.features-section[_ngcontent-%COMP%], \\n.tools-section[_ngcontent-%COMP%] {\\n  margin-bottom: 64px;\\n}\\n\\n.features-section[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], \\n.tools-section[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 40px;\\n  color: #4c63d2;\\n  font-size: 2.2em;\\n  font-weight: 700;\\n  letter-spacing: -0.5px;\\n}\\n\\n.features-grid[_ngcontent-%COMP%], \\n.tools-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));\\n  gap: 32px;\\n}\\n\\n.feature-card[_ngcontent-%COMP%], \\n.tool-card[_ngcontent-%COMP%] {\\n  height: 100%;\\n  border-radius: 20px;\\n  border: none;\\n  background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);\\n  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.1);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  overflow: hidden;\\n  position: relative;\\n}\\n\\n.feature-card[_ngcontent-%COMP%]::before, \\n.tool-card[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  height: 4px;\\n  background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);\\n}\\n\\n.feature-card[_ngcontent-%COMP%]:hover, \\n.tool-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-8px);\\n  box-shadow: 0 20px 40px rgba(102, 126, 234, 0.2);\\n}\\n\\n.feature-tags[_ngcontent-%COMP%] {\\n  margin-top: 20px;\\n}\\n\\n.feature-tags[_ngcontent-%COMP%]   mat-chip[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\\n  color: white;\\n  border-radius: 20px;\\n  font-weight: 500;\\n  font-size: 0.85em;\\n  margin: 4px 8px 4px 0;\\n  padding: 8px 16px;\\n  border: none;\\n}\\n\\n.quick-start-section[_ngcontent-%COMP%] {\\n  margin-bottom: 64px;\\n}\\n\\n.quick-start-card[_ngcontent-%COMP%] {\\n  max-width: 900px;\\n  margin: 0 auto;\\n  border-radius: 24px;\\n  background: linear-gradient(135deg, #ffffff 0%, #f0f4ff 100%);\\n  border: 1px solid #e8eaff;\\n  box-shadow: 0 12px 40px rgba(102, 126, 234, 0.15);\\n  overflow: hidden;\\n  position: relative;\\n}\\n\\n.quick-start-card[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  height: 6px;\\n  background: linear-gradient(90deg, #4facfe 0%, #00f2fe 50%, #43e97b 100%);\\n}\\n\\n.quick-start-steps[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 24px;\\n}\\n\\n.step[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  align-items: flex-start;\\n}\\n\\n.step-number[_ngcontent-%COMP%] {\\n  background: #1976d2;\\n  color: white;\\n  width: 32px;\\n  height: 32px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-weight: bold;\\n  flex-shrink: 0;\\n}\\n\\n.step-content[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  color: #1976d2;\\n}\\n\\n.step-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  color: #666;\\n}\\n\\n.step-code[_ngcontent-%COMP%] {\\n  background: #f5f5f5;\\n  padding: 8px 12px;\\n  border-radius: 4px;\\n  font-family: \\\"Courier New\\\", monospace;\\n  font-size: 0.9em;\\n  display: block;\\n  margin-top: 8px;\\n}\\n\\n@media (max-width: 768px) {\\n  .hero-title[_ngcontent-%COMP%] {\\n    font-size: 2em;\\n    flex-direction: column;\\n    gap: 8px;\\n  }\\n  .hero-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: center;\\n  }\\n  .features-grid[_ngcontent-%COMP%], \\n   .tools-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "MatCardModule", "MatIconModule", "MatButtonModule", "MatChipsModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "tag_r1", "ɵɵtemplate", "OverviewComponent_mat_card_22_mat_chip_10_Template", "ɵɵstyleProp", "feature_r2", "color", "ɵɵtextInterpolate1", "icon", "title", "description", "ɵɵproperty", "tags", "tool_r3", "name", "type", "docLink", "step_r4", "code", "OverviewComponent_div_39_code_8_Template", "i_r5", "OverviewComponent", "constructor", "features", "tools", "quickStartSteps", "selectors", "decls", "vars", "consts", "template", "OverviewComponent_Template", "rf", "ctx", "OverviewComponent_mat_card_22_Template", "OverviewComponent_mat_card_27_Template", "OverviewComponent_div_39_Template", "i1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "i2", "RouterLink", "i3", "MatCard", "MatCardActions", "MatCardAvatar", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardHeader", "MatCardSubtitle", "MatCardTitle", "i4", "MatIcon", "i5", "MatButton", "i6", "MatChip", "MatChipListbox", "styles"], "sources": ["D:\\TGI\\Blockchain.SPT\\frontend\\src\\app\\modules\\documentation\\components\\overview\\overview.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatChipsModule } from '@angular/material/chips';\n\n@Component({\n  selector: 'app-overview',\n  standalone: true,\n  imports: [\n    CommonModule,\n    RouterModule,\n    MatCardModule,\n    MatIconModule,\n    MatButtonModule,\n    MatChipsModule\n  ],\n  template: `\n    <div class=\"overview-container\">\n      <div class=\"hero-section\">\n        <div class=\"hero-content\">\n          <h1 class=\"hero-title\">\n            <mat-icon class=\"hero-icon\">shield</mat-icon>\n            Blockchain Security Protocol Tool\n          </h1>\n          <p class=\"hero-subtitle\">\n            Comprehensive security analysis and auditing for Ethereum and Bitcoin blockchain applications\n          </p>\n          <div class=\"hero-actions\">\n            <button mat-raised-button color=\"primary\" routerLink=\"/doc/getting-started\">\n              <mat-icon>play_arrow</mat-icon>\n              Get Started\n            </button>\n            <button mat-stroked-button routerLink=\"/doc/api-reference\">\n              <mat-icon>api</mat-icon>\n              API Reference\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <div class=\"features-section\">\n        <h2>Key Features</h2>\n        <div class=\"features-grid\">\n          <mat-card class=\"feature-card\" *ngFor=\"let feature of features\">\n            <mat-card-header>\n              <mat-icon mat-card-avatar [style.background-color]=\"feature.color\">\n                {{ feature.icon }}\n              </mat-icon>\n              <mat-card-title>{{ feature.title }}</mat-card-title>\n            </mat-card-header>\n            <mat-card-content>\n              <p>{{ feature.description }}</p>\n              <mat-chip-listbox class=\"feature-tags\">\n                <mat-chip *ngFor=\"let tag of feature.tags\">{{ tag }}</mat-chip>\n              </mat-chip-listbox>\n            </mat-card-content>\n          </mat-card>\n        </div>\n      </div>\n\n      <div class=\"tools-section\">\n        <h2>Available Tools</h2>\n        <div class=\"tools-grid\">\n          <mat-card class=\"tool-card\" *ngFor=\"let tool of tools\">\n            <mat-card-header>\n              <mat-icon mat-card-avatar>{{ tool.icon }}</mat-icon>\n              <mat-card-title>{{ tool.name }}</mat-card-title>\n              <mat-card-subtitle>{{ tool.type }}</mat-card-subtitle>\n            </mat-card-header>\n            <mat-card-content>\n              <p>{{ tool.description }}</p>\n            </mat-card-content>\n            <mat-card-actions>\n              <button mat-button [routerLink]=\"tool.docLink\">\n                <mat-icon>description</mat-icon>\n                Documentation\n              </button>\n            </mat-card-actions>\n          </mat-card>\n        </div>\n      </div>\n\n      <div class=\"quick-start-section\">\n        <mat-card class=\"quick-start-card\">\n          <mat-card-header>\n            <mat-icon mat-card-avatar>rocket_launch</mat-icon>\n            <mat-card-title>Quick Start</mat-card-title>\n            <mat-card-subtitle>Get SPT running in minutes</mat-card-subtitle>\n          </mat-card-header>\n          <mat-card-content>\n            <div class=\"quick-start-steps\">\n              <div class=\"step\" *ngFor=\"let step of quickStartSteps; let i = index\">\n                <div class=\"step-number\">{{ i + 1 }}</div>\n                <div class=\"step-content\">\n                  <h4>{{ step.title }}</h4>\n                  <p>{{ step.description }}</p>\n                  <code class=\"step-code\" *ngIf=\"step.code\">{{ step.code }}</code>\n                </div>\n              </div>\n            </div>\n          </mat-card-content>\n          <mat-card-actions>\n            <button mat-raised-button color=\"primary\" routerLink=\"/doc/getting-started\">\n              <mat-icon>arrow_forward</mat-icon>\n              Full Installation Guide\n            </button>\n          </mat-card-actions>\n        </mat-card>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .overview-container {\n      max-width: 1200px;\n      margin: 0 auto;\n    }\n\n    .hero-section {\n      text-align: center;\n      padding: 64px 32px;\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      color: white;\n      border-radius: 24px;\n      margin-bottom: 48px;\n      position: relative;\n      overflow: hidden;\n      box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);\n    }\n\n    .hero-section::before {\n      content: '';\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grain\" width=\"100\" height=\"100\" patternUnits=\"userSpaceOnUse\"><circle cx=\"25\" cy=\"25\" r=\"1\" fill=\"white\" opacity=\"0.1\"/><circle cx=\"75\" cy=\"75\" r=\"1\" fill=\"white\" opacity=\"0.1\"/><circle cx=\"50\" cy=\"10\" r=\"0.5\" fill=\"white\" opacity=\"0.1\"/><circle cx=\"10\" cy=\"60\" r=\"0.5\" fill=\"white\" opacity=\"0.1\"/><circle cx=\"90\" cy=\"40\" r=\"0.5\" fill=\"white\" opacity=\"0.1\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grain)\"/></svg>');\n      pointer-events: none;\n    }\n\n    .hero-content {\n      padding: 0 24px;\n      position: relative;\n      z-index: 1;\n    }\n\n    .hero-title {\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      gap: 20px;\n      font-size: 2.8em;\n      font-weight: 700;\n      margin: 0 0 20px 0;\n      letter-spacing: -1px;\n      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n    }\n\n    .hero-icon {\n      font-size: 56px;\n      width: 56px;\n      height: 56px;\n      background: rgba(255, 255, 255, 0.2);\n      border-radius: 16px;\n      padding: 12px;\n      backdrop-filter: blur(10px);\n    }\n\n    .hero-subtitle {\n      font-size: 1.3em;\n      margin: 0 0 40px 0;\n      opacity: 0.95;\n      max-width: 700px;\n      margin-left: auto;\n      margin-right: auto;\n      line-height: 1.5;\n      font-weight: 400;\n    }\n\n    .hero-actions {\n      display: flex;\n      gap: 20px;\n      justify-content: center;\n      flex-wrap: wrap;\n    }\n\n    .hero-actions button {\n      padding: 12px 32px;\n      font-size: 1.1em;\n      font-weight: 600;\n      border-radius: 50px;\n      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n      text-transform: none;\n      letter-spacing: 0.5px;\n    }\n\n    .hero-actions button:first-child {\n      background: rgba(255, 255, 255, 0.2);\n      border: 2px solid rgba(255, 255, 255, 0.3);\n      backdrop-filter: blur(10px);\n    }\n\n    .hero-actions button:first-child:hover {\n      background: rgba(255, 255, 255, 0.3);\n      transform: translateY(-2px);\n      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);\n    }\n\n    .hero-actions button:last-child {\n      background: rgba(255, 255, 255, 0.1);\n      border: 2px solid rgba(255, 255, 255, 0.2);\n    }\n\n    .hero-actions button:last-child:hover {\n      background: rgba(255, 255, 255, 0.2);\n      transform: translateY(-2px);\n    }\n\n    .features-section,\n    .tools-section {\n      margin-bottom: 64px;\n    }\n\n    .features-section h2,\n    .tools-section h2 {\n      text-align: center;\n      margin-bottom: 40px;\n      color: #4c63d2;\n      font-size: 2.2em;\n      font-weight: 700;\n      letter-spacing: -0.5px;\n    }\n\n    .features-grid,\n    .tools-grid {\n      display: grid;\n      grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));\n      gap: 32px;\n    }\n\n    .feature-card,\n    .tool-card {\n      height: 100%;\n      border-radius: 20px;\n      border: none;\n      background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);\n      box-shadow: 0 8px 32px rgba(102, 126, 234, 0.1);\n      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n      overflow: hidden;\n      position: relative;\n    }\n\n    .feature-card::before,\n    .tool-card::before {\n      content: '';\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      height: 4px;\n      background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);\n    }\n\n    .feature-card:hover,\n    .tool-card:hover {\n      transform: translateY(-8px);\n      box-shadow: 0 20px 40px rgba(102, 126, 234, 0.2);\n    }\n\n    .feature-tags {\n      margin-top: 20px;\n    }\n\n    .feature-tags mat-chip {\n      background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n      color: white;\n      border-radius: 20px;\n      font-weight: 500;\n      font-size: 0.85em;\n      margin: 4px 8px 4px 0;\n      padding: 8px 16px;\n      border: none;\n    }\n\n    .quick-start-section {\n      margin-bottom: 64px;\n    }\n\n    .quick-start-card {\n      max-width: 900px;\n      margin: 0 auto;\n      border-radius: 24px;\n      background: linear-gradient(135deg, #ffffff 0%, #f0f4ff 100%);\n      border: 1px solid #e8eaff;\n      box-shadow: 0 12px 40px rgba(102, 126, 234, 0.15);\n      overflow: hidden;\n      position: relative;\n    }\n\n    .quick-start-card::before {\n      content: '';\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      height: 6px;\n      background: linear-gradient(90deg, #4facfe 0%, #00f2fe 50%, #43e97b 100%);\n    }\n\n    .quick-start-steps {\n      display: flex;\n      flex-direction: column;\n      gap: 24px;\n    }\n\n    .step {\n      display: flex;\n      gap: 16px;\n      align-items: flex-start;\n    }\n\n    .step-number {\n      background: #1976d2;\n      color: white;\n      width: 32px;\n      height: 32px;\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      font-weight: bold;\n      flex-shrink: 0;\n    }\n\n    .step-content h4 {\n      margin: 0 0 8px 0;\n      color: #1976d2;\n    }\n\n    .step-content p {\n      margin: 0 0 8px 0;\n      color: #666;\n    }\n\n    .step-code {\n      background: #f5f5f5;\n      padding: 8px 12px;\n      border-radius: 4px;\n      font-family: 'Courier New', monospace;\n      font-size: 0.9em;\n      display: block;\n      margin-top: 8px;\n    }\n\n    @media (max-width: 768px) {\n      .hero-title {\n        font-size: 2em;\n        flex-direction: column;\n        gap: 8px;\n      }\n\n      .hero-actions {\n        flex-direction: column;\n        align-items: center;\n      }\n\n      .features-grid,\n      .tools-grid {\n        grid-template-columns: 1fr;\n      }\n    }\n  `]\n})\nexport class OverviewComponent {\n  features = [\n    {\n      title: 'Smart Contract Analysis',\n      description: 'Comprehensive security analysis for Ethereum smart contracts with vulnerability detection and gas optimization suggestions.',\n      icon: 'smart_toy',\n      color: '#4caf50',\n      tags: ['Solidity', 'Ethereum', 'Security']\n    },\n    {\n      title: 'Bitcoin Security',\n      description: 'Advanced security checks for Bitcoin applications including wallet security, transaction validation, and UTXO management.',\n      icon: 'currency_bitcoin',\n      color: '#ff9800',\n      tags: ['Bitcoin', 'Wallet', 'UTXO']\n    },\n    {\n      title: 'Real-time Monitoring',\n      description: 'Live security monitoring with WebSocket connections, real-time alerts, and continuous vulnerability scanning.',\n      icon: 'monitor_heart',\n      color: '#f44336',\n      tags: ['Real-time', 'Monitoring', 'Alerts']\n    },\n    {\n      title: 'Comprehensive Reports',\n      description: 'Detailed security reports with executive summaries, technical details, and actionable recommendations.',\n      icon: 'assessment',\n      color: '#9c27b0',\n      tags: ['Reports', 'Analytics', 'PDF']\n    }\n  ];\n\n  tools = [\n    {\n      name: 'Web Dashboard',\n      type: 'Angular Application',\n      description: 'Modern web interface for security scanning, project management, and report generation.',\n      icon: 'dashboard',\n      docLink: '/doc/getting-started'\n    },\n    {\n      name: 'CLI Tool',\n      type: 'Command Line',\n      description: 'Powerful command-line interface for automated security scanning and CI/CD integration.',\n      icon: 'terminal',\n      docLink: '/doc/cli-guide'\n    },\n    {\n      name: 'VS Code Extension',\n      type: 'IDE Integration',\n      description: 'Real-time security highlighting and inline suggestions directly in your code editor.',\n      icon: 'extension',\n      docLink: '/doc/vscode-extension'\n    },\n    {\n      name: 'REST API',\n      type: 'Backend Service',\n      description: 'RESTful API for integrating SPT security scanning into your development workflow.',\n      icon: 'api',\n      docLink: '/doc/api-reference'\n    }\n  ];\n\n  quickStartSteps = [\n    {\n      title: 'Clone Repository',\n      description: 'Get the SPT source code from GitHub',\n      code: 'git clone https://github.com/blockchain-spt/spt.git'\n    },\n    {\n      title: 'Start Backend',\n      description: 'Launch the Go backend server',\n      code: 'cd backend && go run cmd/main.go'\n    },\n    {\n      title: 'Start Frontend',\n      description: 'Launch the Angular development server',\n      code: 'cd frontend && npm start'\n    },\n    {\n      title: 'Begin Scanning',\n      description: 'Access the web dashboard and start your first security scan',\n      code: 'Open http://localhost:4200'\n    }\n  ];\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,cAAc,QAAQ,yBAAyB;;;;;;;;;;IAkDxCC,EAAA,CAAAC,cAAA,eAA2C;IAAAD,EAAA,CAAAE,MAAA,GAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;IAApBH,EAAA,CAAAI,SAAA,EAAS;IAATJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAS;;;;;IARtDN,EAFJ,CAAAC,cAAA,mBAAgE,sBAC7C,mBACoD;IACjED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACXH,EAAA,CAAAC,cAAA,qBAAgB;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IACrCF,EADqC,CAAAG,YAAA,EAAiB,EACpC;IAEhBH,EADF,CAAAC,cAAA,uBAAkB,QACb;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAChCH,EAAA,CAAAC,cAAA,2BAAuC;IACrCD,EAAA,CAAAO,UAAA,KAAAC,kDAAA,uBAA2C;IAGjDR,EAFI,CAAAG,YAAA,EAAmB,EACF,EACV;;;;IAXmBH,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAAS,WAAA,qBAAAC,UAAA,CAAAC,KAAA,CAAwC;IAChEX,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAY,kBAAA,MAAAF,UAAA,CAAAG,IAAA,MACF;IACgBb,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,iBAAA,CAAAK,UAAA,CAAAI,KAAA,CAAmB;IAGhCd,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAK,iBAAA,CAAAK,UAAA,CAAAK,WAAA,CAAyB;IAEAf,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAgB,UAAA,YAAAN,UAAA,CAAAO,IAAA,CAAe;;;;;IAY3CjB,EAFJ,CAAAC,cAAA,mBAAuD,sBACpC,mBACW;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACpDH,EAAA,CAAAC,cAAA,qBAAgB;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAChDH,EAAA,CAAAC,cAAA,wBAAmB;IAAAD,EAAA,CAAAE,MAAA,GAAe;IACpCF,EADoC,CAAAG,YAAA,EAAoB,EACtC;IAEhBH,EADF,CAAAC,cAAA,uBAAkB,QACb;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAC3BF,EAD2B,CAAAG,YAAA,EAAI,EACZ;IAGfH,EAFJ,CAAAC,cAAA,wBAAkB,kBAC+B,gBACnC;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChCH,EAAA,CAAAE,MAAA,uBACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACQ,EACV;;;;IAbmBH,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAK,iBAAA,CAAAa,OAAA,CAAAL,IAAA,CAAe;IACzBb,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAK,iBAAA,CAAAa,OAAA,CAAAC,IAAA,CAAe;IACZnB,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAK,iBAAA,CAAAa,OAAA,CAAAE,IAAA,CAAe;IAG/BpB,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAK,iBAAA,CAAAa,OAAA,CAAAH,WAAA,CAAsB;IAGNf,EAAA,CAAAI,SAAA,GAA2B;IAA3BJ,EAAA,CAAAgB,UAAA,eAAAE,OAAA,CAAAG,OAAA,CAA2B;;;;;IAuB1CrB,EAAA,CAAAC,cAAA,eAA0C;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAtBH,EAAA,CAAAI,SAAA,EAAe;IAAfJ,EAAA,CAAAK,iBAAA,CAAAiB,OAAA,CAAAC,IAAA,CAAe;;;;;IAJ3DvB,EADF,CAAAC,cAAA,cAAsE,cAC3C;IAAAD,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAExCH,EADF,CAAAC,cAAA,cAA0B,SACpB;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC7BH,EAAA,CAAAO,UAAA,IAAAiB,wCAAA,mBAA0C;IAE9CxB,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IANqBH,EAAA,CAAAI,SAAA,GAAW;IAAXJ,EAAA,CAAAK,iBAAA,CAAAoB,IAAA,KAAW;IAE9BzB,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAiB,OAAA,CAAAR,KAAA,CAAgB;IACjBd,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAK,iBAAA,CAAAiB,OAAA,CAAAP,WAAA,CAAsB;IACAf,EAAA,CAAAI,SAAA,EAAe;IAAfJ,EAAA,CAAAgB,UAAA,SAAAM,OAAA,CAAAC,IAAA,CAAe;;;AAqR1D,OAAM,MAAOG,iBAAiB;EAhX9BC,YAAA;IAiXE,KAAAC,QAAQ,GAAG,CACT;MACEd,KAAK,EAAE,yBAAyB;MAChCC,WAAW,EAAE,6HAA6H;MAC1IF,IAAI,EAAE,WAAW;MACjBF,KAAK,EAAE,SAAS;MAChBM,IAAI,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU;KAC1C,EACD;MACEH,KAAK,EAAE,kBAAkB;MACzBC,WAAW,EAAE,2HAA2H;MACxIF,IAAI,EAAE,kBAAkB;MACxBF,KAAK,EAAE,SAAS;MAChBM,IAAI,EAAE,CAAC,SAAS,EAAE,QAAQ,EAAE,MAAM;KACnC,EACD;MACEH,KAAK,EAAE,sBAAsB;MAC7BC,WAAW,EAAE,+GAA+G;MAC5HF,IAAI,EAAE,eAAe;MACrBF,KAAK,EAAE,SAAS;MAChBM,IAAI,EAAE,CAAC,WAAW,EAAE,YAAY,EAAE,QAAQ;KAC3C,EACD;MACEH,KAAK,EAAE,uBAAuB;MAC9BC,WAAW,EAAE,wGAAwG;MACrHF,IAAI,EAAE,YAAY;MAClBF,KAAK,EAAE,SAAS;MAChBM,IAAI,EAAE,CAAC,SAAS,EAAE,WAAW,EAAE,KAAK;KACrC,CACF;IAED,KAAAY,KAAK,GAAG,CACN;MACEV,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE,qBAAqB;MAC3BL,WAAW,EAAE,wFAAwF;MACrGF,IAAI,EAAE,WAAW;MACjBQ,OAAO,EAAE;KACV,EACD;MACEF,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,cAAc;MACpBL,WAAW,EAAE,wFAAwF;MACrGF,IAAI,EAAE,UAAU;MAChBQ,OAAO,EAAE;KACV,EACD;MACEF,IAAI,EAAE,mBAAmB;MACzBC,IAAI,EAAE,iBAAiB;MACvBL,WAAW,EAAE,sFAAsF;MACnGF,IAAI,EAAE,WAAW;MACjBQ,OAAO,EAAE;KACV,EACD;MACEF,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAE,iBAAiB;MACvBL,WAAW,EAAE,mFAAmF;MAChGF,IAAI,EAAE,KAAK;MACXQ,OAAO,EAAE;KACV,CACF;IAED,KAAAS,eAAe,GAAG,CAChB;MACEhB,KAAK,EAAE,kBAAkB;MACzBC,WAAW,EAAE,qCAAqC;MAClDQ,IAAI,EAAE;KACP,EACD;MACET,KAAK,EAAE,eAAe;MACtBC,WAAW,EAAE,8BAA8B;MAC3CQ,IAAI,EAAE;KACP,EACD;MACET,KAAK,EAAE,gBAAgB;MACvBC,WAAW,EAAE,uCAAuC;MACpDQ,IAAI,EAAE;KACP,EACD;MACET,KAAK,EAAE,gBAAgB;MACvBC,WAAW,EAAE,6DAA6D;MAC1EQ,IAAI,EAAE;KACP,CACF;;;;uCApFUG,iBAAiB;IAAA;EAAA;;;YAAjBA,iBAAiB;MAAAK,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAhWlBrC,EAJR,CAAAC,cAAA,aAAgC,aACJ,aACE,YACD,kBACO;UAAAD,EAAA,CAAAE,MAAA,aAAM;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC7CH,EAAA,CAAAE,MAAA,0CACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,WAAyB;UACvBD,EAAA,CAAAE,MAAA,sGACF;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAGAH,EAFJ,CAAAC,cAAA,aAA0B,iBACoD,gBAChE;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC/BH,EAAA,CAAAE,MAAA,qBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UAEPH,EADF,CAAAC,cAAA,iBAA2D,gBAC/C;UAAAD,EAAA,CAAAE,MAAA,WAAG;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACxBH,EAAA,CAAAE,MAAA,uBACF;UAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;UAGJH,EADF,CAAAC,cAAA,cAA8B,UACxB;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACrBH,EAAA,CAAAC,cAAA,eAA2B;UACzBD,EAAA,CAAAO,UAAA,KAAAgC,sCAAA,wBAAgE;UAepEvC,EADE,CAAAG,YAAA,EAAM,EACF;UAGJH,EADF,CAAAC,cAAA,eAA2B,UACrB;UAAAD,EAAA,CAAAE,MAAA,uBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxBH,EAAA,CAAAC,cAAA,eAAwB;UACtBD,EAAA,CAAAO,UAAA,KAAAiC,sCAAA,wBAAuD;UAiB3DxC,EADE,CAAAG,YAAA,EAAM,EACF;UAKAH,EAHN,CAAAC,cAAA,eAAiC,oBACI,uBAChB,oBACW;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAClDH,EAAA,CAAAC,cAAA,sBAAgB;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAiB;UAC5CH,EAAA,CAAAC,cAAA,yBAAmB;UAAAD,EAAA,CAAAE,MAAA,kCAA0B;UAC/CF,EAD+C,CAAAG,YAAA,EAAoB,EACjD;UAEhBH,EADF,CAAAC,cAAA,wBAAkB,eACe;UAC7BD,EAAA,CAAAO,UAAA,KAAAkC,iCAAA,kBAAsE;UAS1EzC,EADE,CAAAG,YAAA,EAAM,EACW;UAGfH,EAFJ,CAAAC,cAAA,wBAAkB,iBAC4D,gBAChE;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAClCH,EAAA,CAAAE,MAAA,iCACF;UAIRF,EAJQ,CAAAG,YAAA,EAAS,EACQ,EACV,EACP,EACF;;;UAlEmDH,EAAA,CAAAI,SAAA,IAAW;UAAXJ,EAAA,CAAAgB,UAAA,YAAAsB,GAAA,CAAAV,QAAA,CAAW;UAoBjB5B,EAAA,CAAAI,SAAA,GAAQ;UAARJ,EAAA,CAAAgB,UAAA,YAAAsB,GAAA,CAAAT,KAAA,CAAQ;UA4Bd7B,EAAA,CAAAI,SAAA,IAAoB;UAApBJ,EAAA,CAAAgB,UAAA,YAAAsB,GAAA,CAAAR,eAAA,CAAoB;;;qBAlFjEpC,YAAY,EAAAgD,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EACZjD,YAAY,EAAAkD,EAAA,CAAAC,UAAA,EACZlD,aAAa,EAAAmD,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,cAAA,EAAAF,EAAA,CAAAG,aAAA,EAAAH,EAAA,CAAAI,cAAA,EAAAJ,EAAA,CAAAK,aAAA,EAAAL,EAAA,CAAAM,eAAA,EAAAN,EAAA,CAAAO,YAAA,EACbzD,aAAa,EAAA0D,EAAA,CAAAC,OAAA,EACb1D,eAAe,EAAA2D,EAAA,CAAAC,SAAA,EACf3D,cAAc,EAAA4D,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,cAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}