# WebSocket Auto-Reconnect Feature

The SPT VS Code extension now includes an intelligent auto-reconnect feature that provides a better user experience when the connection to the SPT server is lost.

## How It Works

### Initial Connection Attempts
1. **Automatic Retry**: The extension automatically attempts to reconnect up to 5 times with increasing delays
2. **User Consent**: After max attempts are reached, the extension asks for user permission to continue
3. **Smart Reconnection**: Based on user preference, the extension can automatically reconnect whenever the server becomes available

### User Consent Dialog

When the connection fails after multiple attempts, users see a dialog with three options:

#### 1. **Enable Auto-Reconnect**
- Automatically reconnects whenever the SPT server becomes available
- Saves the preference globally for future sessions
- Shows "(Auto)" indicator in the status bar
- Attempts reconnection every 10 seconds in the background

#### 2. **Try Again**
- Attempts to reconnect once more immediately
- Resets the retry counter for another round of automatic attempts
- Good for temporary network issues

#### 3. **Stay Disconnected**
- Disables automatic reconnection
- Provides manual reconnection via status bar click
- Offers quick access to settings to enable auto-reconnect later

## Status Bar Indicators

The status bar shows different states with helpful tooltips:

### Connection States
- **🔌 SPT** - Connected to server
- **⚡ SPT** - Connecting to server (spinning animation)
- **🔌 SPT** - Disconnected (warning background)
- **⚠️ SPT** - Connection error (error background)
- **❌ SPT** - Connection failed (error background)

### Auto-Reconnect Indicators
- **(Auto)** suffix shows when auto-reconnect is enabled
- Tooltips provide context-specific information
- Clickable status bar provides quick actions

## Configuration

### VS Code Settings

```json
{
  "spt.autoReconnect": false  // Default: disabled
}
```

### Setting via UI
1. Open VS Code Settings (`Ctrl+,`)
2. Search for "spt auto"
3. Toggle "Auto Reconnect" option

### Setting via Command Palette
1. Open Command Palette (`Ctrl+Shift+P`)
2. Type "SPT: Toggle Auto-Reconnect"
3. Execute the command

## Commands

### Available Commands
- `spt.reconnectWebSocket` - Manually reconnect to SPT server
- `spt.toggleAutoReconnect` - Toggle auto-reconnect on/off

### Command Palette Access
- **SPT: Reconnect to SPT Server** - Manual reconnection
- **SPT: Toggle Auto-Reconnect** - Toggle auto-reconnect preference

## User Experience Scenarios

### Scenario 1: Server Restart
1. SPT server is restarted
2. Extension detects connection loss
3. Attempts automatic reconnection (5 tries)
4. If auto-reconnect is enabled: continues trying every 10 seconds
5. If auto-reconnect is disabled: shows consent dialog

### Scenario 2: Network Issues
1. Temporary network interruption occurs
2. Extension attempts reconnection with exponential backoff
3. Usually reconnects automatically within the initial 5 attempts
4. No user intervention needed for brief outages

### Scenario 3: First-Time Connection Loss
1. Connection is lost for the first time
2. Extension tries 5 automatic reconnections
3. Shows user-friendly dialog explaining options
4. User can choose their preferred behavior going forward

## Benefits

### For Users
- **Seamless Experience**: Automatic reconnection when possible
- **User Control**: Choice in how reconnection is handled
- **Clear Feedback**: Status bar shows connection state and auto-reconnect status
- **Flexible Options**: Can change preferences at any time

### For Developers
- **Reduced Support**: Fewer "connection lost" issues
- **Better Adoption**: Users stay connected to SPT features
- **Clear Status**: Easy to see connection state at a glance

## Technical Implementation

### Reconnection Strategy
1. **Initial Attempts**: 5 tries with increasing delays (5s, 10s, 15s, 20s, 25s)
2. **User Consent**: Dialog after max attempts reached
3. **Continuous Mode**: Every 10 seconds when auto-reconnect is enabled
4. **Smart Detection**: Checks connection state before attempting

### State Management
- Tracks reconnection attempts and user preferences
- Persists auto-reconnect setting globally
- Updates status bar based on current state
- Handles configuration changes dynamically

### Error Handling
- Graceful degradation when server is unavailable
- Clear error messages and recovery options
- No infinite loops or resource leaks
- Proper cleanup on extension deactivation

## Future Enhancements

Potential improvements for future versions:
- Configurable reconnection intervals
- Connection health monitoring
- Offline mode detection
- Advanced retry strategies
- Connection quality indicators
