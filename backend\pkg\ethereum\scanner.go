package ethereum

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"time"

	"blockchain-spt/backend/pkg/config"
	"blockchain-spt/backend/pkg/models"

	"github.com/sirupsen/logrus"
)

// Scanner represents the Ethereum security scanner
type Scanner struct {
	config                *config.Config
	logger                *logrus.Logger
	vulnerabilityDetector *VulnerabilityDetector
	gasAnalyzer           *GasAnalyzer
	astParser             *ASTParser
}

// NewScanner creates a new Ethereum scanner instance
func NewScanner(cfg *config.Config) (*Scanner, error) {
	return &Scanner{
		config:                cfg,
		logger:                logrus.New(),
		vulnerabilityDetector: NewVulnerabilityDetector(),
		gasAnalyzer:           NewGasAnalyzer(),
		astParser:             NewASTParser(),
	}, nil
}

// ScanProject scans an entire project for Ethereum-specific security issues
func (s *Scanner) ScanProject(ctx context.Context, projectPath string) ([]models.SecurityIssue, error) {
	s.logger.Infof("Starting Ethereum security scan for project: %s", projectPath)

	var allIssues []models.SecurityIssue

	// Find all Solidity files
	solidityFiles, err := s.findSolidityFiles(projectPath)
	if err != nil {
		return nil, fmt.Errorf("failed to find Solidity files: %w", err)
	}

	// Scan each Solidity file
	for _, file := range solidityFiles {
		issues, err := s.ScanFile(ctx, file)
		if err != nil {
			s.logger.Errorf("Failed to scan file %s: %v", file, err)
			continue
		}
		allIssues = append(allIssues, issues...)
	}

	s.logger.Infof("Ethereum scan completed, found %d issues", len(allIssues))
	return allIssues, nil
}

// ScanFile scans a specific file for Ethereum security issues
func (s *Scanner) ScanFile(ctx context.Context, filePath string) ([]models.SecurityIssue, error) {
	if !strings.HasSuffix(filePath, ".sol") {
		return nil, nil // Not a Solidity file
	}

	content, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read file: %w", err)
	}

	var issues []models.SecurityIssue

	// Use advanced vulnerability detection
	vulnIssues, err := s.vulnerabilityDetector.DetectVulnerabilities(filePath, string(content))
	if err != nil {
		s.logger.Warnf("Advanced vulnerability detection failed, using fallback: %v", err)
		// Fallback to basic checks
		issues = append(issues, s.checkReentrancy(filePath, string(content))...)
		issues = append(issues, s.checkIntegerOverflow(filePath, string(content))...)
		issues = append(issues, s.checkUncheckedCalls(filePath, string(content))...)
		issues = append(issues, s.checkAccessControl(filePath, string(content))...)
		issues = append(issues, s.checkDeprecatedFunctions(filePath, string(content))...)
	} else {
		issues = append(issues, vulnIssues...)
	}

	// Parse AST for gas analysis
	ast, err := s.astParser.ParseContract(filePath)
	if err != nil {
		s.logger.Warnf("AST parsing failed for gas analysis: %v", err)
		// Fallback to basic gas optimization checks
		issues = append(issues, s.checkGasOptimization(filePath, string(content))...)
	} else {
		// Use advanced gas analysis
		gasIssues, err := s.gasAnalyzer.AnalyzeGasOptimizations(filePath, string(content), ast)
		if err != nil {
			s.logger.Warnf("Gas analysis failed: %v", err)
			// Fallback to basic gas optimization checks
			issues = append(issues, s.checkGasOptimization(filePath, string(content))...)
		} else {
			issues = append(issues, gasIssues...)
		}
	}

	return issues, nil
}

// checkReentrancy checks for reentrancy vulnerabilities
func (s *Scanner) checkReentrancy(filePath, content string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	// Pattern for external calls followed by state changes
	externalCallPattern := regexp.MustCompile(`(?i)(\.call\(|\.send\(|\.transfer\()`)
	stateChangePattern := regexp.MustCompile(`(?i)(balance\s*=|balances\[.*\]\s*=|\w+\s*=)`)

	lines := strings.Split(content, "\n")
	for i, line := range lines {
		if externalCallPattern.MatchString(line) {
			// Check subsequent lines for state changes
			for j := i + 1; j < len(lines) && j < i+10; j++ {
				if stateChangePattern.MatchString(lines[j]) {
					issues = append(issues, models.SecurityIssue{
						ID:          fmt.Sprintf("reentrancy_%s_%d", filepath.Base(filePath), i+1),
						Type:        "reentrancy",
						Severity:    "high",
						Title:       "Potential Reentrancy Vulnerability",
						Description: "External call followed by state change may be vulnerable to reentrancy attacks",
						File:        filePath,
						Line:        i + 1,
						Code:        strings.TrimSpace(line),
						Chain:       "ethereum",
						Category:    "smart_contract",
						CWE:         "CWE-362",
						OWASP:       "A06:2021 – Vulnerable and Outdated Components",
						Suggestion:  "Use the checks-effects-interactions pattern or reentrancy guards",
						References:  []string{"https://consensys.github.io/smart-contract-best-practices/attacks/reentrancy/"},
						CreatedAt:   time.Now(),
					})
					break
				}
			}
		}
	}

	return issues
}

// checkIntegerOverflow checks for integer overflow vulnerabilities
func (s *Scanner) checkIntegerOverflow(filePath, content string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	// Pattern for arithmetic operations without SafeMath
	arithmeticPattern := regexp.MustCompile(`(?i)(\w+\s*[\+\-\*\/]\s*\w+|\w+\s*[\+\-]\+|\w+\s*\*\*\s*\w+)`)
	safeMathPattern := regexp.MustCompile(`(?i)(SafeMath|using\s+SafeMath)`)

	// Check if SafeMath is used
	usesSafeMath := safeMathPattern.MatchString(content)

	if !usesSafeMath {
		lines := strings.Split(content, "\n")
		for i, line := range lines {
			if arithmeticPattern.MatchString(line) && !strings.Contains(line, "//") {
				issues = append(issues, models.SecurityIssue{
					ID:          fmt.Sprintf("overflow_%s_%d", filepath.Base(filePath), i+1),
					Type:        "integer_overflow",
					Severity:    "medium",
					Title:       "Potential Integer Overflow",
					Description: "Arithmetic operation without overflow protection",
					File:        filePath,
					Line:        i + 1,
					Code:        strings.TrimSpace(line),
					Chain:       "ethereum",
					Category:    "smart_contract",
					CWE:         "CWE-190",
					Suggestion:  "Use SafeMath library or Solidity 0.8+ built-in overflow protection",
					References:  []string{"https://docs.openzeppelin.com/contracts/2.x/api/math#SafeMath"},
					CreatedAt:   time.Now(),
				})
			}
		}
	}

	return issues
}

// checkUncheckedCalls checks for unchecked external calls
func (s *Scanner) checkUncheckedCalls(filePath, content string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	// Pattern for external calls without return value checking
	uncheckedCallPattern := regexp.MustCompile(`(?i)(\w+\.call\(|\w+\.send\()`)

	lines := strings.Split(content, "\n")
	for i, line := range lines {
		if uncheckedCallPattern.MatchString(line) && !strings.Contains(line, "require(") && !strings.Contains(line, "assert(") {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("unchecked_call_%s_%d", filepath.Base(filePath), i+1),
				Type:        "unchecked_call",
				Severity:    "medium",
				Title:       "Unchecked External Call",
				Description: "External call return value is not checked",
				File:        filePath,
				Line:        i + 1,
				Code:        strings.TrimSpace(line),
				Chain:       "ethereum",
				Category:    "smart_contract",
				CWE:         "CWE-252",
				Suggestion:  "Check the return value of external calls using require() or assert()",
				References:  []string{"https://consensys.github.io/smart-contract-best-practices/recommendations/#handle-errors-in-external-calls"},
				CreatedAt:   time.Now(),
			})
		}
	}

	return issues
}

// checkAccessControl checks for access control issues
func (s *Scanner) checkAccessControl(filePath, content string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	// Pattern for functions without access modifiers
	functionPattern := regexp.MustCompile(`(?i)function\s+\w+\s*\([^)]*\)\s*(?:public|external)`)
	modifierPattern := regexp.MustCompile(`(?i)(onlyOwner|onlyAdmin|require\(msg\.sender)`)

	lines := strings.Split(content, "\n")
	for i, line := range lines {
		if functionPattern.MatchString(line) && !modifierPattern.MatchString(line) {
			// Check if it's a state-changing function
			if strings.Contains(line, "public") || strings.Contains(line, "external") {
				issues = append(issues, models.SecurityIssue{
					ID:          fmt.Sprintf("access_control_%s_%d", filepath.Base(filePath), i+1),
					Type:        "access_control",
					Severity:    "medium",
					Title:       "Missing Access Control",
					Description: "Public/external function without access control",
					File:        filePath,
					Line:        i + 1,
					Code:        strings.TrimSpace(line),
					Chain:       "ethereum",
					Category:    "smart_contract",
					CWE:         "CWE-284",
					Suggestion:  "Add appropriate access control modifiers (onlyOwner, onlyAdmin, etc.)",
					References:  []string{"https://docs.openzeppelin.com/contracts/4.x/access-control"},
					CreatedAt:   time.Now(),
				})
			}
		}
	}

	return issues
}

// checkGasOptimization checks for gas optimization opportunities
func (s *Scanner) checkGasOptimization(filePath, content string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	// Pattern for inefficient storage operations
	storagePattern := regexp.MustCompile(`(?i)(storage\s+\w+\s*=|sstore)`)

	lines := strings.Split(content, "\n")
	for i, line := range lines {
		if storagePattern.MatchString(line) {
			issues = append(issues, models.SecurityIssue{
				ID:          fmt.Sprintf("gas_optimization_%s_%d", filepath.Base(filePath), i+1),
				Type:        "gas_optimization",
				Severity:    "low",
				Title:       "Gas Optimization Opportunity",
				Description: "Potential gas optimization in storage operations",
				File:        filePath,
				Line:        i + 1,
				Code:        strings.TrimSpace(line),
				Chain:       "ethereum",
				Category:    "smart_contract",
				Suggestion:  "Consider using memory instead of storage where possible",
				References:  []string{"https://docs.soliditylang.org/en/latest/internals/layout_in_storage.html"},
				CreatedAt:   time.Now(),
			})
		}
	}

	return issues
}

// checkDeprecatedFunctions checks for deprecated Solidity functions
func (s *Scanner) checkDeprecatedFunctions(filePath, content string) []models.SecurityIssue {
	var issues []models.SecurityIssue

	deprecatedFunctions := []string{
		"suicide", "sha3", "block.blockhash", "msg.gas", "throw",
	}

	lines := strings.Split(content, "\n")
	for i, line := range lines {
		for _, deprecated := range deprecatedFunctions {
			if strings.Contains(line, deprecated) {
				issues = append(issues, models.SecurityIssue{
					ID:          fmt.Sprintf("deprecated_%s_%d", filepath.Base(filePath), i+1),
					Type:        "deprecated_function",
					Severity:    "medium",
					Title:       "Deprecated Function Usage",
					Description: fmt.Sprintf("Usage of deprecated function: %s", deprecated),
					File:        filePath,
					Line:        i + 1,
					Code:        strings.TrimSpace(line),
					Chain:       "ethereum",
					Category:    "smart_contract",
					Suggestion:  "Replace with modern Solidity equivalents",
					References:  []string{"https://docs.soliditylang.org/en/latest/"},
					CreatedAt:   time.Now(),
				})
			}
		}
	}

	return issues
}

// findSolidityFiles finds all Solidity files in the project
func (s *Scanner) findSolidityFiles(projectPath string) ([]string, error) {
	var files []string

	err := filepath.Walk(projectPath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// Skip entire directories that should be excluded
		if info.IsDir() {
			for _, excludePath := range s.config.Scanning.Paths.Exclude {
				if info.Name() == excludePath || strings.Contains(path, excludePath) {
					s.logger.Debugf("Skipping directory: %s", path)
					return filepath.SkipDir
				}
			}

			// Skip scanner source code directories to avoid false positives
			if s.isScannerSourcePath(path) {
				s.logger.Debugf("Skipping scanner source directory: %s", path)
				return filepath.SkipDir
			}

			return nil
		}

		// Skip hidden files
		if strings.HasPrefix(info.Name(), ".") {
			return nil
		}

		if strings.HasSuffix(path, ".sol") {
			// Check if path should be excluded
			for _, excludePath := range s.config.Scanning.Paths.Exclude {
				if strings.Contains(path, excludePath) {
					return nil
				}
			}
			files = append(files, path)
		}

		return nil
	})

	return files, err
}

// isScannerSourcePath checks if a path is part of the scanner source code
func (s *Scanner) isScannerSourcePath(path string) bool {
	scannerPaths := []string{
		"backend/pkg/bitcoin",
		"backend/pkg/ethereum",
		"backend/pkg/security",
		"backend/pkg/dependencies",
		"backend/pkg/scanner",
		"backend/cmd",
		"backend/internal",
	}

	for _, scannerPath := range scannerPaths {
		if strings.Contains(path, scannerPath) {
			return true
		}
	}

	return false
}
