{"version": "1.0.0", "project": {"name": "blockchain-security-project", "type": "mixed", "chains": ["ethereum", "bitcoin"]}, "database": {"type": "postgres", "host": "localhost", "port": 5432, "database": "sptdb", "username": "postgres", "password": "123456_Az", "ssl_mode": "disable", "max_connections": 25, "max_idle_connections": 5, "connection_timeout": 30}, "scanning": {"paths": {"include": ["./contracts", "./src", "./scripts", "./test"], "exclude": ["node_modules", "build", "dist", ".git", "coverage", "vendor", "target", "__pycache__", ".pytest_cache", ".idea", ".vscode", ".vs", "bin", "obj", "packages", ".next", ".nuxt", "tmp", "temp", "logs"]}, "fileTypes": ["*.sol", "*.js", "*.ts", "*.go", "*.py", "*.json", "*.env*", "*.yml", "*.yaml"]}, "security": {"level": "strict", "rules": {"keyLeakDetection": {"enabled": true, "patterns": ["privateKey", "mnemonic", "seed", "secret", "password"]}, "smartContractAudit": {"enabled": true, "ethereum": {"reentrancy": true, "integerOverflow": true, "uncheckedCalls": true, "gasOptimization": true, "accessControl": true}, "bitcoin": {"scriptValidation": true, "multisigSecurity": true, "utxoPatterns": true}}, "dependencyScanning": {"enabled": true, "sources": ["osv", "cve", "npm-audit"], "severity": ["critical", "high", "medium"]}, "environmentSecurity": {"enabled": true, "checkDockerfiles": true, "checkCICD": true, "checkEnvFiles": true}}}, "reporting": {"format": "markdown", "outputPath": "./security-reports", "includeTimestamp": true, "sections": ["summary", "vulnerabilities", "recommendations", "checklist"]}, "integrations": {"vscode": {"enabled": true, "realTimeHighlighting": true, "inlineSuggestions": true}, "git": {"preCommitHooks": true, "scanHistory": false}, "ci": {"failOnCritical": true, "reportToSlack": false, "reportToDiscord": false}}, "plugins": {"ethereum": {"enabled": true, "slitherIntegration": true, "mythrilIntegration": false}, "bitcoin": {"enabled": true, "customScriptAnalysis": true}}}